import useSWR from 'swr';

// Get patients list with pagination and filtering
export const GetPatients = (params: string) => {
  const qs = new URLSearchParams(params);

  const { data, error, isLoading, mutate } = useSWR(
    `/crm/patients?${qs.toString()}`
  );

  return {
    patients: data,
    patientsLoading: isLoading,
    patientsError: error,
    mutate: mutate,
  };
};

// Get a single patient by ID
export const GetPatientById = (patientId: number) => {
  const { data, error, isLoading, mutate } = useSWR(
    `/crm/patients/${patientId}`
  );

  return {
    patient: data,
    patientLoading: isLoading,
    patientError: error,
    mutate: mutate,
  };
};

// Get patient medical records
export const GetPatientMedicalRecords = (patientId: number) => {
  const { data, error, isLoading, mutate } = useSWR(
    `/crm/patients/${patientId}/medical-records`
  );

  return {
    medicalRecords: data,
    medicalRecordsLoading: isLoading,
    medicalRecordsError: error,
    mutate: mutate,
  };
};

// Get patient appointments with pagination and filtering
export const GetPatientAppointments = (params: string) => {
  const qs = new URLSearchParams(params);

  const { data, error, isLoading, mutate } = useSWR(
    `/crm/appointments?${qs.toString()}`
  );

  return {
    appointments: data,
    appointmentsLoading: isLoading,
    appointmentsError: error,
    mutate: mutate,
  };
};

// Get appointments for a specific patient
export const GetAppointmentsByPatientId = (
  patientId: number,
  params: string = ''
) => {
  const qs = new URLSearchParams(params);

  const { data, error, isLoading, mutate } = useSWR(
    `/crm/patients/${patientId}/appointments?${qs.toString()}`
  );

  return {
    appointments: data,
    appointmentsLoading: isLoading,
    appointmentsError: error,
    mutate: mutate,
  };
};

// Get patient interactions with pagination and filtering
export const GetPatientInteractions = (params: string) => {
  const qs = new URLSearchParams(params);

  const { data, error, isLoading, mutate } = useSWR(
    `/crm/patient-interactions?${qs.toString()}`
  );

  return {
    interactions: data,
    interactionsLoading: isLoading,
    interactionsError: error,
    mutate: mutate,
  };
};

// Get interactions for a specific patient
export const GetInteractionsByPatientId = (
  patientId: number,
  params: string = ''
) => {
  const qs = new URLSearchParams(params);

  const { data, error, isLoading, mutate } = useSWR(
    `/crm/patients/${patientId}/interactions?${qs.toString()}`
  );

  return {
    interactions: data,
    interactionsLoading: isLoading,
    interactionsError: error,
    mutate: mutate,
  };
};

// Get all medical records with pagination and filtering
export const GetAllMedicalRecords = (params: string) => {
  const qs = new URLSearchParams(params);

  const { data, error, isLoading, mutate } = useSWR(
    `/crm/medical-records?${qs.toString()}`
  );

  return {
    medicalRecords: data,
    medicalRecordsLoading: isLoading,
    medicalRecordsError: error,
    mutate: mutate,
  };
};

// Get hospital departments
export const GetDepartments = () => {
  const { data, error, isLoading, mutate } = useSWR('/crm/departments');

  return {
    departments: data,
    departmentsLoading: isLoading,
    departmentsError: error,
    mutate: mutate,
  };
};

// Get doctors list
export const GetDoctors = (departmentId?: number) => {
  const params = departmentId ? `?departmentId=${departmentId}` : '';
  const { data, error, isLoading, mutate } = useSWR(`/crm/doctors${params}`);

  return {
    doctors: data,
    doctorsLoading: isLoading,
    doctorsError: error,
    mutate: mutate,
  };
};

// Get hospital analytics data
export const GetHospitalAnalytics = (params?: string) => {
  const qs = params ? new URLSearchParams(params) : new URLSearchParams();

  const { data, error, isLoading } = useSWR(
    `/analytic/hospital?${qs.toString()}`
  );

  return {
    hospitalStats: data,
    hospitalStatsLoading: isLoading,
    hospitalStatsError: error,
  };
};

// For backward compatibility with old CRM components
export const GetCustomers = GetPatients;

// Get customer interactions with pagination and filtering (for backward compatibility)
export const GetCustomerInteractions = (params: string) => {
  const qs = new URLSearchParams(params);

  const { data, error, isLoading, mutate } = useSWR(
    `/crm/appointments?${qs.toString()}`
  );

  return {
    interactions: data,
    interactionsLoading: isLoading,
    interactionsError: error,
    mutate: mutate,
  };
};

// Get interactions for a specific customer (for backward compatibility)
export const GetInteractionsByCustomerId = (
  customerId: number,
  params: string = ''
) => {
  return GetAppointmentsByPatientId(customerId, params);
};

// For backward compatibility with old CRM analytics components
export const GetCrmAnalytics = (params?: string) => {
  const { hospitalStats, hospitalStatsLoading, hospitalStatsError } =
    GetHospitalAnalytics(params);

  // Transform the hospital analytics data to match the old CRM analytics structure
  return {
    crmStats: hospitalStats
      ? {
          data: {
            customerStats: {
              totalCustomers:
                hospitalStats.data?.patientStats?.totalPatients || 0,
              newCustomersThisMonth:
                hospitalStats.data?.patientStats?.newPatientsThisMonth || 0,
              activeCustomers:
                hospitalStats.data?.patientStats?.activePatients || 0,
              inactiveCustomers:
                hospitalStats.data?.patientStats?.totalPatients -
                (hospitalStats.data?.patientStats?.activePatients || 0),
            },
            interactionStats: {
              totalInteractions:
                hospitalStats.data?.appointmentStats?.totalAppointments || 0,
              pendingInteractions:
                hospitalStats.data?.appointmentStats?.scheduledAppointments ||
                0,
              completedInteractions:
                hospitalStats.data?.appointmentStats?.completedAppointments ||
                0,
              interactionsByType:
                hospitalStats.data?.appointmentStats?.appointmentsByType || [],
            },
            revenueStats: {
              totalRevenue: hospitalStats.data?.revenueStats?.totalRevenue || 0,
              averageRevenuePerCustomer:
                hospitalStats.data?.revenueStats?.averageRevenuePerPatient || 0,
              revenueByMonth:
                hospitalStats.data?.revenueStats?.revenueByMonth || [],
            },
          },
        }
      : null,
    crmStatsLoading: hospitalStatsLoading,
    crmStatsError: hospitalStatsError,
  };
};
