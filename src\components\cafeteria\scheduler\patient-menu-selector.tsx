// 'use client';

// import { Card, CardContent, CardH<PERSON>er, CardTitle } from '@/components/ui/card';
// import { Button } from '@/components/ui/button';
// import { Badge } from '@/components/ui/badge';
// import { Checkbox } from '@/components/ui/checkbox';
// import { Coffee, Sun, Moon, Plus, Minus } from 'lucide-react';
// import { LoadingState } from '@/components/common/dataState';
// import { numberFormat } from '@/lib/utils';

// interface PatientMenuSelectorProps {
//   weeklyMenus: any;
//   selectedDate: string;
//   dayName: string;
//   orders: any;
//   setOrders: (orders: any) => void;
//   loading: boolean;
// }

// const MEAL_ICONS = {
//   Breakfast: Coffee,
//   Lunch: Sun,
//   Dinner: Moon,
// };

// export default function PatientMenuSelector({
//   weeklyMenus,
//   selectedDate,
//   dayName,
//   orders,
//   setOrders,
//   loading,
// }: PatientMenuSelectorProps) {
//   const dayMenus = weeklyMenus?.[dayName] || {};

//   const toggleMenuItem = (mealType: string, menu: any) => {
//     setOrders((prev: any) => {
//       const currentMeals = prev[mealType] || [];
//       const exists = currentMeals.find((m: any) => m.id === menu.id);

//       if (exists) {
//         return {
//           ...prev,
//           [mealType]: currentMeals.filter((m: any) => m.id !== menu.id),
//         };
//       } else {
//         return {
//           ...prev,
//           [mealType]: [...currentMeals, menu],
//         };
//       }
//     });
//   };

//   const isSelected = (mealType: string, menuId: string) => {
//     return orders[mealType]?.some((m: any) => m.id === menuId) || false;
//   };

//   if (loading) {
//     return <LoadingState />;
//   }

//   return (
//     <div className="space-y-6">
//       <div className="text-center">
//         <h2 className="text-2xl font-semibold mb-2">Menu for {dayName}</h2>
//         <p className="text-gray-600">
//           {new Date(selectedDate).toLocaleDateString()}
//         </p>
//       </div>

//       <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
//         {Object.entries(dayMenus).map(([mealType, menus]: [string, any]) => {
//           const Icon = MEAL_ICONS[mealType as keyof typeof MEAL_ICONS];

//           return (
//             <Card key={mealType} className="h-fit">
//               <CardHeader>
//                 <CardTitle className="flex items-center gap-2">
//                   <Icon className="h-5 w-5" />
//                   {mealType}
//                 </CardTitle>
//               </CardHeader>
//               <CardContent className="space-y-3">
//                 {menus && menus.length > 0 ? (
//                   menus.map((menu: any) => (
//                     <div
//                       key={menu.id}
//                       className={`border rounded-lg p-3 cursor-pointer transition-colors ${
//                         isSelected(mealType, menu.id)
//                           ? 'border-primary bg-primary/5'
//                           : 'border-gray-200 hover:border-gray-300'
//                       }`}
//                       onClick={() => toggleMenuItem(mealType, menu)}
//                     >
//                       <div className="flex items-start justify-between">
//                         <div className="flex-1">
//                           <div className="flex items-center gap-2 mb-1">
//                             <Checkbox
//                               checked={isSelected(mealType, menu.id)}
//                               onChange={() => toggleMenuItem(mealType, menu)}
//                             />
//                             <h4 className="font-medium">{menu.name}</h4>
//                           </div>
//                           <p className="text-sm text-gray-600 mb-2">
//                             {menu.description}
//                           </p>
//                           <div className="flex items-center justify-between">
//                             <Badge variant="outline">
//                               {menu.menuCategory?.name}
//                             </Badge>
//                             <span className="font-semibold text-primary">
//                               {numberFormat(menu.generalPrice)}
//                             </span>
//                           </div>
//                         </div>
//                       </div>
//                     </div>
//                   ))
//                 ) : (
//                   <p className="text-gray-500 text-center py-4">
//                     No menu items available
//                   </p>
//                 )}
//               </CardContent>
//             </Card>
//           );
//         })}
//       </div>

//       {Object.values(orders).some((meals: any[]) => meals.length > 0) && (
//         <Card>
//           <CardHeader>
//             <CardTitle>Your Selected Items</CardTitle>
//           </CardHeader>
//           <CardContent>
//             <div className="space-y-4">
//               {Object.entries(orders).map(
//                 ([mealType, meals]: [string, any[]]) => {
//                   if (meals.length === 0) return null;

//                   const Icon = MEAL_ICONS[mealType as keyof typeof MEAL_ICONS];

//                   return (
//                     <div key={mealType}>
//                       <h4 className="font-medium flex items-center gap-2 mb-2">
//                         <Icon className="h-4 w-4" />
//                         {mealType}
//                       </h4>
//                       <div className="space-y-2 ml-6">
//                         {meals.map((menu: any) => (
//                           <div
//                             key={menu.id}
//                             className="flex items-center justify-between"
//                           >
//                             <span>{menu.name}</span>
//                             <span className="font-medium">
//                               {numberFormat(menu.generalPrice)}
//                             </span>
//                           </div>
//                         ))}
//                       </div>
//                     </div>
//                   );
//                 }
//               )}
//               <div className="border-t pt-2">
//                 <div className="flex items-center justify-between font-semibold">
//                   <span>Total:</span>
//                   <span>
//                     {numberFormat(
//                       Object.values(orders)
//                         .flat()
//                         .reduce(
//                           (sum: number, menu: any) =>
//                             sum + (menu.generalPrice || 0),
//                           0
//                         )
//                     )}
//                   </span>
//                 </div>
//               </div>
//             </div>
//           </CardContent>
//         </Card>
//       )}
//     </div>
//   );
// }
