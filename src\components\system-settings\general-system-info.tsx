// 'use client';

// import React from 'react';
// import { GetSystemSettings } from '@/api/settings/data';
// import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
// import { Switch } from '@/components/ui/switch'; // Assuming you have a Switch component from your UI library
// import { Label } from '@/components/ui/label';
// import  {Button}  from '@/components/ui/button';
// import Button from '@/components/ui/button';

// // Define a type for the expected settings data structure from the API
// // This might live in a shared types file like src/lib/types/types.ts
// interface SystemSettingsData {
//   siteName?: string;
//   maintenanceMode?: boolean;
//   // Add other expected settings properties based on your API response
// }

// export default function GeneralSystemInfo() {
//   const { settings, settingsLoading, settingsError, mutate } = GetSystemSettings();

//   if (settingsLoading) {
//     return (
//       <div className="flex items-center justify-center h-40">
//         <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-t-2 border-primary"></div>
//       </div>
//     );
//   }

//   if (settingsError) {
//     return (
//       <Alert variant="destructive">
//         <AlertTitle>Error Loading Settings</AlertTitle>
//         <AlertDescription>
//           Failed to load system settings. Please try refreshing.
//         </AlertDescription>
//       </Alert>
//     );
//   }

//   // The actual settings object is expected to be within settings.data
//   const systemData = settings?.data as SystemSettingsData | undefined;

//   // Placeholder for an update function if you were to make settings editable
//   // const handleSettingUpdate = async (settingKey: string, value: any) => {
//   //   console.log(`Attempting to update ${settingKey} to ${value}`);
//   //   // Example: await myApi.patch('/admin/system-settings', { [settingKey]: value });
//   //   // mutate(); // Re-fetch data after update
//   // };

//   return (
//     <div className="space-y-6 p-6 border rounded-lg shadow-sm bg-card text-card-foreground">
//       <h3 className="text-xl font-semibold">General System Information</h3>

//       {systemData ? (
//         <div className="space-y-4">
//           <div>
//             <Label htmlFor="siteName" className="text-base">Site Name</Label>
//             <p id="siteName" className="text-md text-muted-foreground mt-1">
//               {systemData.siteName || 'N/A'}
//             </p>
//           </div>

//           <div className="flex items-center space-x-3">
//             <Switch
//               id="maintenanceMode"
//               checked={systemData.maintenanceMode || false}
//               // onCheckedChange={(checked) => handleSettingUpdate('maintenanceMode', checked)}
//               disabled // For display only in this example; enable and implement onCheckedChange for editing
//             />
//             <Label htmlFor="maintenanceMode" className="text-base">
//               Maintenance Mode
//             </Label>
//           </div>
//         </div>
//       ) : (
//         <p className="text-md text-muted-foreground">No system settings data available.</p>
//       )}

//       <Button variant="outline" onClick={() => mutate()}>
//         Refresh Settings
//       </Button>
//     </div>
//   );
// }
