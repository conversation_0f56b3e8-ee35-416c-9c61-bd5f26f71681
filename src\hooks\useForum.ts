'use client';

import { useEffect } from 'react';
import { useSnapshot } from 'valtio';
import {
  forumStore,
  UserPresence,
  TypingIndicator,
  MessageReaction,
} from '@/store/forumStore';
import websocketService from '@/services/websocket';

export function useForum() {
  const {
    userPresence,
    onlineUsers,
    typingIndicators,
    messageReactions,
    isConnected,
    connectionStatus,
    activeGroupId,
    activeRecipientId,
    unreadCounts,
  } = useSnapshot(forumStore);

  useEffect(() => {
    // Set up WebSocket event listeners
    const handleConnected = () => {
      forumStore.setConnectionStatus('connected');
    };

    const handleDisconnected = () => {
      forumStore.setConnectionStatus('disconnected');
    };

    const handleUserPresence = (data: UserPresence) => {
      forumStore.setUserPresence(data.userId, data);
    };

    const handleTypingIndicator = (data: TypingIndicator) => {
      const key = data.groupId || data.recipientId || '';
      forumStore.setTypingIndicator(key, data);

      // Auto-remove typing indicator after 3 seconds
      setTimeout(() => {
        forumStore.removeTypingIndicator(key, data.userId);
      }, 3000);
    };

    const handleMessageReaction = (data: MessageReaction) => {
      forumStore.addMessageReaction(data.messageId, data);
    };

    const handleForumMessage = (data: any) => {
      // Increment unread count if not in active conversation
      const key = data.groupId || data.senderId;
      if (key !== activeGroupId && key !== activeRecipientId) {
        forumStore.incrementUnreadCount(key);
      }
    };

    // Register event listeners
    websocketService.on('connected', handleConnected);
    websocketService.on('disconnected', handleDisconnected);
    websocketService.on('user_presence', handleUserPresence);
    websocketService.on('typing_indicator', handleTypingIndicator);
    websocketService.on('message_reaction', handleMessageReaction);
    websocketService.on('forum_message', handleForumMessage);

    // Cleanup on unmount
    return () => {
      websocketService.off('connected', handleConnected);
      websocketService.off('disconnected', handleDisconnected);
      websocketService.off('user_presence', handleUserPresence);
      websocketService.off('typing_indicator', handleTypingIndicator);
      websocketService.off('message_reaction', handleMessageReaction);
      websocketService.off('forum_message', handleForumMessage);
    };
  }, [activeGroupId, activeRecipientId]);

  return {
    // State
    userPresence,
    onlineUsers,
    typingIndicators,
    messageReactions,
    isConnected,
    connectionStatus,
    activeGroupId,
    activeRecipientId,
    unreadCounts,

    // Actions
    setActiveGroup: forumStore.setActiveGroup,
    setActiveRecipient: forumStore.setActiveRecipient,
    getUnreadCount: forumStore.getUnreadCount,
    getOnlineUsersInGroup: forumStore.getOnlineUsersInGroup,
    getTypingUsersInGroup: forumStore.getTypingUsersInGroup,
    getTypingUsersInDM: forumStore.getTypingUsersInDM,

    // WebSocket actions
    joinGroup: websocketService.joinGroup,
    leaveGroup: websocketService.leaveGroup,
    sendTypingIndicator: websocketService.sendTypingIndicator,
    updatePresence: websocketService.updatePresence,
    markMessageAsRead: websocketService.markMessageAsRead,
    isConnectedToServer: websocketService.isConnectedToServer,
  };
}

export function useUserPresence(userId?: string) {
  const { userPresence } = useSnapshot(forumStore);

  if (!userId) return null;

  return userPresence.get(userId) || null;
}

export function useTypingIndicators(groupId?: string, recipientId?: string) {
  const { typingIndicators } = useSnapshot(forumStore);

  const key = groupId || recipientId || '';
  return typingIndicators.get(key) || [];
}

export function useMessageReactions(messageId: string) {
  const { messageReactions } = useSnapshot(forumStore);

  return messageReactions.get(messageId) || [];
}

export function useOnlineStatus() {
  const { isConnected, connectionStatus, onlineUsers } =
    useSnapshot(forumStore);

  return {
    isConnected,
    connectionStatus,
    onlineUsers,
    onlineCount: onlineUsers.length,
  };
}

export function useUnreadCounts() {
  const { unreadCounts } = useSnapshot(forumStore);

  const getTotalUnreadCount = () => {
    let total = 0;
    unreadCounts.forEach((count: any) => {
      total += count;
    });
    return total;
  };

  return {
    unreadCounts,
    totalUnread: getTotalUnreadCount(),
    totalUnreadCount: getTotalUnreadCount(),
    getUnreadCount: forumStore.getUnreadCount,
  };
}

export function useUpdateUserStatus() {
  const updateStatus = async (statusData: {
    status: string;
    customMessage: string;
    emoji: string;
    clearAfter: string | null;
    showAsAway: boolean;
    pauseNotifications: boolean;
  }) => {
    // This would typically make an API call to update user status
    // For now, we'll update the local store
    websocketService.updatePresence(
      statusData.status as 'online' | 'offline' | 'away' | 'busy'
    );

    // Simulate API call
    return new Promise((resolve) => {
      setTimeout(resolve, 500);
    });
  };

  return {
    updateStatus,
    isUpdating: false,
  };
}
