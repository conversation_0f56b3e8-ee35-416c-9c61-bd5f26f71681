import useSWR from 'swr';

// Get system settings
export const GetSystemSettings = () => {
  const { data, error, isLoading, mutate } = useSWR(`/admin/system-settings`);

  return {
    settings: data,
    settingsLoading: isLoading,
    settingsError: error,
    mutate: mutate,
  };
};

// Get notification emails
export const GetNotificationEmails = () => {
  const { data, error, isLoading, mutate } = useSWR(
    `/settings/admin-notification-emails`
  );

  return {
    notificationEmails: data,
    notificationEmailsLoading: isLoading,
    notificationEmailsError: error,
    mutate: mutate,
  };
};
