import useSWR from 'swr';
import { myApi } from '@/api/fetcher';
import { toast } from 'sonner';

// Types for AI Assistant
export interface Message {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
  isTyping?: boolean;
  metadata?: {
    model?: string;
    tokens?: number;
    responseTime?: number;
  };
}

export interface ChatSession {
  id: string;
  title: string;
  messages: Message[];
  createdAt: Date;
  updatedAt: Date;
  userId: string;
  isActive: boolean;
  metadata?: {
    totalMessages: number;
    totalTokens: number;
    model: string;
  };
}

export interface AIResponse {
  message: Message;
  usage?: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
  model: string;
  responseTime: number;
}

export interface ChatRequest {
  message: string;
  sessionId?: string;
  context?: {
    patientId?: string;
    departmentId?: string;
    specialty?: string;
  };
  settings?: {
    model?: string;
    temperature?: number;
    maxTokens?: number;
  };
}

export interface AISettings {
  model: string;
  temperature: number;
  maxTokens: number;
  systemPrompt: string;
  enableContext: boolean;
  enableMemory: boolean;
}

// API Functions

// Get all chat sessions for current user
export const GetChatSessions = (params?: string) => {
  const qs = new URLSearchParams(params);
  const { data, error, isLoading, mutate } = useSWR(
    `/ai-assistant/sessions?${qs.toString()}`
  );

  return {
    sessions: data?.data || [],
    pagination: data?.pagination,
    isLoading,
    error,
    mutate,
  };
};

// Get a specific chat session
export const GetChatSession = (sessionId: string) => {
  const { data, error, isLoading, mutate } = useSWR(
    sessionId ? `/ai-assistant/sessions/${sessionId}` : null
  );

  return {
    session: data?.data as ChatSession,
    isLoading,
    error,
    mutate,
  };
};

// Create new chat session
export const createChatSession = async (title?: string) => {
  try {
    const response = await myApi.post('/ai-assistant/sessions', {
      title: title || 'New Chat',
    });
    return response.data;
  } catch (error) {
    console.error('Error creating chat session:', error);
    toast.error('Failed to create chat session');
    throw error;
  }
};

// Send message to AI Assistant
export const sendMessage = async (
  chatRequest: ChatRequest
): Promise<AIResponse> => {
  try {
    const response = await myApi.post('/ai-assistant/chat', chatRequest);
    return response.data;
  } catch (error) {
    console.error('Error sending message to AI:', error);
    toast.error('Failed to send message to AI Assistant');
    throw error;
  }
};

// Update chat session title
export const updateChatSessionTitle = async (
  sessionId: string,
  title: string
) => {
  try {
    const response = await myApi.patch(`/ai-assistant/sessions/${sessionId}`, {
      title,
    });
    toast.success('Chat title updated');
    return response.data;
  } catch (error) {
    console.error('Error updating chat session title:', error);
    toast.error('Failed to update chat title');
    throw error;
  }
};

// Delete chat session
export const deleteChatSession = async (sessionId: string) => {
  try {
    const response = await myApi.delete(`/ai-assistant/sessions/${sessionId}`);
    toast.success('Chat session deleted');
    return response.data;
  } catch (error) {
    console.error('Error deleting chat session:', error);
    toast.error('Failed to delete chat session');
    throw error;
  }
};

// Clear all messages in a session
export const clearChatSession = async (sessionId: string) => {
  try {
    const response = await myApi.post(
      `/ai-assistant/sessions/${sessionId}/clear`
    );
    toast.success('Chat cleared');
    return response.data;
  } catch (error) {
    console.error('Error clearing chat session:', error);
    toast.error('Failed to clear chat');
    throw error;
  }
};

// Get AI Assistant settings
export const GetAISettings = () => {
  const { data, error, isLoading, mutate } = useSWR('/ai-assistant/settings');

  return {
    settings: data?.data as AISettings,
    isLoading,
    error,
    mutate,
  };
};

// Update AI Assistant settings
export const updateAISettings = async (settings: Partial<AISettings>) => {
  try {
    const response = await myApi.patch('/ai-assistant/settings', settings);
    toast.success('AI Assistant settings updated');
    return response.data;
  } catch (error) {
    console.error('Error updating AI settings:', error);
    toast.error('Failed to update AI settings');
    throw error;
  }
};

// Get conversation analytics
export const GetConversationAnalytics = (params?: string) => {
  const qs = new URLSearchParams(params);
  const { data, error, isLoading, mutate } = useSWR(
    `/ai-assistant/analytics?${qs.toString()}`
  );

  return {
    analytics: data?.data,
    isLoading,
    error,
    mutate,
  };
};

// Export chat session
export const exportChatSession = async (
  sessionId: string,
  format: 'pdf' | 'txt' | 'json'
) => {
  try {
    const response = await myApi.post(
      `/ai-assistant/sessions/${sessionId}/export`,
      {
        format,
      }
    );

    // Handle file download
    const blob = new Blob([response.data], {
      type: format === 'pdf' ? 'application/pdf' : 'text/plain',
    });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `chat-session-${sessionId}.${format}`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);

    toast.success('Chat session exported successfully');
    return response.data;
  } catch (error) {
    console.error('Error exporting chat session:', error);
    toast.error('Failed to export chat session');
    throw error;
  }
};

// Search chat history
export const searchChatHistory = async (query: string, sessionId?: string) => {
  try {
    const response = await myApi.post('/ai-assistant/search', {
      query,
      sessionId,
    });
    return response.data;
  } catch (error) {
    console.error('Error searching chat history:', error);
    toast.error('Failed to search chat history');
    throw error;
  }
};

// Get suggested prompts based on context
export const getSuggestedPrompts = async (context?: {
  patientId?: string;
  departmentId?: string;
  specialty?: string;
}) => {
  try {
    const response = await myApi.post('/ai-assistant/suggestions', { context });
    return response.data;
  } catch (error) {
    console.error('Error getting suggested prompts:', error);
    return [];
  }
};

// Rate AI response
export const rateAIResponse = async (
  messageId: string,
  rating: 'positive' | 'negative',
  feedback?: string
) => {
  try {
    const response = await myApi.post(
      `/ai-assistant/messages/${messageId}/rate`,
      {
        rating,
        feedback,
      }
    );
    toast.success('Thank you for your feedback');
    return response.data;
  } catch (error) {
    console.error('Error rating AI response:', error);
    toast.error('Failed to submit rating');
    throw error;
  }
};

// Get AI model status
export const GetAIModelStatus = () => {
  const { data, error, isLoading, mutate } = useSWR('/ai-assistant/status');

  return {
    status: data?.data,
    isLoading,
    error,
    mutate,
  };
};

// Mock function for development - simulates OpenAI API call
export const mockAIResponse = async (message: string): Promise<string> => {
  // Simulate API delay
  await new Promise((resolve) =>
    setTimeout(resolve, 1000 + Math.random() * 2000)
  );

  const input = message.toLowerCase();

  if (input.includes('patient') || input.includes('medical')) {
    return 'I can help you with patient-related queries. For specific medical information, please ensure you have proper authorization and follow HIPAA guidelines. What specific information do you need? Please note: Full AI assistance is still in development.';
  } else if (input.includes('procedure') || input.includes('protocol')) {
    return "I can provide information about hospital procedures and protocols. Please specify which procedure or protocol you'd like to know about, and I'll provide the relevant guidelines. Please note: Full AI assistance is still in development.";
  } else if (input.includes('schedule') || input.includes('appointment')) {
    return 'For scheduling and appointments, I can guide you through the process. Would you like to know about appointment booking procedures, staff schedules, or patient appointment management? Please note: Full AI assistance is still in development.';
  } else if (input.includes('emergency')) {
    return 'For emergency situations, please follow the established emergency protocols. If this is a real emergency, contact the emergency response team immediately. I can provide information about emergency procedures if needed. Please note: Full AI assistance is still in development.';
  } else if (input.includes('medication') || input.includes('drug')) {
    return 'I can provide general information about medications and drug interactions. However, for specific patient prescriptions, please consult with the attending physician or pharmacist. What medication information do you need? Please note: Full AI assistance is still in development.';
  } else if (input.includes('lab') || input.includes('test')) {
    return "I can help with laboratory test information, normal ranges, and interpretation guidelines. Please specify which test or lab value you're asking about. Please note: Full AI assistance is still in development.";
  } else {
    return "Thank you for your question. I'm here to assist with hospital-related queries, medical information, procedures, administrative tasks and more. Please note: Full AI assistance is still in development.";
  }
};
