'use client';

import { useState } from 'react';
import { BarChart4 } from 'lucide-react';
import { GetHospitalAnalytics } from '@/api/crm/data';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { ChartContainer } from '@/components/ui/chart';
import { currencyFormat, numberFormat } from '@/lib/utils';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  <PERSON><PERSON>hart,
  Pie,
  Cell,
} from 'recharts';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

interface ChartConfig {
  [key: string]: {
    label: string;
    theme: {
      light: string;
      dark: string;
    };
  };
}

export default function HospitalAnalyticsPage() {
  const [dateRange, setDateRange] = useState<{
    startDate?: string;
    endDate?: string;
  }>({});
  const [activeTab, setActiveTab] = useState('patients');

  const { hospitalStats, hospitalStatsLoading } = GetHospitalAnalytics(
    dateRange.startDate && dateRange.endDate
      ? `startDate=${dateRange.startDate}&endDate=${dateRange.endDate}`
      : ''
  );

  const data = hospitalStats?.data || {
    patientStats: {
      totalPatients: 0,
      newPatientsThisMonth: 0,
      activePatients: 0,
      inpatientPatients: 0,
      outpatientPatients: 0,
      patientsByAge: [],
      patientsByGender: [],
    },
    appointmentStats: {
      totalAppointments: 0,
      scheduledAppointments: 0,
      completedAppointments: 0,
      cancelledAppointments: 0,
      noShowAppointments: 0,
      appointmentsByDepartment: [],
      appointmentsByType: [],
    },
    revenueStats: {
      totalRevenue: 0,
      averageRevenuePerPatient: 0,
      revenueByDepartment: [],
      revenueByMonth: [],
    },
    healthMetrics: {
      averageStayDuration: 0,
      readmissionRate: 0,
      patientSatisfactionScore: 0,
      commonDiagnoses: [],
    },
  };

  const chartConfig = {
    patients: {
      label: 'Patients',
      theme: {
        light: '#BD9A3D',
        dark: '#BD9A3D',
      },
    },
    revenue: {
      label: 'Revenue',
      theme: {
        light: '#2196F3',
        dark: '#2196F3',
      },
    },
    appointments: {
      label: 'Appointments',
      theme: {
        light: '#1A1A1A',
        dark: '#1A1A1A',
      },
    },
    metrics: {
      label: 'Metrics',
      theme: {
        light: '#4CAF50',
        dark: '#4CAF50',
      },
    },
  } satisfies ChartConfig;

  const COLORS = [
    '#BD9A3D',
    '#2196F3',
    '#1A1A1A',
    '#4CAF50',
    '#FF5722',
    '#9C27B0',
  ];

  const PatientAnalytics = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">
              Total Patients
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {numberFormat(data.patientStats.totalPatients)}
            </div>
            <p className="text-xs text-muted-foreground">
              {numberFormat(data.patientStats.newPatientsThisMonth)} new this
              month
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">
              Active Patients
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {numberFormat(data.patientStats.activePatients)}
            </div>
            <p className="text-xs text-muted-foreground">
              {data.patientStats.totalPatients > 0
                ? (
                    (data.patientStats.activePatients /
                      data.patientStats.totalPatients) *
                    100
                  ).toFixed(1)
                : 0}
              % of total
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Inpatients</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {numberFormat(data.patientStats.inpatientPatients)}
            </div>
            <p className="text-xs text-muted-foreground">Currently admitted</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Outpatients</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {numberFormat(data.patientStats.outpatientPatients)}
            </div>
            <p className="text-xs text-muted-foreground">Regular visits</p>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Patients by Age Group</CardTitle>
            <CardDescription>Distribution of patients by age</CardDescription>
          </CardHeader>
          <CardContent>
            <ChartContainer
              className="aspect-auto h-[300px]"
              config={chartConfig}
            >
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={data.patientStats.patientsByAge}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="ageGroup" />
                  <YAxis />
                  <Tooltip
                    formatter={(value) => [
                      numberFormat(value as number),
                      'Patients',
                    ]}
                  />
                  <Legend />
                  <Bar dataKey="count" name="Patients" fill="#BD9A3D" />
                </BarChart>
              </ResponsiveContainer>
            </ChartContainer>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Patients by Gender</CardTitle>
            <CardDescription>Distribution by gender</CardDescription>
          </CardHeader>
          <CardContent>
            <ChartContainer
              className="aspect-auto h-[300px]"
              config={chartConfig}
            >
              <PieChart>
                <Pie
                  data={data.patientStats.patientsByGender}
                  dataKey="count"
                  nameKey="gender"
                  cx="50%"
                  cy="50%"
                  outerRadius={80}
                  label={({ gender, count }) => `${gender}: ${count}`}
                  labelLine={false}
                >
                  {data.patientStats.patientsByGender.map(
                    (entry: any, index: number) => (
                      <Cell
                        key={`cell-${index}`}
                        fill={COLORS[index % COLORS.length]}
                      />
                    )
                  )}
                </Pie>
                <Tooltip
                  formatter={(value) => [`${value} patients`, 'Count']}
                  labelFormatter={(label) => `Gender: ${label}`}
                />
                <Legend />
              </PieChart>
            </ChartContainer>
          </CardContent>
        </Card>
      </div>
    </div>
  );

  const AppointmentAnalytics = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">
              Total Appointments
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {numberFormat(data.appointmentStats.totalAppointments)}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Scheduled</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {numberFormat(data.appointmentStats.scheduledAppointments)}
            </div>
            <p className="text-xs text-muted-foreground">
              Upcoming appointments
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Completed</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {numberFormat(data.appointmentStats.completedAppointments)}
            </div>
            <p className="text-xs text-muted-foreground">
              Successfully completed
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">
              Cancelled/No-Show
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {numberFormat(
                data.appointmentStats.cancelledAppointments +
                  data.appointmentStats.noShowAppointments
              )}
            </div>
            <p className="text-xs text-muted-foreground">Missed appointments</p>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Appointments by Department</CardTitle>
            <CardDescription>Distribution across departments</CardDescription>
          </CardHeader>
          <CardContent>
            <ChartContainer
              className="aspect-auto h-[300px]"
              config={chartConfig}
            >
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={data.appointmentStats.appointmentsByDepartment}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="department" />
                  <YAxis />
                  <Tooltip
                    formatter={(value) => [
                      numberFormat(value as number),
                      'Appointments',
                    ]}
                  />
                  <Legend />
                  <Bar dataKey="count" name="Appointments" fill="#2196F3" />
                </BarChart>
              </ResponsiveContainer>
            </ChartContainer>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Appointments by Type</CardTitle>
            <CardDescription>Distribution by appointment type</CardDescription>
          </CardHeader>
          <CardContent>
            <ChartContainer
              className="aspect-auto h-[300px]"
              config={chartConfig}
            >
              <PieChart>
                <Pie
                  data={data.appointmentStats.appointmentsByType}
                  dataKey="count"
                  nameKey="type"
                  cx="50%"
                  cy="50%"
                  outerRadius={80}
                  label={({ type, count }) => `${type}: ${count}`}
                  labelLine={false}
                >
                  {data.appointmentStats.appointmentsByType.map(
                    (entry: any, index: number) => (
                      <Cell
                        key={`cell-${index}`}
                        fill={COLORS[index % COLORS.length]}
                      />
                    )
                  )}
                </Pie>
                <Tooltip
                  formatter={(value) => [`${value} appointments`, 'Count']}
                  labelFormatter={(label) => `Type: ${label}`}
                />
                <Legend />
              </PieChart>
            </ChartContainer>
          </CardContent>
        </Card>
      </div>
    </div>
  );

  const RevenueAnalytics = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {currencyFormat(data.revenueStats.totalRevenue)}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">
              Avg. Revenue Per Patient
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {currencyFormat(data.revenueStats.averageRevenuePerPatient)}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">
              Patient Satisfaction
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {data.healthMetrics.patientSatisfactionScore.toFixed(1)}/5.0
            </div>
            <p className="text-xs text-muted-foreground">Average rating</p>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Revenue by Month</CardTitle>
            <CardDescription>Monthly revenue trends</CardDescription>
          </CardHeader>
          <CardContent>
            <ChartContainer
              className="aspect-auto h-[300px]"
              config={chartConfig}
            >
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={data.revenueStats.revenueByMonth}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis />
                  <Tooltip
                    formatter={(value) => [
                      currencyFormat(value as number),
                      'Revenue',
                    ]}
                  />
                  <Legend />
                  <Bar dataKey="revenue" name="Revenue" fill="#4CAF50" />
                </BarChart>
              </ResponsiveContainer>
            </ChartContainer>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Revenue by Department</CardTitle>
            <CardDescription>Distribution across departments</CardDescription>
          </CardHeader>
          <CardContent>
            <ChartContainer
              className="aspect-auto h-[300px]"
              config={chartConfig}
            >
              <PieChart>
                <Pie
                  data={data.revenueStats.revenueByDepartment}
                  dataKey="revenue"
                  nameKey="department"
                  cx="50%"
                  cy="50%"
                  outerRadius={80}
                  label={({ department, revenue }) =>
                    `${department}: ${currencyFormat(revenue)}`
                  }
                  labelLine={false}
                >
                  {data.revenueStats.revenueByDepartment.map(
                    (entry: any, index: number) => (
                      <Cell
                        key={`cell-${index}`}
                        fill={COLORS[index % COLORS.length]}
                      />
                    )
                  )}
                </Pie>
                <Tooltip
                  formatter={(value) => [
                    currencyFormat(value as number),
                    'Revenue',
                  ]}
                  labelFormatter={(label) => `Department: ${label}`}
                />
                <Legend />
              </PieChart>
            </ChartContainer>
          </CardContent>
        </Card>
      </div>
    </div>
  );

  const HealthMetricsAnalytics = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">
              Avg. Stay Duration
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {data.healthMetrics.averageStayDuration.toFixed(1)} days
            </div>
            <p className="text-xs text-muted-foreground">For inpatients</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">
              Readmission Rate
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {(data.healthMetrics.readmissionRate * 100).toFixed(1)}%
            </div>
            <p className="text-xs text-muted-foreground">30-day readmissions</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">
              Patient Satisfaction
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {data.healthMetrics.patientSatisfactionScore.toFixed(1)}/5.0
            </div>
            <p className="text-xs text-muted-foreground">Average rating</p>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Common Diagnoses</CardTitle>
          <CardDescription>Most frequent patient diagnoses</CardDescription>
        </CardHeader>
        <CardContent>
          <ChartContainer
            className="aspect-auto h-[300px]"
            config={chartConfig}
          >
            <ResponsiveContainer width="100%" height="100%">
              <BarChart
                data={data.healthMetrics.commonDiagnoses}
                layout="vertical"
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis type="number" />
                <YAxis dataKey="diagnosis" type="category" width={150} />
                <Tooltip
                  formatter={(value) => [
                    numberFormat(value as number),
                    'Cases',
                  ]}
                />
                <Legend />
                <Bar dataKey="count" name="Cases" fill="#9C27B0" />
              </BarChart>
            </ResponsiveContainer>
          </ChartContainer>
        </CardContent>
      </Card>
    </div>
  );

  return (
    <>
      <h2 className="text-lg font-bold text-gray-900 dark:text-white mb-4 text-left flex items-center gap-2">
        <BarChart4 className="w-5 h-5 text-zinc-900 dark:text-zinc-50" />
        Hospital Analytics
      </h2>

      <Tabs
        defaultValue="patients"
        value={activeTab}
        onValueChange={setActiveTab}
        className="w-full mb-6"
      >
        <TabsList className="grid grid-cols-4 mb-4">
          <TabsTrigger value="patients">Patients</TabsTrigger>
          <TabsTrigger value="appointments">Appointments</TabsTrigger>
          <TabsTrigger value="revenue">Revenue</TabsTrigger>
          <TabsTrigger value="metrics">Health Metrics</TabsTrigger>
        </TabsList>

        <TabsContent value="patients" className="mt-0">
          <PatientAnalytics />
        </TabsContent>

        <TabsContent value="appointments" className="mt-0">
          <AppointmentAnalytics />
        </TabsContent>

        <TabsContent value="revenue" className="mt-0">
          <RevenueAnalytics />
        </TabsContent>

        <TabsContent value="metrics" className="mt-0">
          <HealthMetricsAnalytics />
        </TabsContent>
      </Tabs>
    </>
  );
}
