import useSWR from 'swr';
import { myApi } from '../fetcher';
import { toast } from 'sonner';

// Game Types
export interface Game {
  id: string;
  name: string;
  description: string;
  type:
    | 'quiz'
    | 'trivia'
    | 'word_game'
    | 'puzzle'
    | 'prediction'
    | 'tournament';
  status: 'draft' | 'scheduled' | 'active' | 'completed' | 'cancelled';
  maxParticipants: number;
  currentParticipants: number;
  prizePool: number;
  prizeDistribution: PrizeDistribution[];
  startDate: string;
  endDate: string;
  duration: number; // in minutes
  rules: string;
  createdBy: string;
  createdByName: string;
  createdAt: string;
  updatedAt: string;
  settings: GameSettings;
  isPublic: boolean;
  tags: string[];
  category: string;
  difficulty: 'easy' | 'medium' | 'hard';
  totalQuestions: number;
  passingScore?: number;
}

export interface GameSettings {
  allowLateJoin: boolean;
  showLeaderboard: boolean;
  allowSpectators: boolean;
  requireApproval: boolean;
  maxAttemptsPerUser: number;
  timeLimit: number; // in seconds per question/round
  difficultyLevel: 'easy' | 'medium' | 'hard' | 'mixed';
  showCorrectAnswers: boolean;
  allowReviewAfterCompletion: boolean;
  randomizeQuestions: boolean;
  randomizeOptions: boolean;
  enableHints: boolean;
  penaltyForWrongAnswer: number; // points deducted
  bonusForSpeed: boolean; // extra points for quick answers
  minimumTimePerQuestion: number; // minimum seconds before allowing answer
}

export interface PrizeDistribution {
  position: number;
  amount: number;
  type: 'cash' | 'points' | 'reward';
  description: string;
}

export interface GameParticipant {
  id: string;
  gameId: string;
  userId: string;
  userName: string;
  userAvatar?: string;
  joinedAt: string;
  status:
    | 'registered'
    | 'active'
    | 'playing'
    | 'completed'
    | 'disqualified'
    | 'disconnected';
  score: number;
  rank: number;
  completedAt?: string;
  answers?: GameAnswer[];
  currentQuestionIndex: number;
  timeSpent: number; // total time in seconds
  correctAnswers: number;
  wrongAnswers: number;
  skippedAnswers: number;
  streakCount: number; // consecutive correct answers
  lastActivityAt: string;
}

export interface GameAnswer {
  questionId: string;
  answer: string | string[];
  isCorrect: boolean;
  timeSpent: number; // time spent on this question in seconds
  submittedAt: string;
  pointsEarned: number;
  speedBonus?: number;
  hintUsed: boolean;
  attemptNumber: number;
}

export interface GameQuestion {
  id: string;
  gameId: string;
  question: string;
  type:
    | 'multiple_choice'
    | 'single_choice'
    | 'true_false'
    | 'text'
    | 'number'
    | 'matching';
  options?: QuestionOption[];
  correctAnswer: string | string[];
  points: number;
  timeLimit: number; // in seconds
  order: number;
  explanation?: string;
  hint?: string;
  category?: string;
  difficulty: 'easy' | 'medium' | 'hard';
  media?: {
    type: 'image' | 'video' | 'audio';
    url: string;
    caption?: string;
  };
  tags?: string[];
}

export interface QuestionOption {
  id: string;
  text: string;
  value: string;
  isCorrect: boolean;
  order: number;
}

export interface GameResult {
  id: string;
  gameId: string;
  participants: GameParticipant[];
  winners: GameWinner[];
  totalPrizeAwarded: number;
  completedAt: string;
  statistics: GameStatistics;
}

export interface GameWinner {
  userId: string;
  userName: string;
  userAvatar?: string;
  position: number;
  score: number;
  prizeAmount: number;
  prizeType: 'cash' | 'points' | 'reward';
  awardedAt: string;
}

export interface GameStatistics {
  totalParticipants: number;
  completionRate: number;
  averageScore: number;
  averageTimeSpent: number;
  questionStats: QuestionStatistics[];
}

export interface QuestionStatistics {
  questionId: string;
  correctAnswers: number;
  incorrectAnswers: number;
  averageTimeSpent: number;
  difficultyRating: number;
}

export interface Leaderboard {
  gameId: string;
  participants: LeaderboardEntry[];
  lastUpdated: string;
}

export interface LeaderboardEntry {
  rank: number;
  userId: string;
  userName: string;
  userAvatar?: string;
  score: number;
  completedQuestions: number;
  totalQuestions: number;
  timeSpent: number;
  isCurrentUser: boolean;
  correctAnswers: number;
  accuracy: number; // percentage
  streakCount: number;
  lastUpdated: string;
}

// Real-time Quiz Session Interfaces
export interface QuizSession {
  id: string;
  gameId: string;
  participantId: string;
  status: 'waiting' | 'in_progress' | 'completed' | 'abandoned';
  currentQuestionIndex: number;
  startedAt: string;
  completedAt?: string;
  timeRemaining: number; // seconds remaining for current question
  totalTimeSpent: number;
  score: number;
  answers: SessionAnswer[];
  canUseHint: boolean;
  hintsUsed: number;
  maxHints: number;
}

export interface SessionAnswer {
  questionId: string;
  selectedAnswer?: string | string[];
  isCorrect?: boolean;
  timeSpent: number;
  pointsEarned: number;
  submittedAt: string;
  hintUsed: boolean;
}

export interface QuizTimer {
  questionId: string;
  timeLimit: number;
  timeRemaining: number;
  isActive: boolean;
  startedAt: string;
  warningThreshold: number; // seconds when to show warning
}

export interface LiveQuizUpdate {
  type:
    | 'question_start'
    | 'time_update'
    | 'question_end'
    | 'leaderboard_update'
    | 'participant_joined'
    | 'participant_left';
  gameId: string;
  data: any;
  timestamp: string;
}

// API Functions

// Get all games
export const GetGames = (params?: string) => {
  const qs = new URLSearchParams(params);
  const { data, error, isLoading, mutate } = useSWR(`/games?${qs.toString()}`);

  return {
    games: data?.data || [],
    isLoading,
    error,
    mutate,
  };
};

// Get active games
export const GetActiveGames = () => {
  const { data, error, isLoading, mutate } = useSWR('/games?status=active');

  return {
    activeGames: data?.data || [],
    isLoading,
    error,
    mutate,
  };
};

// Get a specific game
export const GetGame = (gameId: string) => {
  const { data, error, isLoading, mutate } = useSWR(
    gameId ? `/games/${gameId}` : null
  );

  return {
    game: data?.data,
    isLoading,
    error,
    mutate,
  };
};

// Get game participants
export const GetGameParticipants = (gameId: string) => {
  const { data, error, isLoading, mutate } = useSWR(
    gameId ? `/games/${gameId}/participants` : null
  );

  return {
    participants: data?.data || [],
    isLoading,
    error,
    mutate,
  };
};

// Get game questions
export const GetGameQuestions = (gameId: string) => {
  const { data, error, isLoading, mutate } = useSWR(
    gameId ? `/games/${gameId}/questions` : null
  );

  return {
    questions: data?.data || [],
    isLoading,
    error,
    mutate,
  };
};

// Get game leaderboard
export const GetGameLeaderboard = (gameId: string) => {
  const { data, error, isLoading, mutate } = useSWR(
    gameId ? `/games/${gameId}/leaderboard` : null
  );

  return {
    leaderboard: data?.data,
    isLoading,
    error,
    mutate,
  };
};

// Get game results
export const GetGameResults = (gameId: string) => {
  const { data, error, isLoading, mutate } = useSWR(
    gameId ? `/games/${gameId}/results` : null
  );

  return {
    results: data?.data,
    isLoading,
    error,
    mutate,
  };
};

// Get user's game history
export const GetUserGameHistory = (userId?: string) => {
  const { data, error, isLoading, mutate } = useSWR(
    userId ? `/games/user/${userId}/history` : '/games/user/history'
  );

  return {
    gameHistory: data?.data || [],
    isLoading,
    error,
    mutate,
  };
};

// Create a new game
export const createGame = async (gameData: Partial<Game>) => {
  try {
    const response = await myApi.post('/games', gameData);
    toast.success('Game created successfully');
    return response.data;
  } catch (error) {
    console.error('Error creating game:', error);
    toast.error('Failed to create game');
    throw error;
  }
};

// Update a game
export const updateGame = async (gameId: string, gameData: Partial<Game>) => {
  try {
    const response = await myApi.put(`/games/${gameId}`, gameData);
    toast.success('Game updated successfully');
    return response.data;
  } catch (error) {
    console.error('Error updating game:', error);
    toast.error('Failed to update game');
    throw error;
  }
};

// Delete a game
export const deleteGame = async (gameId: string) => {
  try {
    const response = await myApi.delete(`/games/${gameId}`);
    toast.success('Game deleted successfully');
    return response.data;
  } catch (error) {
    console.error('Error deleting game:', error);
    toast.error('Failed to delete game');
    throw error;
  }
};

// Quiz Session API Functions

// Start a quiz session
export const startQuizSession = async (gameId: string) => {
  try {
    const response = await myApi.post(`/games/${gameId}/session/start`);
    return response.data;
  } catch (error) {
    console.error('Error starting quiz session:', error);
    toast.error('Failed to start quiz session');
    throw error;
  }
};

// Get current quiz session
export const GetQuizSession = (gameId: string) => {
  const { data, error, isLoading, mutate } = useSWR(
    gameId ? `/games/${gameId}/session` : null
  );

  return {
    session: data?.data,
    isLoading,
    error,
    mutate,
  };
};

// Submit answer for current question
export const submitQuizAnswer = async (
  gameId: string,
  questionId: string,
  answer: string | string[],
  timeSpent: number,
  hintUsed: boolean = false
) => {
  try {
    const response = await myApi.post(`/games/${gameId}/session/answer`, {
      questionId,
      answer,
      timeSpent,
      hintUsed,
    });
    return response.data;
  } catch (error) {
    console.error('Error submitting quiz answer:', error);
    toast.error('Failed to submit answer');
    throw error;
  }
};

// Get hint for current question
export const getQuestionHint = async (gameId: string, questionId: string) => {
  try {
    const response = await myApi.post(`/games/${gameId}/session/hint`, {
      questionId,
    });
    return response.data;
  } catch (error) {
    console.error('Error getting hint:', error);
    toast.error('Failed to get hint');
    throw error;
  }
};

// Skip current question
export const skipQuestion = async (gameId: string, questionId: string) => {
  try {
    const response = await myApi.post(`/games/${gameId}/session/skip`, {
      questionId,
    });
    return response.data;
  } catch (error) {
    console.error('Error skipping question:', error);
    toast.error('Failed to skip question');
    throw error;
  }
};

// Complete quiz session
export const completeQuizSession = async (gameId: string) => {
  try {
    const response = await myApi.post(`/games/${gameId}/session/complete`);
    toast.success('Quiz completed successfully!');
    return response.data;
  } catch (error) {
    console.error('Error completing quiz session:', error);
    toast.error('Failed to complete quiz');
    throw error;
  }
};

// Get live leaderboard
export const GetLiveLeaderboard = (gameId: string) => {
  const { data, error, isLoading, mutate } = useSWR(
    gameId ? `/games/${gameId}/leaderboard/live` : null,
    { refreshInterval: 2000 } // Refresh every 2 seconds
  );

  return {
    leaderboard: data?.data || [],
    isLoading,
    error,
    mutate,
  };
};

// Join a game
export const joinGame = async (gameId: string) => {
  try {
    const response = await myApi.post(`/games/${gameId}/join`);
    toast.success('Successfully joined the game');
    return response.data;
  } catch (error) {
    console.error('Error joining game:', error);
    toast.error('Failed to join game');
    throw error;
  }
};

// Leave a game
export const leaveGame = async (gameId: string) => {
  try {
    const response = await myApi.post(`/games/${gameId}/leave`);
    toast.success('Successfully left the game');
    return response.data;
  } catch (error) {
    console.error('Error leaving game:', error);
    toast.error('Failed to leave game');
    throw error;
  }
};

// Start a game
export const startGame = async (gameId: string) => {
  try {
    const response = await myApi.post(`/games/${gameId}/start`);
    toast.success('Game started successfully');
    return response.data;
  } catch (error) {
    console.error('Error starting game:', error);
    toast.error('Failed to start game');
    throw error;
  }
};

// End a game
export const endGame = async (gameId: string) => {
  try {
    const response = await myApi.post(`/games/${gameId}/end`);
    toast.success('Game ended successfully');
    return response.data;
  } catch (error) {
    console.error('Error ending game:', error);
    toast.error('Failed to end game');
    throw error;
  }
};

// Submit answer to a game question
export const submitAnswer = async (
  gameId: string,
  questionId: string,
  answer: string
) => {
  try {
    const response = await myApi.post(
      `/games/${gameId}/questions/${questionId}/answer`,
      {
        answer,
      }
    );
    return response.data;
  } catch (error) {
    console.error('Error submitting answer:', error);
    throw error;
  }
};

// Add questions to a game
export const addGameQuestions = async (
  gameId: string,
  questions: Partial<GameQuestion>[]
) => {
  try {
    const response = await myApi.post(`/games/${gameId}/questions`, {
      questions,
    });
    toast.success('Questions added successfully');
    return response.data;
  } catch (error) {
    console.error('Error adding questions:', error);
    toast.error('Failed to add questions');
    throw error;
  }
};

// Update a game question
export const updateGameQuestion = async (
  gameId: string,
  questionId: string,
  questionData: Partial<GameQuestion>
) => {
  try {
    const response = await myApi.put(
      `/games/${gameId}/questions/${questionId}`,
      questionData
    );
    toast.success('Question updated successfully');
    return response.data;
  } catch (error) {
    console.error('Error updating question:', error);
    toast.error('Failed to update question');
    throw error;
  }
};

// Delete a game question
export const deleteGameQuestion = async (
  gameId: string,
  questionId: string
) => {
  try {
    const response = await myApi.delete(
      `/games/${gameId}/questions/${questionId}`
    );
    toast.success('Question deleted successfully');
    return response.data;
  } catch (error) {
    console.error('Error deleting question:', error);
    toast.error('Failed to delete question');
    throw error;
  }
};

// Award prizes
export const awardPrizes = async (gameId: string) => {
  try {
    const response = await myApi.post(`/games/${gameId}/award-prizes`);
    toast.success('Prizes awarded successfully');
    return response.data;
  } catch (error) {
    console.error('Error awarding prizes:', error);
    toast.error('Failed to award prizes');
    throw error;
  }
};

// Get game analytics
export const GetGameAnalytics = (gameId?: string, dateRange?: string) => {
  const params = new URLSearchParams();
  if (gameId) params.append('gameId', gameId);
  if (dateRange) params.append('dateRange', dateRange);

  const { data, error, isLoading, mutate } = useSWR(
    `/games/analytics?${params.toString()}`
  );

  return {
    analytics: data?.data,
    isLoading,
    error,
    mutate,
  };
};

// Get overall leaderboard (across all games)
export const GetOverallLeaderboard = (
  timeframe?: 'week' | 'month' | 'year' | 'all'
) => {
  const params = timeframe ? `?timeframe=${timeframe}` : '';
  const { data, error, isLoading, mutate } = useSWR(
    `/games/leaderboard${params}`
  );

  return {
    leaderboard: data?.data || [],
    isLoading,
    error,
    mutate,
  };
};

// Get user statistics
export const GetUserGameStats = (userId?: string) => {
  const endpoint = userId ? `/games/user/${userId}/stats` : '/games/user/stats';
  const { data, error, isLoading, mutate } = useSWR(endpoint);

  return {
    stats: data?.data,
    isLoading,
    error,
    mutate,
  };
};
