import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useSnapshot } from 'valtio';
import { accessTokenStore } from '../store/accessToken';
import { GetProfile } from '@/api/staff';

/**
 * Hook to check if the user is logged in with a valid token and profile
 * @returns Boolean indicating if the user is logged in
 */
const useIsLoggedIn = () => {
  const token = useSnapshot(accessTokenStore).accessToken;
  const [isLoggedIn, setIsLoggedIn] = useState<boolean | null>(null);
  const router = useRouter();
  const { profile, isLoading } = GetProfile();

  useEffect(() => {
    // If no token, definitely not logged in
    if (!token) {
      setIsLoggedIn(false);
      // Only redirect if we're not already on the login page
      if (window.location.pathname !== '/login') {
        router.push('/login');
      }
      return;
    }

    // If still loading profile data, wait but consider user as potentially logged in
    if (isLoading) {
      // Don't change the state while loading to avoid flashing content
      return;
    }

    // Check if profile is valid - but be more lenient during initial load
    // Only consider invalid if we've definitely received the profile and it's invalid
    let profileValid = true;

    if (profile === null) {
      // Profile request completed but returned null - definitely invalid
      profileValid = false;
    } else if (profile && !profile.data) {
      // Profile exists but is missing required data - definitely invalid
      profileValid = false;
    }

    // Set logged in state based on token and profile validity
    setIsLoggedIn(!!token && profileValid);

    // If token exists but profile is definitely invalid (not just loading),
    // and we've waited a reasonable time, then redirect
    if (token && !profileValid && !isLoading) {
      // Add a delay to avoid immediate redirect which might cause loops
      const timer = setTimeout(() => {
        console.error('Invalid user profile detected');
        // Only redirect if we're not already on the login page
        if (window.location.pathname !== '/login') {
          // Clear token and redirect to login
          accessTokenStore.accessToken = undefined;
          router.push('/login');
        }
      }, 2000); // 2 second delay

      return () => clearTimeout(timer);
    }
  }, [token, profile, isLoading, router]);

  return isLoggedIn;
};

export default useIsLoggedIn;
