import React, { useState } from 'react';
import { InputField, FormRow } from '@/components/common/form';
import { Form } from '@/components/ui/form';
import { Modal } from '@/components/common/modal';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import {
  locationFormSchema,
  Location,
} from '@/components/validations/locationPrice';
import { myApi } from '@/api/fetcher';
import { toast } from 'sonner';
import { ModalProps } from '../../types';

const NewLocation: React.FC<ModalProps> = ({ setOpen, mutate, open }) => {
  const [isLoading, setIsLoading] = useState(false);

  const form = useForm<Location>({
    resolver: zodResolver(locationFormSchema),
    defaultValues: {
      name: '',
      region: '',
      country: '',
      currency: 'NGN',
      longitude: undefined,
      latitude: undefined,
    },
  });

  const onSubmit = async (data: Location) => {
    try {
      setIsLoading(true);
      const res = await myApi.post('/location/new-location', {
        name: data.name,
        region: data.region,
        country: data.country,
        currency: data.currency,
        latitude: data.latitude,
        longitude: data.longitude,
      });
      setIsLoading(false);
      if (res.status === 200) {
        toast.success(res.data.data.message);
        if (mutate) {
          mutate();
        }
        setOpen(false);
        form.reset();
      }
    } catch (error) {
      setIsLoading(false);
    }
  };

  return (
    <Modal
      open={open}
      setOpen={setOpen}
      title="Add New Location"
      description="Add to the Hospital list of locations"
      isLoading={isLoading}
      onSubmit={form.handleSubmit(onSubmit)}
    >
      <Form {...form}>
        <form className="space-y-8">
          <FormRow>
            <InputField
              control={form.control}
              name="name"
              label="Location Name"
              placeholder="E.g Gudu"
              type="text"
            />
            <InputField
              control={form.control}
              name="region"
              label="Location State"
              placeholder="E.g Abuja"
              type="text"
            />
            <InputField
              control={form.control}
              name="country"
              label="Country"
              placeholder="E.g Nigeria"
              type="text"
            />
            <InputField
              control={form.control}
              name="currency"
              label="Currency(Optional, NGN is default)"
              placeholder="E.g NGN"
              type="text"
            />
            <InputField
              control={form.control}
              name="latitude"
              label="latitude(Optional)"
              placeholder="E.g 8.992833354560634"
              type="string"
            />
            <InputField
              control={form.control}
              name="longitude"
              label="longitude(Optional)"
              placeholder="E.g 7.487009537470287"
              type="string"
            />
          </FormRow>
        </form>
      </Form>
    </Modal>
  );
};

export default NewLocation;
