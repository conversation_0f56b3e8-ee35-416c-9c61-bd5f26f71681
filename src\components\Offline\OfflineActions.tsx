'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { RefreshCw, Home, AlertCircle } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';

const OfflineActions = () => {
  const [isOnline, setIsOnline] = useState(true);
  const [showReconnected, setShowReconnected] = useState(false);

  useEffect(() => {
    // Set initial online status
    setIsOnline(navigator.onLine);

    // Add event listeners for online/offline events
    const handleOnline = () => {
      setIsOnline(true);
      setShowReconnected(true);

      // Hide the reconnected message after 5 seconds
      setTimeout(() => {
        setShowReconnected(false);
      }, 5000);
    };

    const handleOffline = () => {
      setIsOnline(false);
      setShowReconnected(false);
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  const handleRefresh = () => {
    window.location.reload();
  };

  const handleGoHome = () => {
    window.location.href = '/';
  };

  return (
    <div className="space-y-4">
      {showReconnected && (
        <Alert className="bg-green-50 border-green-200">
          <AlertCircle className="h-4 w-4 text-green-600" />
          <AlertTitle className="text-green-800">Connected</AlertTitle>
          <AlertDescription className="text-green-700">
            Your internet connection has been restored.
          </AlertDescription>
        </Alert>
      )}

      <div className="flex flex-col sm:flex-row gap-3 justify-center">
        <Button
          onClick={handleRefresh}
          className="flex items-center justify-center gap-2"
          disabled={!isOnline}
        >
          <RefreshCw className="w-4 h-4" />
          {isOnline ? 'Refresh Page' : 'Try Again'}
        </Button>

        <Button
          variant="outline"
          onClick={handleGoHome}
          className="flex items-center justify-center gap-2"
        >
          <Home className="w-4 h-4" />
          Go to Homepage
        </Button>
      </div>

      {!isOnline && (
        <p className="text-xs text-gray-500 mt-4">Waiting for connection...</p>
      )}
    </div>
  );
};

export default OfflineActions;
