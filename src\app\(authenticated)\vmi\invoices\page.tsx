'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { vmiApi } from '@/api/vmi';
import { Invoice } from '@/lib/types/vmi';
import { formatCurrency } from '@/lib/vmi-utils';
import { Download, Eye, Plus } from 'lucide-react';
import jsPDF from 'jspdf';
import 'jspdf-autotable';

declare module 'jspdf' {
  interface jsPDF {
    autoTable: (options: any) => jsPDF;
  }
}

export default function VMIInvoices() {
  const [invoices, setInvoices] = useState<Invoice[]>([]);

  useEffect(() => {
    vmiApi.getInvoices().then(setInvoices);
  }, []);

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'paid':
        return <Badge variant="default">Paid</Badge>;
      case 'overdue':
        return <Badge variant="destructive">Overdue</Badge>;
      case 'sent':
        return <Badge variant="secondary">Sent</Badge>;
      default:
        return <Badge variant="outline">Draft</Badge>;
    }
  };

  const downloadInvoice = (invoice: Invoice) => {
    const doc = new jsPDF();

    // Header
    doc.setFontSize(20);
    doc.text('VMI INVOICE', 20, 30);

    // Invoice details
    doc.setFontSize(12);
    doc.text(`Invoice Number: ${invoice.invoiceNumber}`, 20, 50);
    doc.text(
      `Issue Date: ${new Date(invoice.issueDate).toLocaleDateString()}`,
      20,
      60
    );
    doc.text(
      `Due Date: ${new Date(invoice.dueDate).toLocaleDateString()}`,
      20,
      70
    );
    doc.text(`Status: ${invoice.status.toUpperCase()}`, 20, 80);

    // Items table
    const tableData = invoice.items.map((item) => [
      item.product.name,
      item.quantity.toString(),
      `₦${item.unitPrice.toLocaleString()}`,
      `₦${item.totalPrice.toLocaleString()}`,
    ]);

    doc.autoTable({
      head: [['Product', 'Quantity', 'Unit Price', 'Total']],
      body: tableData,
      startY: 90,
    });

    // Total
    const finalY = (doc as any).lastAutoTable.finalY + 10;
    doc.setFontSize(14);
    doc.text(
      `Total Amount: ₦${invoice.totalAmount.toLocaleString()}`,
      20,
      finalY
    );

    doc.save(`invoice-${invoice.invoiceNumber}.pdf`);
  };

  const downloadReceipt = (invoice: Invoice) => {
    const doc = new jsPDF();

    doc.setFontSize(20);
    doc.text('PAYMENT RECEIPT', 20, 30);

    doc.setFontSize(12);
    doc.text(`Receipt for Invoice: ${invoice.invoiceNumber}`, 20, 50);
    doc.text(`Payment Date: ${new Date().toLocaleDateString()}`, 20, 60);
    doc.text(`Amount Paid: ₦${invoice.totalAmount.toLocaleString()}`, 20, 70);
    doc.text('Payment Method: Bank Transfer', 20, 80);
    doc.text('Status: PAID', 20, 90);

    doc.save(`receipt-${invoice.invoiceNumber}.pdf`);
  };

  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Invoices & Receipts</h1>
        <Button>
          <Plus className="h-4 w-4 mr-2" />
          Generate Invoice
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader>
            <CardTitle className="text-sm font-medium">
              Total Invoices
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{invoices.length}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-sm font-medium">Outstanding</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-500">
              {invoices.filter((i) => i.status !== 'paid').length}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-sm font-medium">Overdue</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-500">
              {invoices.filter((i) => i.status === 'overdue').length}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-sm font-medium">Total Value</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatCurrency(
                invoices.reduce((sum, inv) => sum + inv.totalAmount, 0)
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Invoice List</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Invoice #</TableHead>
                <TableHead>Issue Date</TableHead>
                <TableHead>Due Date</TableHead>
                <TableHead>Amount</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {invoices.map((invoice) => (
                <TableRow key={invoice.id}>
                  <TableCell className="font-medium">
                    {invoice.invoiceNumber}
                  </TableCell>
                  <TableCell>
                    {new Date(invoice.issueDate).toLocaleDateString()}
                  </TableCell>
                  <TableCell>
                    {new Date(invoice.dueDate).toLocaleDateString()}
                  </TableCell>
                  <TableCell>{formatCurrency(invoice.totalAmount)}</TableCell>
                  <TableCell>{getStatusBadge(invoice.status)}</TableCell>
                  <TableCell>
                    <div className="flex gap-2">
                      <Button size="sm" variant="outline">
                        <Eye className="h-3 w-3 mr-1" />
                        View
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => downloadInvoice(invoice)}
                      >
                        <Download className="h-3 w-3 mr-1" />
                        Invoice
                      </Button>
                      {invoice.status === 'paid' && (
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => downloadReceipt(invoice)}
                        >
                          <Download className="h-3 w-3 mr-1" />
                          Receipt
                        </Button>
                      )}
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}
