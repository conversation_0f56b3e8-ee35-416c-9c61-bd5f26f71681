import React, { useState } from 'react';
import { Modal } from '@/components/common/modal';
import { ModalProps } from '@/components/crm/types';
import dayjs from 'dayjs';
import { myApi } from '@/api/fetcher';
import { toast } from 'sonner';
import { GetProfile } from '@/api/staff';
import { Button } from '@/components/ui/button';
import { hasPermission, PERMISSIONS } from '@/lib/types/permissions';
import { useRouter } from 'next/navigation';
import { Paths } from '@/components/navigations/data';
import {
  MessageSquare,
  Mail,
  Phone,
  Video,
  MessageCircle,
  Instagram,
  Twitter,
  Facebook,
  Reply,
  Check,
  Calendar,
  Tag,
} from 'lucide-react';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';

const Details: React.FC<ModalProps> = ({ open, setOpen, mutate, data }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [replyMode, setReplyMode] = useState(false);
  const [replyContent, setReplyContent] = useState('');
  const { profile } = GetProfile();
  const router = useRouter();
  const permitUpdate = hasPermission(PERMISSIONS.INTERACTION_EDIT);
  const permitReply = hasPermission(PERMISSIONS.INTERACTION_REPLY);

  if (!data) return null;

  // Function to render the appropriate icon based on channel
  const getChannelIcon = (channel: string) => {
    switch (channel) {
      case 'whatsapp':
        return <MessageSquare className="w-5 h-5 text-green-500" />;
      case 'email':
        return <Mail className="w-5 h-5 text-blue-500" />;
      case 'sms':
        return <MessageCircle className="w-5 h-5 text-purple-500" />;
      case 'phone':
        return <Phone className="w-5 h-5 text-red-500" />;
      case 'video':
        return <Video className="w-5 h-5 text-orange-500" />;
      case 'instagram':
        return <Instagram className="w-5 h-5 text-pink-500" />;
      case 'twitter':
        return <Twitter className="w-5 h-5 text-blue-400" />;
      case 'facebook':
        return <Facebook className="w-5 h-5 text-blue-600" />;
      default:
        return <MessageSquare className="w-5 h-5 text-gray-500" />;
    }
  };

  const handleMarkAsCompleted = async () => {
    try {
      setIsLoading(true);
      const res = await myApi.patch(
        `/crm/patient-interactions/${data.id}/status`,
        {
          status: 'completed',
          updatedBy: profile.data.fullName,
        }
      );
      setIsLoading(false);
      if (res.status === 200) {
        toast.success('Interaction marked as completed');
        if (mutate) {
          mutate();
        }
        setOpen(false);
      }
    } catch (error: any) {
      setIsLoading(false);
      toast.error(error?.response?.data?.message || 'An error occurred');
    }
  };

  const handleReply = async () => {
    if (!replyContent.trim()) {
      toast.error('Reply content cannot be empty');
      return;
    }

    try {
      setIsLoading(true);
      const res = await myApi.post('/crm/patient-interactions', {
        patientId: data.patientId,
        channel: data.channel,
        subject: `Re: ${data.subject}`,
        content: replyContent,
        status: 'completed',
        direction: 'outbound',
        contactInfo: data.contactInfo,
        tags: data.tags,
        parentInteractionId: data.id,
        createdBy: profile.data.fullName,
      });
      setIsLoading(false);
      if (res.status === 200) {
        toast.success('Reply sent successfully');
        if (mutate) {
          mutate();
        }
        setReplyMode(false);
        setReplyContent('');
        setOpen(false);
      }
    } catch (error: any) {
      setIsLoading(false);
      toast.error(error?.response?.data?.message || 'An error occurred');
    }
  };

  const viewPatientDetails = () => {
    router.push(`${Paths.Patients}?id=${data.patientId}`);
    setOpen(false);
  };

  return (
    <Modal
      open={open}
      setOpen={setOpen}
      title="Interaction Details"
      description={data.subject}
      size="lg"
    >
      <div className="space-y-6">
        <div className="flex justify-between items-start">
          <div className="flex items-center gap-2">
            {getChannelIcon(data.channel)}
            <h3 className="text-lg font-semibold capitalize">
              {data.channel} Interaction
            </h3>
            <span
              className={`px-2 py-1 text-xs rounded-full ${
                data.direction === 'inbound'
                  ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400'
                  : 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'
              }`}
            >
              {data.direction === 'inbound' ? 'Inbound' : 'Outbound'}
            </span>
          </div>
          <span
            className={`px-2 py-1 text-xs rounded-full ${
              data.status === 'completed'
                ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'
                : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400'
            }`}
          >
            {data.status.charAt(0).toUpperCase() + data.status.slice(1)}
          </span>
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <h4 className="text-sm font-medium">Patient</h4>
            <p className="text-sm flex items-center gap-2">
              {data.patientName}
              <Button variant="outline" size="sm" onClick={viewPatientDetails}>
                View Patient
              </Button>
            </p>
          </div>
          <div>
            <h4 className="text-sm font-medium">Date & Time</h4>
            <p className="text-sm">
              {dayjs(data.createdAt).format('MMMM D, YYYY h:mm A')}
            </p>
          </div>
          <div>
            <h4 className="text-sm font-medium">Contact Information</h4>
            <p className="text-sm">{data.contactInfo || 'Not provided'}</p>
          </div>
          <div>
            <h4 className="text-sm font-medium">Recorded By</h4>
            <p className="text-sm">{data.createdBy}</p>
          </div>
        </div>

        <div>
          <h4 className="text-sm font-medium">Subject</h4>
          <p className="text-sm">{data.subject}</p>
        </div>

        <div>
          <h4 className="text-sm font-medium">Content</h4>
          <div className="p-3 bg-gray-50 dark:bg-gray-900 rounded-md mt-1">
            <p className="text-sm whitespace-pre-wrap">{data.content}</p>
          </div>
        </div>

        {data.tags && (
          <div>
            <h4 className="text-sm font-medium flex items-center gap-1">
              <Tag className="w-4 h-4" />
              Tags
            </h4>
            <div className="flex flex-wrap gap-2 mt-1">
              {data.tags.split(',').map((tag: string, index: number) => (
                <Badge key={index} variant="outline" className="text-xs">
                  {tag.trim()}
                </Badge>
              ))}
            </div>
          </div>
        )}

        {data.followUpRequired && (
          <div>
            <h4 className="text-sm font-medium flex items-center gap-1">
              <Calendar className="w-4 h-4" />
              Follow-up Required
            </h4>
            <p className="text-sm">
              {data.followUpDate
                ? `Scheduled for ${dayjs(data.followUpDate).format('MMMM D, YYYY h:mm A')}`
                : 'No date specified'}
            </p>
          </div>
        )}

        {replyMode ? (
          <div className="space-y-3">
            <h4 className="text-sm font-medium">Reply</h4>
            <Textarea
              placeholder="Type your reply here..."
              className="resize-none"
              value={replyContent}
              onChange={(e) => setReplyContent(e.target.value)}
              rows={5}
            />
            <div className="flex justify-end gap-2">
              <Button
                type="button"
                variant="outline"
                onClick={() => setReplyMode(false)}
              >
                Cancel
              </Button>
              <Button
                type="button"
                onClick={handleReply}
                disabled={isLoading || !replyContent.trim()}
              >
                Send Reply
              </Button>
            </div>
          </div>
        ) : (
          <div className="flex justify-end gap-2">
            {data.status === 'pending' && permitUpdate && (
              <Button
                variant="outline"
                onClick={handleMarkAsCompleted}
                disabled={isLoading}
                className="flex items-center gap-1"
              >
                <Check className="w-4 h-4" />
                Mark as Completed
              </Button>
            )}
            {permitReply && (
              <Button
                onClick={() => setReplyMode(true)}
                disabled={isLoading}
                className="flex items-center gap-1"
              >
                <Reply className="w-4 h-4" />
                Reply
              </Button>
            )}
          </div>
        )}
      </div>
    </Modal>
  );
};

export default Details;
