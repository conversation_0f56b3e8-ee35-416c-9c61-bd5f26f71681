import React, { useState, useEffect } from 'react';
import { InputField } from '@/components/common/form';
import { Form } from '@/components/ui/form';
import { Modal } from '@/components/common/modal';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { myApi } from '@/api/fetcher';
import { toast } from 'sonner';
import {
  doctorFormSchema,
  DoctorFormValues,
} from '@/components/validations/crm';
import { GetProfile } from '@/api/staff';
import { GetDepartments } from '@/api/crm/data';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { ModalProps } from '@/components/crm/types';
import { Switch } from '@/components/ui/switch';

const Create: React.FC<ModalProps> = ({ setOpen, open, mutate }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [departments, setDepartments] = useState<any[]>([]);
  const [departmentsLoading, setDepartmentsLoading] = useState(false);
  const { profile } = GetProfile();

  const form = useForm<DoctorFormValues>({
    resolver: zodResolver(doctorFormSchema),
    defaultValues: {
      name: '',
      email: '',
      phoneNumber: '',
      department: '',
      specialization: '',
      qualifications: '',
      experience: '',
      bio: '',
      isAvailable: true,
      consultationFee: '',
    },
  });

  // Fetch departments for dropdown
  useEffect(() => {
    const fetchDepartments = async () => {
      if (open) {
        try {
          setDepartmentsLoading(true);
          const response = await myApi.get('/crm/departments');
          if (response.status === 200) {
            setDepartments(response.data.data.departments);
          }
        } catch (error) {
          console.error('Error fetching departments:', error);
        } finally {
          setDepartmentsLoading(false);
        }
      }
    };

    fetchDepartments();
  }, [open]);

  const onSubmit = async (data: DoctorFormValues) => {
    try {
      setIsLoading(true);
      const res = await myApi.post('/crm/doctors', {
        ...data,
        createdBy: profile.data.fullName,
      });
      setIsLoading(false);
      if (res.status === 200) {
        toast.success('Doctor added successfully');
        if (mutate) {
          mutate();
        }
        setOpen(false);
        form.reset();
      }
    } catch (error: any) {
      setIsLoading(false);
      toast.error(error?.response?.data?.message || 'An error occurred');
    }
  };

  return (
    <Modal
      title="Add New Doctor"
      open={open}
      setOpen={setOpen}
      isLoading={isLoading}
      description="Add a new doctor to the hospital"
      onSubmit={form.handleSubmit(onSubmit)}
    >
      <Form {...form}>
        <form className="space-y-4">
          <InputField
            control={form.control}
            name="name"
            label="Full Name"
            placeholder="Dr. John Doe"
          />

          <div className="grid grid-cols-2 gap-4">
            <InputField
              control={form.control}
              name="email"
              label="Email"
              placeholder="<EMAIL>"
              type="email"
            />

            <InputField
              control={form.control}
              name="phoneNumber"
              label="Phone Number"
              placeholder="+234 ************"
            />
          </div>

          <div>
            <label className="text-sm font-medium">Department</label>
            <Select
              onValueChange={(value) => form.setValue('department', value)}
              disabled={departmentsLoading}
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Select department" />
              </SelectTrigger>
              <SelectContent>
                {departments.map((department) => (
                  <SelectItem key={department.id} value={department.name}>
                    {department.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {form.formState.errors.department && (
              <p className="text-xs text-red-500 mt-1">
                {form.formState.errors.department.message}
              </p>
            )}
          </div>

          <InputField
            control={form.control}
            name="specialization"
            label="Specialization"
            placeholder="Cardiology, Pediatrics, etc."
          />

          <InputField
            control={form.control}
            name="qualifications"
            label="Qualifications"
            placeholder="MBBS, MD, MS, etc."
          />

          <InputField
            control={form.control}
            name="experience"
            label="Experience (years)"
            placeholder="10"
            type="number"
          />

          <InputField
            control={form.control}
            name="consultationFee"
            label="Consultation Fee"
            placeholder="5000"
            type="number"
          />

          <div>
            <label className="text-sm font-medium">Bio</label>
            <Textarea
              placeholder="Brief description about the doctor"
              className="resize-none"
              {...form.register('bio')}
            />
            {form.formState.errors.bio && (
              <p className="text-xs text-red-500 mt-1">
                {form.formState.errors.bio.message}
              </p>
            )}
          </div>

          <div className="flex items-center space-x-2">
            <Switch
              id="availability"
              checked={form.getValues('isAvailable')}
              onCheckedChange={(checked) =>
                form.setValue('isAvailable', checked)
              }
            />
            <label htmlFor="availability" className="text-sm font-medium">
              Available for Appointments
            </label>
          </div>
        </form>
      </Form>
    </Modal>
  );
};

export default Create;
