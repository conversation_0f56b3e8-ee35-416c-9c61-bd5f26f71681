import useSWR from 'swr';
import { myApi } from '../fetcher';
import { toast } from 'sonner';

// Survey Types
export interface Survey {
  id: string;
  title: string;
  description: string;
  status: 'draft' | 'active' | 'completed' | 'archived';
  createdBy: string;
  createdByName: string;
  createdAt: string;
  updatedAt: string;
  startDate?: string;
  endDate?: string;
  isAnonymous: boolean;
  allowMultipleResponses: boolean;
  requireLogin: boolean;
  questions: SurveyQuestion[];
  totalResponses: number;
  targetAudience: 'all' | 'department' | 'role' | 'custom';
  targetCriteria?: string[];
  settings: SurveySettings;
}

export interface SurveySettings {
  showProgressBar: boolean;
  allowBackNavigation: boolean;
  randomizeQuestions: boolean;
  showResultsAfterSubmission: boolean;
  sendNotificationEmails: boolean;
  collectRespondentInfo: boolean;
}

export interface SurveyQuestion {
  id: string;
  surveyId: string;
  type:
    | 'multiple_choice'
    | 'single_choice'
    | 'text'
    | 'textarea'
    | 'rating'
    | 'yes_no'
    | 'date'
    | 'number'
    | 'email'
    | 'matrix';
  question: string;
  description?: string;
  isRequired: boolean;
  order: number;
  options?: SurveyQuestionOption[];
  validation?: QuestionValidation;
  logic?: QuestionLogic;
}

export interface SurveyQuestionOption {
  id: string;
  text: string;
  value: string;
  order: number;
}

export interface QuestionValidation {
  minLength?: number;
  maxLength?: number;
  minValue?: number;
  maxValue?: number;
  pattern?: string;
  customMessage?: string;
}

export interface QuestionLogic {
  showIf?: {
    questionId: string;
    operator:
      | 'equals'
      | 'not_equals'
      | 'contains'
      | 'greater_than'
      | 'less_than';
    value: string;
  };
  skipTo?: string; // Question ID to skip to
}

export interface SurveyResponse {
  id: string;
  surveyId: string;
  respondentId?: string;
  respondentName?: string;
  respondentEmail?: string;
  submittedAt: string;
  completionTime: number; // in seconds
  isComplete: boolean;
  answers: SurveyAnswer[];
  ipAddress?: string;
  userAgent?: string;
}

export interface SurveyAnswer {
  id: string;
  responseId: string;
  questionId: string;
  questionType: string;
  answer: string | string[] | number;
  textAnswer?: string;
  selectedOptions?: string[];
  numericAnswer?: number;
  dateAnswer?: string;
}

export interface SurveyAnalytics {
  surveyId: string;
  totalResponses: number;
  completionRate: number;
  averageCompletionTime: number;
  responsesByDate: { date: string; count: number }[];
  questionAnalytics: QuestionAnalytics[];
  demographicBreakdown?: DemographicBreakdown;
}

export interface QuestionAnalytics {
  questionId: string;
  questionText: string;
  questionType: string;
  totalResponses: number;
  skipRate: number;
  optionStats?: { option: string; count: number; percentage: number }[];
  averageRating?: number;
  textResponses?: string[];
  numericStats?: {
    min: number;
    max: number;
    average: number;
    median: number;
  };
}

export interface DemographicBreakdown {
  byDepartment: { department: string; count: number }[];
  byRole: { role: string; count: number }[];
  byExperience: { range: string; count: number }[];
}

// API Functions

// Get all surveys
export const GetSurveys = (params?: string) => {
  const qs = new URLSearchParams(params);
  const { data, error, isLoading, mutate } = useSWR(
    `/surveys?${qs.toString()}`
  );

  return {
    surveys: data?.data || [],
    isLoading,
    error,
    mutate,
  };
};

// Get active surveys
export const GetActiveSurveys = () => {
  const { data, error, isLoading, mutate } = useSWR('/surveys?status=active');

  return {
    activeSurveys: data?.data || [],
    isLoading,
    error,
    mutate,
  };
};

// Get a specific survey
export const GetSurvey = (surveyId: string) => {
  const { data, error, isLoading, mutate } = useSWR(
    surveyId ? `/surveys/${surveyId}` : null
  );

  return {
    survey: data?.data,
    isLoading,
    error,
    mutate,
  };
};

// Get survey responses
export const GetSurveyResponses = (surveyId: string) => {
  const { data, error, isLoading, mutate } = useSWR(
    surveyId ? `/surveys/${surveyId}/responses` : null
  );

  return {
    responses: data?.data || [],
    isLoading,
    error,
    mutate,
  };
};

// Get survey analytics
export const GetSurveyAnalytics = (surveyId: string) => {
  const { data, error, isLoading, mutate } = useSWR(
    surveyId ? `/surveys/${surveyId}/analytics` : null
  );

  return {
    analytics: data?.data,
    isLoading,
    error,
    mutate,
  };
};

// Get user's survey responses
export const GetUserSurveyResponses = (userId?: string) => {
  const endpoint = userId
    ? `/surveys/user/${userId}/responses`
    : '/surveys/user/responses';
  const { data, error, isLoading, mutate } = useSWR(endpoint);

  return {
    userResponses: data?.data || [],
    isLoading,
    error,
    mutate,
  };
};

// Create a new survey
export const createSurvey = async (surveyData: Partial<Survey>) => {
  try {
    const response = await myApi.post('/surveys', surveyData);
    toast.success('Survey created successfully');
    return response.data;
  } catch (error) {
    console.error('Error creating survey:', error);
    toast.error('Failed to create survey');
    throw error;
  }
};

// Update a survey
export const updateSurvey = async (
  surveyId: string,
  surveyData: Partial<Survey>
) => {
  try {
    const response = await myApi.put(`/surveys/${surveyId}`, surveyData);
    toast.success('Survey updated successfully');
    return response.data;
  } catch (error) {
    console.error('Error updating survey:', error);
    toast.error('Failed to update survey');
    throw error;
  }
};

// Delete a survey
export const deleteSurvey = async (surveyId: string) => {
  try {
    const response = await myApi.delete(`/surveys/${surveyId}`);
    toast.success('Survey deleted successfully');
    return response.data;
  } catch (error) {
    console.error('Error deleting survey:', error);
    toast.error('Failed to delete survey');
    throw error;
  }
};

// Publish a survey
export const publishSurvey = async (surveyId: string) => {
  try {
    const response = await myApi.post(`/surveys/${surveyId}/publish`);
    toast.success('Survey published successfully');
    return response.data;
  } catch (error) {
    console.error('Error publishing survey:', error);
    toast.error('Failed to publish survey');
    throw error;
  }
};

// Close a survey
export const closeSurvey = async (surveyId: string) => {
  try {
    const response = await myApi.post(`/surveys/${surveyId}/close`);
    toast.success('Survey closed successfully');
    return response.data;
  } catch (error) {
    console.error('Error closing survey:', error);
    toast.error('Failed to close survey');
    throw error;
  }
};

// Submit survey response
export const submitSurveyResponse = async (
  surveyId: string,
  answers: Partial<SurveyAnswer>[]
) => {
  try {
    const response = await myApi.post(`/surveys/${surveyId}/responses`, {
      answers,
    });
    toast.success('Survey response submitted successfully');
    return response.data;
  } catch (error) {
    console.error('Error submitting survey response:', error);
    toast.error('Failed to submit survey response');
    throw error;
  }
};

// Save draft response
export const saveDraftResponse = async (
  surveyId: string,
  answers: Partial<SurveyAnswer>[]
) => {
  try {
    const response = await myApi.post(`/surveys/${surveyId}/responses/draft`, {
      answers,
    });
    return response.data;
  } catch (error) {
    console.error('Error saving draft response:', error);
    throw error;
  }
};

// Get draft response
export const getDraftResponse = (surveyId: string) => {
  const { data, error, isLoading, mutate } = useSWR(
    surveyId ? `/surveys/${surveyId}/responses/draft` : null
  );

  return {
    draftResponse: data?.data,
    isLoading,
    error,
    mutate,
  };
};
