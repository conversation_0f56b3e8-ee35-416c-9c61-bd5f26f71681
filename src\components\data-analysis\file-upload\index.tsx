'use client';

import React, { useRef, useState } from 'react';
import { Button } from '@/components/ui/button';
import { Upload, X, FileSpreadsheet, AlertCircle } from 'lucide-react';
import { cn } from '@/lib/utils';

interface FileUploadProps {
  onFileChange: (file: File | null) => void;
  accept?: string;
  maxSize?: number; // in MB
}

const FileUpload: React.FC<FileUploadProps> = ({
  onFileChange,
  accept = '.csv,.xlsx,.xls',
  maxSize = 10, // Default max size is 10MB
}) => {
  const [file, setFile] = useState<File | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isDragging, setIsDragging] = useState(false);
  const fileInputRef = useRef<HTMLInputElement | null>(null);

  const validateFile = (file: File): boolean => {
    // Check file type
    const fileType = file.name.split('.').pop()?.toLowerCase();
    const validTypes = accept
      .split(',')
      .map((type) => (type.startsWith('.') ? type.substring(1) : type));

    if (!fileType || !validTypes.includes(fileType)) {
      setError(`Invalid file type. Accepted types: ${accept}`);
      return false;
    }

    // Check file size
    const fileSizeInMB = file.size / (1024 * 1024);
    if (fileSizeInMB > maxSize) {
      setError(`File size exceeds the maximum limit of ${maxSize}MB`);
      return false;
    }

    return true;
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setError(null);

    if (e.target.files && e.target.files[0]) {
      const selectedFile = e.target.files[0];

      if (validateFile(selectedFile)) {
        setFile(selectedFile);
        onFileChange(selectedFile);
      } else {
        // Reset file input
        if (fileInputRef.current) {
          fileInputRef.current.value = '';
        }
        onFileChange(null);
      }
    }
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);
    setError(null);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      const droppedFile = e.dataTransfer.files[0];

      if (validateFile(droppedFile)) {
        setFile(droppedFile);
        onFileChange(droppedFile);
      } else {
        onFileChange(null);
      }
    }
  };

  const handleRemoveFile = () => {
    setFile(null);
    setError(null);
    onFileChange(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  return (
    <div className="w-full">
      <div
        className={cn(
          'border-2 border-dashed rounded-lg p-6 flex flex-col items-center justify-center cursor-pointer transition-colors',
          isDragging
            ? 'border-primary bg-primary/5'
            : 'border-gray-300 dark:border-gray-700 hover:border-primary/50',
          error &&
            'border-red-500 dark:border-red-500 bg-red-50 dark:bg-red-900/10'
        )}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        onClick={() => !file && fileInputRef.current?.click()}
      >
        {file ? (
          <div className="flex flex-col items-center gap-2 w-full">
            <div className="flex items-center justify-between w-full bg-gray-100 dark:bg-gray-800 p-3 rounded">
              <div className="flex items-center gap-2">
                <FileSpreadsheet className="h-8 w-8 text-primary" />
                <div className="flex flex-col">
                  <span className="text-sm font-medium truncate max-w-[200px] sm:max-w-xs">
                    {file.name}
                  </span>
                  <span className="text-xs text-gray-500">
                    {(file.size / (1024 * 1024)).toFixed(2)} MB
                  </span>
                </div>
              </div>
              <Button
                variant="ghost"
                size="icon"
                onClick={(e) => {
                  e.stopPropagation();
                  handleRemoveFile();
                }}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </div>
        ) : (
          <div className="flex flex-col items-center gap-2 text-center">
            <Upload className="h-10 w-10 text-gray-400 dark:text-gray-500 mb-2" />
            <p className="text-sm font-medium">
              Drag and drop your file here or click to browse
            </p>
            <p className="text-xs text-gray-500 dark:text-gray-400">
              Supported formats: CSV, XLSX, XLS (max. {maxSize}MB)
            </p>
            {error && (
              <div className="flex items-center gap-1 text-red-500 mt-2">
                <AlertCircle className="h-4 w-4" />
                <p className="text-xs">{error}</p>
              </div>
            )}
          </div>
        )}
        <input
          type="file"
          ref={fileInputRef}
          accept={accept}
          className="hidden"
          onChange={handleFileChange}
        />
      </div>
    </div>
  );
};

export default FileUpload;
