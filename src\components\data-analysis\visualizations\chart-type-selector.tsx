'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Donut,
  <PERSON><PERSON><PERSON>,
} from 'lucide-react';
import {
  Toolt<PERSON>,
  Toolt<PERSON>Content,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';

export type ChartType =
  // Categorical data charts
  | 'pie'
  | 'bar'
  | 'donut'
  // Numerical data charts
  | 'bar-numeric'
  | 'line'
  | 'area'
  | 'scatter'
  // Date data charts
  | 'composite';

interface ChartTypeSelectorProps {
  dataType: 'categorical' | 'numeric' | 'date';
  selectedType: ChartType;
  onSelectType: (type: ChartType) => void;
}

export const ChartTypeSelector: React.FC<ChartTypeSelectorProps> = ({
  dataType,
  selectedType,
  onSelectType,
}) => {
  return (
    <TooltipProvider>
      <div className="flex items-center gap-1 bg-gray-100 dark:bg-gray-800 rounded-md p-1">
        {dataType === 'categorical' ? (
          <>
            <ChartButton
              icon={<PieChartIcon className="h-4 w-4" />}
              type="pie"
              label="Pie Chart"
              isSelected={selectedType === 'pie'}
              onClick={() => onSelectType('pie')}
            />
            <ChartButton
              icon={<BarChart3 className="h-4 w-4" />}
              type="bar"
              label="Bar Chart"
              isSelected={selectedType === 'bar'}
              onClick={() => onSelectType('bar')}
            />
            <ChartButton
              icon={<Donut className="h-4 w-4" />}
              type="donut"
              label="Donut Chart"
              isSelected={selectedType === 'donut'}
              onClick={() => onSelectType('donut')}
            />
          </>
        ) : dataType === 'numeric' ? (
          <>
            <ChartButton
              icon={<BarChart3 className="h-4 w-4" />}
              type="bar-numeric"
              label="Bar Chart"
              isSelected={selectedType === 'bar-numeric'}
              onClick={() => onSelectType('bar-numeric')}
            />
            <ChartButton
              icon={<LineChart className="h-4 w-4" />}
              type="line"
              label="Line Chart"
              isSelected={selectedType === 'line'}
              onClick={() => onSelectType('line')}
            />
            <ChartButton
              icon={<AreaChart className="h-4 w-4" />}
              type="area"
              label="Area Chart"
              isSelected={selectedType === 'area'}
              onClick={() => onSelectType('area')}
            />
            <ChartButton
              icon={<ScatterChart className="h-4 w-4" />}
              type="scatter"
              label="Scatter Plot"
              isSelected={selectedType === 'scatter'}
              onClick={() => onSelectType('scatter')}
            />
          </>
        ) : (
          // Date data type
          <>
            <ChartButton
              icon={<LineChart className="h-4 w-4" />}
              type="line"
              label="Line Chart"
              isSelected={selectedType === 'line'}
              onClick={() => onSelectType('line')}
            />
            <ChartButton
              icon={<BarChart3 className="h-4 w-4" />}
              type="bar-numeric"
              label="Bar Chart"
              isSelected={selectedType === 'bar-numeric'}
              onClick={() => onSelectType('bar-numeric')}
            />
            <ChartButton
              icon={<AreaChart className="h-4 w-4" />}
              type="area"
              label="Area Chart"
              isSelected={selectedType === 'area'}
              onClick={() => onSelectType('area')}
            />
            <ChartButton
              icon={<BarChart3 className="h-4 w-4" />}
              type="composite"
              label="Composite Chart"
              isSelected={selectedType === 'composite'}
              onClick={() => onSelectType('composite')}
            />
          </>
        )}
      </div>
    </TooltipProvider>
  );
};

interface ChartButtonProps {
  icon: React.ReactNode;
  type: ChartType;
  label: string;
  isSelected: boolean;
  onClick: () => void;
}

const ChartButton: React.FC<ChartButtonProps> = ({
  icon,
  type,
  label,
  isSelected,
  onClick,
}) => {
  return (
    <Tooltip>
      <TooltipTrigger asChild>
        <Button
          variant={isSelected ? 'default' : 'ghost'}
          size="icon"
          className="h-8 w-8"
          onClick={onClick}
        >
          {icon}
        </Button>
      </TooltipTrigger>
      <TooltipContent>
        <p>{label}</p>
      </TooltipContent>
    </Tooltip>
  );
};

export default ChartTypeSelector;
