// 'use client';

// import React, { useState } from 'react';
// import { Modal } from '@/components/common/modal';
// import ImageUpload from '@/components/common/image';
// import { Button } from '@/components/ui/button';

// interface ImageEditModalProps {
//   open: boolean;
//   setOpen: (open: boolean) => void;
//   onImageChange: (file: File | null) => void;
//   onPreviewChange: (preview: string | null) => void;
//   initialPreview: string | null;
//   currentImage?: string;
// }

// const ImageEditModal: React.FC<ImageEditModalProps> = ({
//   open,
//   setOpen,
//   onImageChange,
//   onPreviewChange,
//   initialPreview,
//   currentImage,
// }) => {
//   const [tempImage, setTempImage] = useState<File | null>(null);
//   const [tempPreview, setTempPreview] = useState<string | null>(
//     currentImage || initialPreview
//   );
//   const [isLoading, setIsLoading] = useState(false);

//   const handleImageChange = (file: File | null) => {
//     setTempImage(file);
//   };

//   const handlePreviewChange = (preview: string | null) => {
//     setTempPreview(preview);
//   };

//   const handleSave = () => {
//     setIsLoading(true);
//     try {
//       // Update the parent component with the new image
//       onImageChange(tempImage);
//       onPreviewChange(tempPreview);
//       setOpen(false);
//     } catch (error) {
//       console.error('Error saving image:', error);
//     } finally {
//       setIsLoading(false);
//     }
//   };

//   return (
//     <Modal
//       open={open}
//       setOpen={setOpen}
//       title="Edit Package Image"
//       description="Upload a new image for this package"
//       isLoading={isLoading}
//     >
//       <div className="space-y-4">
//         <div className="space-y-2">
//           {currentImage && !tempPreview && (
//             <div className="mb-4">
//               <p className="text-sm text-muted-foreground mb-2">
//                 Current Image:
//               </p>
//               <img
//                 src={currentImage}
//                 alt="Current package"
//                 className="w-full max-h-48 object-contain rounded-md border"
//               />
//             </div>
//           )}
//           <ImageUpload
//             onImageChange={handleImageChange}
//             onPreviewChange={handlePreviewChange}
//             initialPreview={tempPreview}
//           />
//         </div>
//         <div className="flex justify-end gap-2 mt-4">
//           <Button
//             variant="outline"
//             onClick={() => setOpen(false)}
//             type="button"
//           >
//             Cancel
//           </Button>
//           <Button
//             onClick={handleSave}
//             type="button"
//             disabled={!tempImage && !currentImage}
//           >
//             Save Changes
//           </Button>
//         </div>
//       </div>
//     </Modal>
//   );
// };

// export default ImageEditModal;
