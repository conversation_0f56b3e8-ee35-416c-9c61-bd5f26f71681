import { useState, useEffect } from 'react';

const useMediaQuery = (query: string) => {
  const [matches, setMatches] = useState(false);

  useEffect(() => {
    const mediaQueryList = window.matchMedia(query);
    const updateMatches = () => setMatches(mediaQueryList.matches);

    // Check the initial state
    updateMatches();

    // Add event listener to update state when viewport changes
    mediaQueryList.addEventListener('change', updateMatches);

    // Cleanup on unmount
    return () => mediaQueryList.removeEventListener('change', updateMatches);
  }, [query]);

  return matches;
};
export default useMediaQuery;
