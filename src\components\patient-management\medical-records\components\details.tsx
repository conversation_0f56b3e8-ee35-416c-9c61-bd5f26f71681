import React from 'react';
import { Modal } from '@/components/common/modal';
import { ModalProps } from '@/components/crm/types';
import dayjs from 'dayjs';
import { Button } from '@/components/ui/button';

const Details: React.FC<ModalProps> = ({ open, setOpen, data }) => {
  if (!data) return null;

  return (
    <Modal
      open={open}
      setOpen={setOpen}
      title="Medical Record Details"
      description={`${data.recordType} - ${dayjs(data.recordDate).format('MMMM D, YYYY')}`}
      size="lg"
    >
      <div className="space-y-6">
        <div>
          <h3 className="text-xs font-medium mb-2 text-gray-500 uppercase">
            Patient Information
          </h3>
          <div className="grid sm:grid-cols-2 gap-4 text-sm">
            <div>
              <p className="text-gray-500 dark:text-gray-400">Patient Name</p>
              <p className="font-medium">{data.patientName}</p>
            </div>
            <div>
              <p className="text-gray-500 dark:text-gray-400">Patient ID</p>
              <p className="font-medium">
                P-{String(data.patientId).padStart(6, '0')}
              </p>
            </div>
          </div>
        </div>

        <div>
          <h3 className="text-xs font-medium mb-2 text-gray-500 uppercase">
            Record Details
          </h3>
          <div className="grid sm:grid-cols-2 gap-4 text-sm">
            <div>
              <p className="text-gray-500 dark:text-gray-400">Record Type</p>
              <p className="font-medium capitalize">{data.recordType}</p>
            </div>
            <div>
              <p className="text-gray-500 dark:text-gray-400">Record Date</p>
              <p className="font-medium">
                {dayjs(data.recordDate).format('MMMM D, YYYY')}
              </p>
            </div>
            <div>
              <p className="text-gray-500 dark:text-gray-400">Doctor</p>
              <p className="font-medium">{data.doctorName}</p>
            </div>
          </div>
        </div>

        {data.diagnosis && (
          <div>
            <h3 className="text-xs font-medium mb-2 text-gray-500 uppercase">
              Diagnosis
            </h3>
            <p className="text-sm whitespace-pre-wrap">{data.diagnosis}</p>
          </div>
        )}

        {data.treatment && (
          <div>
            <h3 className="text-xs font-medium mb-2 text-gray-500 uppercase">
              Treatment
            </h3>
            <p className="text-sm whitespace-pre-wrap">{data.treatment}</p>
          </div>
        )}

        {data.notes && (
          <div>
            <h3 className="text-xs font-medium mb-2 text-gray-500 uppercase">
              Notes
            </h3>
            <p className="text-sm whitespace-pre-wrap">{data.notes}</p>
          </div>
        )}

        <div className="flex justify-end gap-2 mt-4">
          <Button variant="outline" onClick={() => setOpen(false)}>
            Close
          </Button>
        </div>
      </div>
    </Modal>
  );
};

export default Details;
