'use client';

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useState, useEffect } from 'react';

interface YearSelectProps {
  placeholder?: string;
  className?: string;
  onChange?: (value: string) => void;
}

const YearSelect: React.FC<YearSelectProps> = ({
  placeholder = 'Select a year',
  className = 'w-[130px]',
  onChange,
}) => {
  const startYear = 2025;
  const currentYear = new Date().getFullYear();
  const years = Array.from(
    { length: currentYear - startYear + 1 },
    (_, i) => startYear + i
  );

  const [selectedYear, setSelectedYear] = useState<string>(
    currentYear.toString()
  );

  useEffect(() => {
    // Notify parent on mount with default value (optional)
    if (onChange) {
      onChange(currentYear.toString());
    }
  }, [onChange, currentYear]);

  const handleChange = (value: string) => {
    setSelectedYear(value);
    onChange?.(value);
  };

  return (
    <Select onValueChange={handleChange} value={selectedYear}>
      <SelectTrigger className={className}>
        <SelectValue placeholder={placeholder} />
      </SelectTrigger>
      <SelectContent>
        {years.map((year) => (
          <SelectItem key={year} value={year.toString()}>
            {year}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
};

export default YearSelect;
