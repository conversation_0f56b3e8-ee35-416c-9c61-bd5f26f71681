/**
 * Notification utility for the application
 */

// Check if the browser supports notifications
export const isNotificationSupported = (): boolean => {
  return typeof window !== 'undefined' && 'Notification' in window;
};

// Send a notification
export const sendNotification = (
  title: string,
  options: NotificationOptions = {}
): Notification | null => {
  if (!isNotificationSupported()) {
    console.warn('Notifications are not supported in this browser');
    return null;
  }

  if (Notification.permission !== 'granted') {
    console.warn('Notification permission not granted');
    return null;
  }

  try {
    return new Notification(title, options);
  } catch (error) {
    console.error('Error creating notification:', error);
    return null;
  }
};

// Register the service worker for PWA
export const registerServiceWorker =
  async (): Promise<ServiceWorkerRegistration | null> => {
    if (typeof window !== 'undefined' && 'serviceWorker' in navigator) {
      try {
        const registration = await navigator.serviceWorker.register('/sw.js');
        console.log(
          'Service Worker registered with scope:',
          registration.scope
        );
        return registration;
      } catch (error) {
        console.error('Service Worker registration failed:', error);
        return null;
      }
    }

    console.warn('Service Workers are not supported in this browser');
    return null;
  };
