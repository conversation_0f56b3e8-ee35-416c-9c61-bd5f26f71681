import { z } from 'zod';

// Patient form validation schema
export const patientFormSchema = z.object({
  title: z.string().optional(),
  firstName: z.string().min(1, { message: 'First name is required' }),
  lastName: z.string().min(1, { message: 'Last name is required' }),
  emailAddress: z
    .string()
    .email({ message: 'Please enter a valid email address' }),
  phoneNumber: z.string().min(1, { message: 'Phone number is required' }),
  address: z.string().optional(),
  city: z.string().optional(),
  state: z.string().optional(),
  zipCode: z.string().optional(),
  dateOfBirth: z.string().optional(),
  gender: z.string().optional(),
  bloodType: z.string().optional(),
  allergies: z.string().optional(),
  medicalHistory: z.string().optional(),
  emergencyContact: z.string().optional(),
  emergencyContactPhone: z.string().optional(),
  insuranceProvider: z.string().optional(),
  insuranceNumber: z.string().optional(),
  notes: z.string().optional(),
});

export type PatientFormValues = z.infer<typeof patientFormSchema>;

// For backward compatibility with old CRM components
export const customerFormSchema = patientFormSchema;
export type CustomerFormValues = PatientFormValues;

// Doctor form validation schema
export const doctorFormSchema = z.object({
  name: z.string().min(1, { message: 'Name is required' }),
  email: z.string().email({ message: 'Please enter a valid email address' }),
  phoneNumber: z.string().min(1, { message: 'Phone number is required' }),
  department: z.string().min(1, { message: 'Department is required' }),
  specialization: z.string().min(1, { message: 'Specialization is required' }),
  qualifications: z.string().optional(),
  experience: z.string().optional(),
  bio: z.string().optional(),
  isAvailable: z.boolean().default(true),
  consultationFee: z.string().optional(),
});

export type DoctorFormValues = z.infer<typeof doctorFormSchema>;

// Department form validation schema
export const departmentFormSchema = z.object({
  name: z.string().min(1, { message: 'Department name is required' }),
  description: z.string().optional(),
  headOfDepartmentId: z.string().optional(),
  isActive: z.boolean().default(true),
  location: z.string().optional(),
  contactEmail: z
    .string()
    .email({ message: 'Please enter a valid email address' })
    .optional()
    .or(z.literal('')),
  contactPhone: z.string().optional(),
});

export type DepartmentFormValues = z.infer<typeof departmentFormSchema>;

// Patient interaction form validation schema for communication channels
export const patientInteractionFormSchema = z.object({
  patientId: z.number().or(z.string().transform((val) => Number(val))),
  channel: z.enum([
    'whatsapp',
    'email',
    'sms',
    'phone',
    'video',
    'instagram',
    'twitter',
    'facebook',
    'other',
  ]),
  subject: z.string().min(1, { message: 'Subject is required' }),
  content: z.string().min(1, { message: 'Content is required' }),
  status: z.enum(['pending', 'completed']),
  direction: z.enum(['inbound', 'outbound']),
  contactInfo: z.string().optional(),
  tags: z.string().optional(),
  followUpRequired: z.boolean().default(false),
  followUpDate: z.string().optional(),
  parentInteractionId: z.number().optional(),
});

export type PatientInteractionFormValues = z.infer<
  typeof patientInteractionFormSchema
>;

// Patient interaction form validation schema
export const interactionFormSchema = z.object({
  patientId: z.number().or(z.string().transform((val) => Number(val))),
  type: z.enum([
    'appointment',
    'call',
    'email',
    'lab_result',
    'prescription',
    'other',
  ]),
  subject: z.string().min(1, { message: 'Subject is required' }),
  description: z.string().min(1, { message: 'Description is required' }),
  status: z.enum(['scheduled', 'completed', 'cancelled', 'no_show']),
  priority: z.enum(['routine', 'urgent', 'emergency']),
  appointmentDate: z.string().optional(),
  department: z.string().optional(),
  doctorId: z
    .number()
    .optional()
    .or(
      z
        .string()
        .transform((val) => (val ? Number(val) : undefined))
        .optional()
    ),
  symptoms: z.string().optional(),
  diagnosis: z.string().optional(),
  treatment: z.string().optional(),
  followUpRequired: z.boolean().optional(),
  followUpDate: z.string().optional(),
});

export type InteractionFormValues = z.infer<typeof interactionFormSchema>;
