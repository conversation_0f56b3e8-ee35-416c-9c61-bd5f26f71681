import React, { useState } from 'react';
import { InputField, InputTextArea } from '@/components/common/form';
import { Form } from '@/components/ui/form';
import { Modal } from '@/components/common/modal';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { myApi } from '@/api/fetcher';
import { toast } from 'sonner';
import { ModalProps } from '@/components/types';
import {
  IssueEntry,
  IssueIItemFormSchema,
} from '@/components/validations/inventory';

const IssueModal: React.FC<ModalProps> = ({
  setOpen,
  open,
  props,
  profile,
  mutate,
}) => {
  const [isLoading, setIsLoading] = useState(false);

  const form = useForm<IssueEntry>({
    resolver: zodResolver(IssueIItemFormSchema),
    defaultValues: {
      quantity: '',
      comment: '',
    },
  });

  const onSubmit = async (data: any) => {
    try {
      setIsLoading(true);
      const res = await myApi.post('/cafeteria/inventory/issue-stock', {
        quantity: data.quantity,
        comment: data.comment,
        inventoryItemId: props?.id,
        issuedBy: profile?.fullName,
      });
      setIsLoading(false);
      if (res.status === 200) {
        toast.success(res.data.data.message);
        if (mutate) {
          mutate();
        }
        setOpen(false);
        form.reset();
      }
    } catch (error) {
      setIsLoading(false);
    }
  };

  return (
    <Modal
      open={open}
      setOpen={setOpen}
      title="Issue Item"
      description={`Issue ${props?.itemName || ''} from inventory`}
      isLoading={isLoading}
      onSubmit={form.handleSubmit(onSubmit)}
    >
      <Form {...form}>
        <form className="space-y-4">
          <div className="grid gap-4 py-4">
            <InputField
              control={form.control}
              name="quantity"
              label="Quantity"
              type="number"
              placeholder="Quantity to issue"
            />
            <InputTextArea
              control={form.control}
              name="comment"
              label="Comment"
              placeholder="E.g 2 crates issued to kitchen"
            />
          </div>
        </form>
      </Form>
    </Modal>
  );
};

export default IssueModal;
