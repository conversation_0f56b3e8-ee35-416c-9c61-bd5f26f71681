// components/ui/SheetComponent.tsx
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Sheet,
  SheetClose,
  SheetContent,
  SheetDescription,
  She<PERSON><PERSON>ooter,
  She<PERSON><PERSON>eader,
  She<PERSON><PERSON><PERSON>le,
  She<PERSON><PERSON>rigger,
} from '@/components/ui/sheet';

// Define the props for the reusable SheetComponent
interface SheetComponentProps {
  triggerText: string;
  title: string;
  description: string;
  fields: { label: string; id: string; value: string }[];
  onSave: () => void;
}

export function SheetComponent({
  triggerText,
  title,
  description,
  fields,
  onSave,
}: SheetComponentProps) {
  return (
    <Sheet>
      <SheetTrigger asChild>
        <Button>{triggerText}</Button>
      </SheetTrigger>
      <SheetContent side="bottom">
        <SheetHeader>
          <SheetTitle>{title}</SheetTitle>
          <SheetDescription>{description}</SheetDescription>
        </SheetHeader>
        <div className="grid gap-4 py-4">
          {fields.map((field) => (
            <div className="grid grid-cols-4 items-center gap-4" key={field.id}>
              <Label htmlFor={field.id} className="text-right">
                {field.label}
              </Label>
              <Input id={field.id} value={field.value} className="col-span-3" />
            </div>
          ))}
        </div>
        <SheetFooter>
          <SheetClose asChild>
            <Button type="button" onClick={onSave}>
              Save changes
            </Button>
          </SheetClose>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  );
}
