'use client';

import { useFormContext } from 'react-hook-form';
import { Input } from '@/components/ui/input';

export default function StepIndemnity() {
  const { register } = useFormContext();

  return (
    <div className="space-y-6">
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-foreground border-b pb-2">
          Medical Indemnity
        </h3>
        <div className="space-y-4">
          <div className="space-y-2">
            <label className="text-sm font-medium text-foreground">
              Are you a member of a medical defence organisation?
            </label>
            <select
              {...register('indemnity.member')}
              className="flex h-10 w-full rounded-sm border border-input bg-transparent px-3 py-1 text-sm shadow-xs"
            >
              <option value="">Select</option>
              <option>Yes</option>
              <option>No</option>
            </select>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Input
              placeholder="Organisation Name"
              {...register('indemnity.organisation')}
            />
            <Input
              placeholder="Indemnity Reference / Certificate No"
              {...register('indemnity.reference')}
            />
          </div>
        </div>
      </div>

      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-foreground border-b pb-2">
          Declarations
        </h3>
        <p className="text-sm text-muted-foreground">
          Please provide details if applicable, or write "None" if not
          applicable.
        </p>
        <div className="space-y-4">
          <div className="space-y-2">
            <label className="text-sm font-medium text-foreground">
              Financial interests / conflicts of interest
            </label>
            <textarea
              {...register('declarations.financialInterests')}
              className="flex min-h-[80px] w-full rounded-sm border border-input bg-transparent px-3 py-2 text-sm shadow-xs placeholder:text-muted-foreground/40"
            />
          </div>
          <div className="space-y-2">
            <label className="text-sm font-medium text-foreground">
              Police investigations / criminal offences
            </label>
            <textarea
              {...register('declarations.police')}
              className="flex min-h-[80px] w-full rounded-sm border border-input bg-transparent px-3 py-2 text-sm shadow-xs placeholder:text-muted-foreground/40"
            />
          </div>
          <div className="space-y-2">
            <label className="text-sm font-medium text-foreground">
              Regulatory body investigations
            </label>
            <textarea
              {...register('declarations.regulatory')}
              className="flex min-h-[80px] w-full rounded-sm border border-input bg-transparent px-3 py-2 text-sm shadow-xs placeholder:text-muted-foreground/40"
            />
          </div>
          <div className="space-y-2">
            <label className="text-sm font-medium text-foreground">
              Conditions/restrictions/suspension
            </label>
            <textarea
              {...register('declarations.conditions')}
              className="flex min-h-[80px] w-full rounded-sm border border-input bg-transparent px-3 py-2 text-sm shadow-xs placeholder:text-muted-foreground/40"
            />
          </div>
          <div className="space-y-2">
            <label className="text-sm font-medium text-foreground">
              Disqualification/limitations on practice
            </label>
            <textarea
              {...register('declarations.disqualification')}
              className="flex min-h-[80px] w-full rounded-sm border border-input bg-transparent px-3 py-2 text-sm shadow-xs placeholder:text-muted-foreground/40"
            />
          </div>
        </div>
      </div>

      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-foreground border-b pb-2">
          Confirmation
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Input placeholder="Full Signature" {...register('signature')} />
          <Input placeholder="Initials" {...register('shortSignature')} />
        </div>
        <div className="space-y-2">
          <label className="text-sm font-medium text-foreground">Date</label>
          <Input type="date" {...register('signatureDate')} />
        </div>
      </div>
    </div>
  );
}
