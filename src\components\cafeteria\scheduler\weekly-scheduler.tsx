// 'use client';

// import { useState } from 'react';
// import { Button } from '@/components/ui/button';
// import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
// import {
//   Select,
//   SelectContent,
//   SelectItem,
//   SelectTrigger,
//   SelectValue,
// } from '@/components/ui/select';
// import { Badge } from '@/components/ui/badge';
// import { Save, X } from 'lucide-react';
// import { toast } from 'sonner';
// import { fetcher } from '@/api/fetcher';

// interface WeeklySchedulerProps {
//   weeklyMenus: any;
//   availableMenus: any[];
//   selectedWeek: string;
//   mutate: () => void;
// }

// const DAYS = [
//   'Monday',
//   'Tuesday',
//   'Wednesday',
//   'Thursday',
//   'Friday',
//   'Saturday',
//   'Sunday',
// ];
// const MEAL_TYPES = ['Breakfast', 'Lunch', 'Dinner'];

// export default function WeeklyScheduler({
//   weeklyMenus,
//   availableMenus,
//   selectedWeek,
//   mutate,
// }: WeeklySchedulerProps) {
//   const [schedule, setSchedule] = useState(weeklyMenus || {});
//   const [saving, setSaving] = useState(false);

//   const handleMenuSelect = (day: string, mealType: string, menuId: string) => {
//     const menu = availableMenus.find((m) => m.id === menuId);
//     if (!menu) return;

//     setSchedule((prev: any) => ({
//       ...prev,
//       [day]: {
//         ...prev[day],
//         [mealType]: [...(prev[day]?.[mealType] || []), menu],
//       },
//     }));
//   };

//   const removeMenu = (day: string, mealType: string, menuId: string) => {
//     setSchedule((prev: any) => ({
//       ...prev,
//       [day]: {
//         ...prev[day],
//         [mealType]:
//           prev[day]?.[mealType]?.filter((m: any) => m.id !== menuId) || [],
//       },
//     }));
//   };

//   const saveSchedule = async () => {
//     setSaving(true);
//     try {
//       await fetcher('/cafeteria/menu/weekly', {
//         method: 'POST',
//         body: JSON.stringify({ week: selectedWeek, schedule }),
//       });
//       toast.success('Weekly menu schedule saved successfully');
//       mutate();
//     } catch (error) {
//       toast.error('Failed to save schedule');
//     } finally {
//       setSaving(false);
//     }
//   };

//   return (
//     <div className="space-y-6">
//       <div className="flex justify-end">
//         <Button onClick={saveSchedule} disabled={saving}>
//           <Save className="h-4 w-4 mr-2" />
//           {saving ? 'Saving...' : 'Save Schedule'}
//         </Button>
//       </div>

//       <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-4">
//         {DAYS.map((day) => (
//           <Card key={day}>
//             <CardHeader>
//               <CardTitle className="text-lg">{day}</CardTitle>
//             </CardHeader>
//             <CardContent className="space-y-4">
//               {MEAL_TYPES.map((mealType) => (
//                 <div key={mealType} className="space-y-2">
//                   <div className="flex items-center justify-between">
//                     <h4 className="font-medium text-sm">{mealType}</h4>
//                     <Select
//                       onValueChange={(value) =>
//                         handleMenuSelect(day, mealType, value)
//                       }
//                     >
//                       <SelectTrigger className="w-32 h-8">
//                         <SelectValue placeholder="Add menu" />
//                       </SelectTrigger>
//                       <SelectContent>
//                         {availableMenus.map((menu) => (
//                           <SelectItem key={menu.id} value={menu.id}>
//                             {menu.name}
//                           </SelectItem>
//                         ))}
//                       </SelectContent>
//                     </Select>
//                   </div>
//                   <div className="space-y-1">
//                     {schedule[day]?.[mealType]?.map((menu: any) => (
//                       <div
//                         key={menu.id}
//                         className="flex items-center justify-between bg-muted p-2 rounded"
//                       >
//                         <span className="text-sm">{menu.name}</span>
//                         <Button
//                           variant="ghost"
//                           size="sm"
//                           onClick={() => removeMenu(day, mealType, menu.id)}
//                         >
//                           <X className="h-3 w-3" />
//                         </Button>
//                       </div>
//                     ))}
//                   </div>
//                 </div>
//               ))}
//             </CardContent>
//           </Card>
//         ))}
//       </div>
//     </div>
//   );
// }
