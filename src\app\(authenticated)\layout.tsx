import Layout from '@/components/common/layout';
import { NotificationPermission } from '@/components/notification-permission';
import { PWAInstallPrompt } from '@/components/auth/pwa-install-prompt';
import WebSocketProvider from '@/Providers/WebSocketProvider';

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <WebSocketProvider>
      <Layout>
        <main>{children}</main>
        <NotificationPermission />
        <PWAInstallPrompt />
      </Layout>
    </WebSocketProvider>
  );
}
