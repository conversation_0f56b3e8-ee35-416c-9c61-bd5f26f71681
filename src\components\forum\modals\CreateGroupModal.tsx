import React, { useState } from 'react';
import { Modal } from '@/components/common/modal';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { createForumGroup } from '@/api/forum/data';
import { GetStaffList } from '@/api/staff';
import { toast } from 'sonner';
import { X, Users, Hash, Lock, Globe } from 'lucide-react';

interface CreateGroupModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export const CreateGroupModal: React.FC<CreateGroupModalProps> = ({
  isOpen,
  onClose,
}) => {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    type: 'public' as 'public' | 'private' | 'channel',
    allowMemberInvites: true,
    allowFileSharing: true,
    allowReactions: true,
    allowThreads: true,
    moderationEnabled: false,
  });
  const [selectedMembers, setSelectedMembers] = useState<string[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const { staffList } = GetStaffList();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.name.trim()) {
      toast.error('Group name is required');
      return;
    }

    setIsSubmitting(true);

    try {
      const groupData = {
        ...formData,
        settings: {
          allowMemberInvites: formData.allowMemberInvites,
          allowFileSharing: formData.allowFileSharing,
          allowReactions: formData.allowReactions,
          allowThreads: formData.allowThreads,
          moderationEnabled: formData.moderationEnabled,
        },
        memberIds: selectedMembers,
      };

      await createForumGroup(groupData);
      toast.success('Group created successfully');
      resetForm();
      onClose();
    } catch (error) {
      console.error('Error creating group:', error);
      toast.error('Failed to create group');
    } finally {
      setIsSubmitting(false);
    }
  };

  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
      type: 'public',
      allowMemberInvites: true,
      allowFileSharing: true,
      allowReactions: true,
      allowThreads: true,
      moderationEnabled: false,
    });
    setSelectedMembers([]);
  };

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSwitchChange = (name: string, checked: boolean) => {
    setFormData((prev) => ({ ...prev, [name]: checked }));
  };

  const handleMemberToggle = (memberId: string) => {
    setSelectedMembers((prev) =>
      prev.includes(memberId)
        ? prev.filter((id) => id !== memberId)
        : [...prev, memberId]
    );
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'public':
        return <Globe className="h-4 w-4" />;
      case 'private':
        return <Lock className="h-4 w-4" />;
      case 'channel':
        return <Hash className="h-4 w-4" />;
      default:
        return <Users className="h-4 w-4" />;
    }
  };

  const handleModalSubmit = () => {
    // Trigger form submission
    const form = document.getElementById('group-form') as HTMLFormElement;
    if (form) {
      form.requestSubmit();
    }
  };

  return (
    <Modal
      open={isOpen}
      setOpen={onClose}
      title="Create New Group"
      description="Create a new forum group with custom settings and permissions"
      size="lg"
      onSubmit={handleModalSubmit}
      isLoading={isSubmitting}
    >
      <form id="group-form" onSubmit={handleSubmit}>
        <div className="grid gap-6 py-4">
          {/* Basic Information */}
          <div className="space-y-4">
            <h3 className="text-sm font-medium">Basic Information</h3>

            <div className="grid gap-2">
              <Label htmlFor="name">Group Name</Label>
              <Input
                id="name"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                placeholder="Enter group name"
                required
              />
            </div>

            <div className="grid gap-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                placeholder="Enter group description"
                rows={3}
              />
            </div>

            <div className="grid gap-2">
              <Label htmlFor="type">Group Type</Label>
              <Select
                value={formData.type}
                onValueChange={(value) =>
                  setFormData((prev) => ({ ...prev, type: value as any }))
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select group type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="public">
                    <div className="flex items-center gap-2">
                      <Globe className="h-4 w-4" />
                      <span>Public - Anyone can join</span>
                    </div>
                  </SelectItem>
                  <SelectItem value="private">
                    <div className="flex items-center gap-2">
                      <Lock className="h-4 w-4" />
                      <span>Private - Invite only</span>
                    </div>
                  </SelectItem>
                  <SelectItem value="channel">
                    <div className="flex items-center gap-2">
                      <Hash className="h-4 w-4" />
                      <span>Channel - Organized discussions</span>
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Group Settings */}
          <div className="space-y-4">
            <h3 className="text-sm font-medium">Group Settings</h3>

            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Allow Member Invites</Label>
                  <p className="text-xs text-muted-foreground">
                    Let members invite others to the group
                  </p>
                </div>
                <Switch
                  checked={formData.allowMemberInvites}
                  onCheckedChange={(checked) =>
                    handleSwitchChange('allowMemberInvites', checked)
                  }
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Allow File Sharing</Label>
                  <p className="text-xs text-muted-foreground">
                    Enable file and image sharing
                  </p>
                </div>
                <Switch
                  checked={formData.allowFileSharing}
                  onCheckedChange={(checked) =>
                    handleSwitchChange('allowFileSharing', checked)
                  }
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Allow Reactions</Label>
                  <p className="text-xs text-muted-foreground">
                    Enable emoji reactions on messages
                  </p>
                </div>
                <Switch
                  checked={formData.allowReactions}
                  onCheckedChange={(checked) =>
                    handleSwitchChange('allowReactions', checked)
                  }
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Allow Threads</Label>
                  <p className="text-xs text-muted-foreground">
                    Enable threaded conversations
                  </p>
                </div>
                <Switch
                  checked={formData.allowThreads}
                  onCheckedChange={(checked) =>
                    handleSwitchChange('allowThreads', checked)
                  }
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Enable Moderation</Label>
                  <p className="text-xs text-muted-foreground">
                    Require approval for new messages
                  </p>
                </div>
                <Switch
                  checked={formData.moderationEnabled}
                  onCheckedChange={(checked) =>
                    handleSwitchChange('moderationEnabled', checked)
                  }
                />
              </div>
            </div>
          </div>

          {/* Initial Members */}
          {formData.type === 'private' && (
            <div className="space-y-4">
              <h3 className="text-sm font-medium">Initial Members</h3>
              <div className="max-h-40 overflow-y-auto border rounded-md p-2">
                {staffList?.map((staff: any) => (
                  <div
                    key={staff.id}
                    className="flex items-center justify-between p-2 hover:bg-muted rounded-sm"
                  >
                    <div className="flex items-center gap-2">
                      <div className="h-6 w-6 rounded-full bg-primary/10 flex items-center justify-center">
                        <span className="text-xs font-medium">
                          {staff.name.charAt(0).toUpperCase()}
                        </span>
                      </div>
                      <span className="text-sm">{staff.name}</span>
                    </div>
                    <Switch
                      checked={selectedMembers.includes(staff.id)}
                      onCheckedChange={() => handleMemberToggle(staff.id)}
                    />
                  </div>
                ))}
              </div>
              {selectedMembers.length > 0 && (
                <div className="flex flex-wrap gap-1">
                  {selectedMembers.map((memberId) => {
                    const member = staffList?.find(
                      (s: any) => s.id === memberId
                    );
                    return member ? (
                      <Badge
                        key={memberId}
                        variant="secondary"
                        className="text-xs"
                      >
                        {member.name}
                        <X
                          className="h-3 w-3 ml-1 cursor-pointer"
                          onClick={() => handleMemberToggle(memberId)}
                        />
                      </Badge>
                    ) : null;
                  })}
                </div>
              )}
            </div>
          )}
        </div>
      </form>
    </Modal>
  );
};
