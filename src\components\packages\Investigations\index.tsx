'use client';

import React, { useState } from 'react';
import { TestTubeDiagonal, Plus } from 'lucide-react';
import { Button } from '@/components/ui/button';
import Breadcrumb from '@/components/common/breadcrumb';
import Investigation from './components/data-table';
import { hasPermission, PERMISSIONS } from '@/lib/types/permissions';

export default function InvestigationPage() {
  const [open, setOpen] = useState(false);
  const permitDelete = hasPermission(PERMISSIONS.PACKAGE_DELETE);

  const data = [
    { label: 'bookings', href: '/packages' },
    { label: 'manage-package', href: '/packages/manage-package' },
    { label: 'investigation' },
  ];

  return (
    <div className="space-y-4">
      <Breadcrumb data={data} />
      <div className="flex flex-wrap justify-between">
        <h2 className="text-lg font-bold text-gray-900 dark:text-white mb-4 text-left flex items-center gap-2">
          <TestTubeDiagonal className="w-5 h-5 text-zinc-900 dark:text-zinc-50" />
          Package Investigations
        </h2>
        <div>
          <div className="flex flex-wrap gap-2">
            {hasPermission(PERMISSIONS.PACKAGE_CREATE) && (
              <Button className="cursor-pointer" onClick={() => setOpen(true)}>
                <Plus className="w-3.5 h-3.5" /> Add Investigation
              </Button>
            )}
          </div>
        </div>
      </div>
      <div className="mt-4 dark:bg-[#0F0F12] rounded-xl p-4 flex flex-col items-start justify-start border border-gray-200 dark:border-[#1F1F23]">
        <Investigation
          permitDelete={permitDelete}
          openCreate={open}
          setOpenCreate={setOpen}
        />
      </div>
    </div>
  );
}
