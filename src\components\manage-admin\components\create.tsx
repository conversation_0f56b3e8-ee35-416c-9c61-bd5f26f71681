// import React, { useState } from 'react';
// import { InputField, PasswordField, FormRow } from '@/components/common/form';
// import { Form } from '@/components/ui/form';
// import { Modal } from '@/components/common/modal';
// import { zodResolver } from '@hookform/resolvers/zod';
// import { useForm } from 'react-hook-form';
// import { myApi } from '@/api/fetcher';
// import { toast } from 'sonner';
// import {
//   formSchema,
//   AdminFormValues,
// } from '@/components/validations/createAdmin';
// import { CustomSelectForm } from '@/components/common/another';
// import { GetRoles } from '@/api/admin/data';
// import { GetLocations } from '@/api/data';

// interface ModalProps {
//   open: boolean;
//   setOpen: React.Dispatch<React.SetStateAction<boolean>>;
//   mutate?: () => void;
// }

// const Create: React.FC<ModalProps> = ({ setOpen, mutate, open }) => {
//   const [isLoading, setIsLoading] = useState(false);

//   // Fetch roles for the dropdown
//   const { roles, roleLoading } = GetRoles();
//   const { locations } = GetLocations();

//   const data = roles?.data;
//   const locationData = locations?.data;

//   const form = useForm<AdminFormValues>({
//     resolver: zodResolver(formSchema),
//     defaultValues: {
//       fullName: '',
//       email: '',
//       password: '',
//       roleId: '',
//       designation: '',
//     },
//     mode: 'onTouched',
//   });

//   const onSubmit = async (data: AdminFormValues) => {
//     try {
//       setIsLoading(true);
//       const res = await myApi.post('/admin/create', {
//         fullName: data.fullName,
//         email: data.email,
//         password: data.password,
//         designation: data.designation,
//         roleId: parseInt(data.roleId),
//       });
//       setIsLoading(false);
//       if (res.status === 200) {
//         toast.success(res.data.message || 'Admin created successfully');
//         if (mutate) {
//           mutate();
//         }
//         setOpen(false);
//         form.reset();
//       }
//     } catch (error: any) {
//       setIsLoading(false);
//       toast.error(error?.response?.data?.message || 'An error occurred');
//     }
//   };

//   return (
//     <Modal
//       title="Create New Admin"
//       open={open}
//       setOpen={setOpen}
//       isLoading={isLoading}
//       description="Create a new admin account with the specified details"
//       onSubmit={form.handleSubmit(onSubmit)}
//     >
//       <Form {...form}>
//         <form className="space-y-4">
//           <CustomSelectForm
//             control={form.control}
//             name="roleId"
//             label="Role"
//             placeholder="Select a role"
//             options={data}
//           />
//           <InputField
//             control={form.control}
//             name="fullName"
//             label="Full Name"
//             placeholder="John Doe"
//           />
//           <InputField
//             control={form.control}
//             name="email"
//             label="Email"
//             placeholder="<EMAIL>"
//             type="email"
//           />
//           <PasswordField
//             control={form.control}
//             name="password"
//             label="Password"
//             placeholder="********"
//           />
//           <FormRow>
//             <CustomSelectForm
//               control={form.control}
//               name="roleId"
//               label="Role"
//               placeholder="Select a role"
//               options={data}
//             />
//             <InputField
//               control={form.control}
//               name="designation"
//               label="Designation"
//               placeholder="Operations Manager"
//               type="email"
//             />
//           </FormRow>
//         </form>
//       </Form>
//     </Modal>
//   );
// };

// export default Create;
