import useSWR from 'swr';
import { useAuthSWR } from '../useAuthSWR';

export const GetAllInventory = (params: string) => {
  const qs = new URLSearchParams(params);

  const { data, error, isLoading, mutate } = useSWR(
    `/cafeteria/inventory/list?${qs.toString()}`
  );

  return {
    inventory: data,
    inventoryLoading: isLoading,
    mutate: mutate,
  };
};

export const GetAllInventoryCat = () => {
  const { data, isLoading } = useAuthSWR(`/cafeteria/inventory/list-category`);

  return {
    category: data,
    categoryLoading: isLoading,
  };
};

export const GetInventorySupplies = (slug: any) => {
  const { data, error, isLoading } = useAuthSWR(
    `/cafeteria/inventory/${slug}/supplies`
  );

  return {
    suppliesData: data,
    suppliesLoading: isLoading,
  };
};

export const GetIssuedStock = (params: string) => {
  const qs = new URLSearchParams(params);

  const { data, error, isLoading, mutate } = useSWR(
    `/cafeteria/inventory/issued-stock?${qs.toString()}`
  );

  return {
    stock: data,
    stockLoading: isLoading,
    mutate: mutate,
  };
};

export const GetSpecialty = () => {
  const { data, isLoading, mutate } = useSWR(`/staff/list-specialty`);

  return {
    specialty: data,
    specialtyLoading: isLoading,
    mutate: mutate,
  };
};
