import React, { useState, useEffect } from 'react';
import { useSearchAndPagination } from '@/hooks/useSearchAndPagination';
import Pagination from '@/components/common/pagination';
import dayjs from 'dayjs';
import { Loader2, Ellipsis, Search } from 'lucide-react';
import { StatusBadge } from '@/components/common/status-badge';
import { GetStaffList } from '@/api/reward/data';
import { EmptyState, LoadingState } from '@/components/common/dataState';
import { Input } from '@/components/ui/input';
import DateRangeFilter from '@/components/common/date-range-filter';
import { Badge } from '../ui/badge';

import Details from './details';
import CreateStaff from './create';

const Staff = () => {
  const [open, setOpen] = useState(false);
  const [openCreate, setOpenCreate] = useState(false);

  const {
    searchTerm,
    debouncedSearchTerm,
    handleSearchChange,
    currentPage,
    pageSize,
    handlePageChange,
    queryParam,
    setQueryParam,
  } = useSearchAndPagination({ initialPageSize: 10 });

  const [detail, setDetail] = useState<any | null>(null);
  const [startDate, setStartDate] = useState<Date | undefined>(undefined);
  const [endDate, setEndDate] = useState<Date | undefined>(undefined);

  // Update query parameters when search term or date range changes
  useEffect(() => {
    let params = [];

    if (debouncedSearchTerm) {
      params.push(`search=${debouncedSearchTerm}`);
    }

    if (startDate) {
      const formattedStartDate = dayjs(startDate).format('YYYY-MM-DD');
      params.push(`startDate=${formattedStartDate}`);
    }

    if (endDate) {
      const formattedEndDate = dayjs(endDate).format('YYYY-MM-DD');
      params.push(`endDate=${formattedEndDate}`);
    }

    setQueryParam(params.join('&'));
  }, [debouncedSearchTerm, startDate, endDate, setQueryParam]);

  const { staff, staffLoading, mutate } = GetStaffList(
    `?page=${currentPage}&limit=${pageSize}${queryParam ? `&${queryParam}` : ''}`
  );
  const stafData = staff?.data?.staffs;
  const totalPages = staff?.data?.totalPages ?? 0;

  const handleEventFromModal = (staff: any) => {
    setDetail(staff);
    setOpen(true);
  };

  const handleDateRangeChange = (
    start: Date | undefined,
    end: Date | undefined
  ) => {
    setStartDate(start);
    setEndDate(end);
  };

  return (
    <>
      <div>
        <Badge
          className="h-7 cursor-pointer"
          onClick={() => setOpenCreate(true)}
        >
          Add staff
        </Badge>
      </div>
      <div className="w-full overflow-x-auto rounded-t-lg shadow-md">
        <div className="flex flex-wrap justify-between items-center py-4">
          <div className="flex flex-wrap gap-3 items-center">
            <DateRangeFilter
              onDateRangeChange={handleDateRangeChange}
              className="w-[250px]"
            />
            <div className="relative w-64">
              <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                <Search className="w-4 h-4 text-gray-500" />
              </div>
              <Input
                type="text"
                placeholder="Search staff..."
                className="pl-10 pr-4 py-2 w-full"
                value={searchTerm}
                onChange={handleSearchChange}
              />
            </div>
          </div>
        </div>
        <table className="w-full table-auto text-left text-xs">
          <thead className="bg-primary text-gray-100 dark:text-black">
            <tr>
              <th className="table-style">S/N</th>
              <th className="table-style">Type</th>

              <th className="table-style">Full Name</th>
              <th className="table-style">Location</th>
              <th className="table-style">Referal Code</th>
              <th className="table-style">Code Usage</th>
              <th className="table-style">Last Login</th>
              <th className="table-style">Status</th>
              <th className="table-style">Action</th>
            </tr>
          </thead>
          <tbody className="relative overflow-x-auto whitespace-nowrap text-sm shadow-md">
            {stafData?.map((staff: any, index: any) => (
              <tr
                className="text-xs text-[#062A55] dark:text-white"
                key={staff.id}
              >
                <td className="table-style">
                  {currentPage === 1
                    ? index + 1
                    : (currentPage - 1) * pageSize + (index + 1)}
                </td>
                <td className="table-style">{staff.type}</td>
                <td className="table-style">{staff.fullName}</td>
                <td className="table-style">
                  {staff.location.name}, {staff.location.region.name}
                </td>
                <td className="table-style">{staff.referralCode.code}</td>
                <td className="table-style">{staff.codeUsage}</td>
                <td className="table-style">
                  {staff.lastLogin
                    ? dayjs(staff.lastLogin).format('MMM D, YYYY')
                    : '--'}
                </td>
                <td className="table-style">
                  {staff.isActive
                    ? StatusBadge({ status: 'active' })
                    : StatusBadge({ status: 'inactive' })}
                </td>
                <td className="table-style">
                  <Ellipsis
                    onClick={() => handleEventFromModal(staff)}
                    className="w-4 h-4 cursor-pointer"
                  />
                </td>
              </tr>
            ))}
          </tbody>
        </table>
        {staffLoading ? (
          <LoadingState />
        ) : totalPages === 0 ? (
          <EmptyState />
        ) : null}
      </div>
      {totalPages > 1 ? (
        <Pagination
          title="Staffs"
          totalPages={totalPages}
          currentPage={currentPage}
          onPageChange={handlePageChange}
          totalCount={staff?.data?.totalCount}
        />
      ) : (
        ''
      )}
      <Details open={open} setOpen={setOpen} mutate={mutate} data={detail} />
      <CreateStaff open={openCreate} setOpen={setOpenCreate} mutate={mutate} />
    </>
  );
};

export default Staff;
