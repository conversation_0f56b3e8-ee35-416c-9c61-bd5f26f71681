'use client';

import { useState } from 'react';
import { Gem } from 'lucide-react';
import Link from 'next/link';
import { Paths } from '@/components/navigations/data';
import { Badge } from '../ui/badge';
import RewardsTable from './components/data-table';
import { hasPermission, PERMISSIONS } from '@/lib/types/permissions';

export default function RewardsPage() {
  const [open, setOpen] = useState(false);
  const permitCreate = hasPermission(PERMISSIONS.REWARD_CREATE);

  return (
    <>
      <h2 className="text-lg font-bold text-gray-900 dark:text-white mb-4 text-left flex items-center gap-2">
        <Gem className="w-5 h-5 text-zinc-900 dark:text-zinc-50" />
        Rewards Management
      </h2>
      <div className="flex gap-2">
        {permitCreate && (
          <Badge className="cursor-pointer" onClick={() => setOpen(true)}>
            Add Reward
          </Badge>
        )}
      </div>
      <div className="mt-4 bg-white dark:bg-[#0F0F12] rounded-xl sm:p-4 flex flex-col items-start justify-start sm:border border-gray-200 dark:border-[#1F1F23]">
        <RewardsTable openCreate={open} setOpenCreate={setOpen} />
      </div>
    </>
  );
}
