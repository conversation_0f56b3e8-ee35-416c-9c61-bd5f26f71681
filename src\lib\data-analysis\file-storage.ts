/**
 * File Storage Service
 *
 * This service handles saving uploaded files, retrieving file history,
 * and managing stored files.
 */

import { myApi } from '@/api/fetcher';

export interface StoredFile {
  id: string;
  name: string;
  type: string;
  size: number;
  uploadDate: string;
  lastAccessDate: string;
  path: string;
  metadata?: {
    rowCount?: number;
    columnCount?: number;
    columns?: string[];
    previewData?: any;
  };
}

/**
 * Save an uploaded file to the server
 *
 * @param file The file to save
 * @param metadata Optional metadata about the file
 * @returns A promise that resolves to the stored file information
 */
export async function saveUploadedFile(
  file: File,
  metadata?: StoredFile['metadata']
): Promise<StoredFile> {
  try {
    // Create a FormData object to send the file
    const formData = new FormData();
    formData.append('file', file);

    // Add metadata if provided
    if (metadata) {
      formData.append('metadata', JSON.stringify(metadata));
    }

    // Send the file to the server
    const response = await myApi.post('/data-analysis/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });

    return response.data;
  } catch (error) {
    console.error('Error saving file:', error);
    throw new Error('Failed to save file');
  }
}

/**
 * Get a list of all stored files
 *
 * @returns A promise that resolves to an array of stored files
 */
export async function getStoredFiles(): Promise<StoredFile[]> {
  try {
    const response = await myApi.get('/data-analysis/files');
    return response.data;
  } catch (error) {
    console.error('Error getting stored files:', error);

    // For development/demo purposes, return mock data if the API fails
    return getMockStoredFiles();
  }
}

/**
 * Get a specific stored file by ID
 *
 * @param fileId The ID of the file to retrieve
 * @returns A promise that resolves to the stored file information
 */
export async function getStoredFileById(fileId: string): Promise<StoredFile> {
  try {
    const response = await myApi.get(`/data-analysis/files/${fileId}`);
    return response.data;
  } catch (error) {
    console.error(`Error getting file with ID ${fileId}:`, error);
    throw new Error('Failed to retrieve file');
  }
}

/**
 * Delete a stored file
 *
 * @param fileId The ID of the file to delete
 * @returns A promise that resolves when the file is deleted
 */
export async function deleteStoredFile(fileId: string): Promise<void> {
  try {
    await myApi.delete(`/data-analysis/files/${fileId}`);
  } catch (error) {
    console.error(`Error deleting file with ID ${fileId}:`, error);
    throw new Error('Failed to delete file');
  }
}

/**
 * Update the metadata for a stored file
 *
 * @param fileId The ID of the file to update
 * @param metadata The new metadata
 * @returns A promise that resolves to the updated stored file information
 */
export async function updateStoredFileMetadata(
  fileId: string,
  metadata: StoredFile['metadata']
): Promise<StoredFile> {
  try {
    const response = await myApi.patch(
      `/data-analysis/files/${fileId}/metadata`,
      {
        metadata,
      }
    );
    return response.data;
  } catch (error) {
    console.error(`Error updating metadata for file with ID ${fileId}:`, error);
    throw new Error('Failed to update file metadata');
  }
}

/**
 * Generate mock stored files for development/demo purposes
 *
 * @returns An array of mock stored files
 */
function getMockStoredFiles(): StoredFile[] {
  return [
    {
      id: '1',
      name: 'sales_data_2023.csv',
      type: 'csv',
      size: 1024 * 25, // 25 KB
      uploadDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days ago
      lastAccessDate: new Date(
        Date.now() - 2 * 24 * 60 * 60 * 1000
      ).toISOString(), // 2 days ago
      path: '/uploads/sales_data_2023.csv',
      metadata: {
        rowCount: 150,
        columnCount: 8,
        columns: [
          'Date',
          'Product',
          'Region',
          'Sales',
          'Quantity',
          'Revenue',
          'Profit',
          'Customer',
        ],
      },
    },
    {
      id: '2',
      name: 'patient_records.xlsx',
      type: 'xlsx',
      size: 1024 * 75, // 75 KB
      uploadDate: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(), // 3 days ago
      lastAccessDate: new Date(
        Date.now() - 1 * 24 * 60 * 60 * 1000
      ).toISOString(), // 1 day ago
      path: '/uploads/patient_records.xlsx',
      metadata: {
        rowCount: 250,
        columnCount: 12,
        columns: [
          'PatientID',
          'Name',
          'Age',
          'Gender',
          'BloodType',
          'Diagnosis',
          'AdmissionDate',
          'DischargeDate',
          'Doctor',
          'Department',
          'BillingAmount',
          'InsuranceCoverage',
        ],
      },
    },
    {
      id: '3',
      name: 'financial_report_q1.csv',
      type: 'csv',
      size: 1024 * 42, // 42 KB
      uploadDate: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000).toISOString(), // 14 days ago
      lastAccessDate: new Date(
        Date.now() - 5 * 24 * 60 * 60 * 1000
      ).toISOString(), // 5 days ago
      path: '/uploads/financial_report_q1.csv',
      metadata: {
        rowCount: 120,
        columnCount: 10,
        columns: [
          'Month',
          'Department',
          'Revenue',
          'Expenses',
          'Profit',
          'PatientCount',
          'AverageBill',
          'InsuranceClaims',
          'Reimbursements',
          'OutstandingPayments',
        ],
      },
    },
  ];
}
