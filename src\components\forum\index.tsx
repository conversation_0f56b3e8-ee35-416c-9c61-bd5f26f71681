'use client';

import React, { useEffect } from 'react';
import { ForumSidebar } from '@/components/forum/components/ForumSidebar';
import { hasPermission, PERMISSIONS } from '@/lib/types/permissions';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { notificationStore } from '@/store/notificationStore';
import websocketService from '@/services/websocket';

export default function ForumPage() {
  const router = useRouter();
  const canViewGroups = hasPermission(PERMISSIONS.FORUM_VIEW_GROUP);

  // Example: Send a message to join the forum channel when the page loads
  useEffect(() => {
    // For demonstration purposes, we'll add a notification
    // In a real implementation, you would use the WebSocket service
    const timer = setTimeout(() => {
      notificationStore.addNotification({
        title: 'New Message',
        message: 'You have a new message in the forum',
        link: '/forum/direct-messages',
      });
    }, 3000);

    // Example of how to use the WebSocket service
    // websocketService.send({
    //   type: 'join_channel',
    //   data: { channel: 'forum' }
    // });

    return () => {
      clearTimeout(timer);
      // Example of leaving a channel when component unmounts
      // websocketService.send({
      //   type: 'leave_channel',
      //   data: { channel: 'forum' }
      // });
    };
  }, []);

  if (!canViewGroups) {
    return (
      <div className="flex h-full items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold mb-2">Access Denied</h2>
          <p className="text-muted-foreground">
            You don&apos;t have permission to view the forum.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col md:flex-row h-full">
      <div className="md:hidden p-4 border-b flex justify-between items-center">
        <h1 className="text-xl font-bold">Forum</h1>
        <Button
          variant="outline"
          size="sm"
          onClick={() => router.push('/forum/direct-messages')}
        >
          Direct Messages
        </Button>
      </div>
      <div className="hidden md:block">
        <ForumSidebar />
      </div>
      <div className="flex-1 flex items-center justify-center bg-muted/30 p-4">
        <div className="text-center max-w-md p-4 md:p-6">
          <h2 className="text-xl md:text-2xl font-bold mb-2 md:mb-4">
            Welcome to the Forum
          </h2>
          <p className="text-sm md:text-base text-muted-foreground mb-4 md:mb-6">
            Select a group from the sidebar to start chatting or create a new
            group. You can also send direct messages to other staff members.
          </p>
          <div className="flex flex-col md:flex-row justify-center gap-2 md:gap-4">
            <button
              className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90"
              onClick={() => router.push('/forum/direct-messages')}
            >
              Direct Messages
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
