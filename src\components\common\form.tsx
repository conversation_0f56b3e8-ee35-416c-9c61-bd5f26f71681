'use client';
import * as React from 'react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Eye, EyeOff } from 'lucide-react';
import { Switch } from '../ui/switch';
import { Textarea } from '../ui/textarea';

import { Calendar } from '@/components/ui/calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Button } from '@/components/ui/button';
import { CalendarIcon, Upload, X } from 'lucide-react';
import { format } from 'date-fns';

// InputField.tsx

export function InputField({
  control,
  name,
  label,
  placeholder,
  type = 'text',
  min,
  disabled = false,
}: {
  control: any;
  name: string;
  label: string;
  placeholder: string;
  type?: string;
  min?: any;
  disabled?: boolean;
}) {
  return (
    <FormField
      control={control}
      name={name}
      render={({ field }: { field: any }) => (
        <FormItem>
          <FormLabel>{label}</FormLabel>
          <FormControl>
            <Input
              min={min}
              placeholder={placeholder}
              {...field}
              type={type}
              disabled={disabled}
            />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}

export function CurrencyInputField({
  control,
  name,
  label,
  placeholder,
  disabled = false,
}: {
  control: any;
  name: string;
  label: string;
  placeholder: string;
  disabled?: boolean;
}) {
  const [displayValue, setDisplayValue] = React.useState('');

  return (
    <FormField
      control={control}
      name={name}
      render={({ field }: { field: any }) => {
        // Initialize display value when field.value changes
        React.useEffect(() => {
          if (field.value) {
            setDisplayValue(new Intl.NumberFormat('en-US').format(field.value));
          }
        }, [field.value]);

        return (
          <FormItem>
            <FormLabel>{label}</FormLabel>
            <FormControl>
              <Input
                placeholder={placeholder}
                disabled={disabled}
                value={displayValue}
                onChange={(e) => {
                  // Remove all non-numeric characters
                  const rawValue = e.target.value.replace(/[^\d]/g, '');

                  // Format with commas for display
                  const formattedValue = rawValue
                    ? new Intl.NumberFormat('en-US').format(parseInt(rawValue))
                    : '';

                  // Update the displayed value with commas
                  setDisplayValue(formattedValue);

                  // Update the form value with the numeric value only
                  field.onChange(rawValue);
                }}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        );
      }}
    />
  );
}

export function InputTextArea({
  control,
  name,
  label,
  placeholder,
  type = 'text',
  disabled = false,
}: {
  control: any;
  name: string;
  label: string;
  placeholder: string;
  type?: string;
  disabled?: boolean;
}) {
  return (
    <FormField
      control={control}
      name={name}
      render={({ field }: { field: any }) => (
        <FormItem>
          <FormLabel>{label}</FormLabel>
          <FormControl>
            <Textarea
              placeholder={placeholder}
              className="min-h-[100px]"
              {...field}
              type={type}
              disabled={disabled}
            />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}

export function PasswordField({
  control,
  name,
  label,
  placeholder,
}: {
  control: any;
  name: string;
  label: string;
  placeholder: string;
}) {
  const [showPassword, setShowPassword] = React.useState(false);

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  return (
    <FormField
      control={control}
      name={name}
      render={({ field }) => (
        <FormItem>
          <FormLabel>{label}</FormLabel>
          <FormControl>
            <div className="relative">
              <Input
                placeholder={placeholder}
                {...field}
                type={showPassword ? 'text' : 'password'}
              />
              <button
                type="button"
                onClick={togglePasswordVisibility}
                className="absolute inset-y-0 right-0 flex items-center pr-3"
              >
                {showPassword ? <EyeOff size={20} /> : <Eye size={20} />}
              </button>
            </div>
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}

export function SwitchField({
  control,
  name,
  label,
  disabled,
}: {
  control: any;
  name: string;
  label: string;
  disabled?: boolean;
}) {
  return (
    <FormField
      control={control}
      name={name}
      render={({ field }) => (
        <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm">
          <div className="space-y-0.5">
            <FormLabel>{label}</FormLabel>
          </div>
          <FormControl>
            <Switch
              checked={field.value}
              onCheckedChange={field.onChange}
              aria-label={label}
              disabled={disabled}
            />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}

// Reusable CustomSelectForm Component
interface CustomSelectFormProps {
  control: any;
  name: string;
  label: string;
  placeholder: string;
  options: { id: string; name: string }[];
  description?: string; //
}

export function CustomSelectForm({
  control,
  name,
  label,
  placeholder,
  options,
}: CustomSelectFormProps) {
  return (
    <FormField
      control={control}
      name={name}
      render={({ field }) => (
        <FormItem>
          <FormLabel>{label}</FormLabel>
          <Select onValueChange={field.onChange} defaultValue={field.value}>
            <FormControl>
              <SelectTrigger>
                <SelectValue placeholder={placeholder} />
              </SelectTrigger>
            </FormControl>
            <SelectContent position="popper" sideOffset={4}>
              {options.map((option) => (
                <SelectItem key={option.id} value={option.id}>
                  {option.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}

interface FormRowProps {
  children: React.ReactNode;
}
export function FormRow({ children }: FormRowProps) {
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">{children}</div>
  );
}

export function InputCalendar({
  control,
  name,
  label,
  placeholder = 'Pick a date',
  minDate,
}: {
  control: any;
  name: string;
  label: string;
  placeholder?: string;
  minDate?: Date;
}) {
  return (
    <FormField
      control={control}
      name={name}
      render={({ field }) => (
        <FormItem className="flex flex-col">
          <FormLabel>{label}</FormLabel>
          <Popover>
            <PopoverTrigger asChild>
              <FormControl>
                <Button
                  variant={'outline'}
                  className={`w-full justify-start text-left font-normal ${
                    !field.value ? 'text-muted-foreground' : ''
                  }`}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {field.value
                    ? format(field.value, 'MMMM d, yyyy')
                    : placeholder}
                </Button>
              </FormControl>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="start">
              <Calendar
                mode="single"
                selected={field.value}
                onSelect={field.onChange}
                disabled={(date) => (minDate ? date < minDate : false)}
              />
            </PopoverContent>
          </Popover>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}

export function InputDateTime({
  control,
  name,
  label,
  placeholder = 'Pick date and time',
  minDate,
}: {
  control: any;
  name: string;
  label: string;
  placeholder?: string;
  minDate?: Date;
}) {
  const [time, setTime] = React.useState('');

  return (
    <FormField
      control={control}
      name={name}
      render={({ field }) => {
        const selectedDate = field.value ? new Date(field.value) : undefined;

        const handleDateSelect = (date: Date | undefined) => {
          if (date) {
            if (time) {
              const [hours, minutes] = time.split(':');
              const newDateTime = new Date(date);
              newDateTime.setHours(parseInt(hours), parseInt(minutes));
              field.onChange(newDateTime.toISOString());
            } else {
              // Store date temporarily, will combine with time when time is selected
              const tempDateTime = new Date(date);
              tempDateTime.setHours(0, 0, 0, 0);
              field.onChange(tempDateTime.toISOString());
            }
          }
        };

        const handleTimeChange = (newTime: string) => {
          setTime(newTime);
          if (selectedDate && newTime) {
            const [hours, minutes] = newTime.split(':');
            const newDateTime = new Date(selectedDate);
            newDateTime.setHours(parseInt(hours), parseInt(minutes));
            field.onChange(newDateTime.toISOString());
          }
        };

        return (
          <FormItem className="flex flex-col">
            <FormLabel>{label}</FormLabel>
            <div className="space-y-2">
              <Popover>
                <PopoverTrigger asChild>
                  <FormControl>
                    <Button
                      variant={'outline'}
                      className={`w-full justify-start text-left font-normal ${
                        !field.value ? 'text-muted-foreground' : ''
                      }`}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {field.value
                        ? format(new Date(field.value), 'MMMM d, yyyy')
                        : placeholder}
                    </Button>
                  </FormControl>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={selectedDate}
                    onSelect={handleDateSelect}
                    disabled={(date) => (minDate ? date < minDate : false)}
                  />
                </PopoverContent>
              </Popover>
              <Input
                type="time"
                value={time}
                onChange={(e) => handleTimeChange(e.target.value)}
                placeholder="Select time"
                className="w-full"
              />
            </div>
            <FormMessage />
          </FormItem>
        );
      }}
    />
  );
}

export function FileUpload({
  control,
  name,
  label,
  accept = 'image/*',
  onFileChange,
}: {
  control: any;
  name: string;
  label: string;
  accept?: string;
  onFileChange?: (file: File | null) => void;
}) {
  const fileInputRef = React.useRef<HTMLInputElement>(null);

  const handleFileChange = (
    file: File | null,
    onChange: (value: any) => void
  ) => {
    onChange(file);
    onFileChange?.(file);
  };

  const clearFile = (onChange: (value: any) => void) => {
    onChange(null);
    onFileChange?.(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  return (
    <FormField
      control={control}
      name={name}
      render={({ field }) => (
        <FormItem>
          <FormLabel>{label}</FormLabel>
          <FormControl>
            <div className="flex items-center gap-2">
              <Input
                ref={fileInputRef}
                type="file"
                accept={accept}
                onChange={(e) => {
                  const file = e.target.files?.[0] || null;
                  handleFileChange(file, field.onChange);
                }}
                className="file:mr-2 file:py-1 file:px-2 file:rounded file:border-0 file:text-sm file:bg-primary file:text-primary-foreground hover:file:bg-primary/90"
              />
              {field.value && (
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => clearFile(field.onChange)}
                >
                  <X className="h-4 w-4" />
                </Button>
              )}
            </div>
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}
