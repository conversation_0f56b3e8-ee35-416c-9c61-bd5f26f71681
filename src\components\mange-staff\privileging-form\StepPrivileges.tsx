'use client';

import { useFormContext } from 'react-hook-form';

const categories = [
  'Consultation in out-patients (including medico-legal)',
  'Consultation + minor procedures in out-patients',
  'Consultation + minor procedures + admission of in/day patients',
  'Consultation + minor procedures + admission + operative procedures',
  'Admission and care of in/day patients only',
  'Provision of anaesthetic services',
  'Diagnostic & therapeutic imaging (incl. admission & interventional)',
  'Diagnostic & therapeutic imaging (reporting & advice)',
  'Pathology procedures, reporting & advice',
  'Administration of sedation (non-anaesthetists)',
];

export default function StepPrivileges() {
  const { register } = useFormContext();

  return (
    <div className="space-y-6">
      <div className="space-y-4">
        <p className="text-sm text-muted-foreground">
          Select all categories that apply to your requested practising
          privileges:
        </p>
        <div className="space-y-3">
          {categories.map((cat, i) => (
            <div
              key={i}
              className="flex items-start space-x-3 p-3 rounded-lg border hover:bg-muted/50 transition-colors"
            >
              <input
                type="checkbox"
                {...register(`privileges.${i}`)}
                className="mt-1 h-4 w-4 rounded border-input text-primary focus:ring-primary focus:ring-offset-0"
              />
              <label className="text-sm font-medium leading-relaxed cursor-pointer flex-1">
                {cat}
              </label>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
