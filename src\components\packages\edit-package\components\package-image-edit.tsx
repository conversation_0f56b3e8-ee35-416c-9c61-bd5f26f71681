'use client';

import React, { useState } from 'react';
import { Modal } from '@/components/common/modal';
import ImageUpload from '@/components/common/image';
import { Button } from '@/components/ui/button';
import { CameraIcon } from 'lucide-react';
import { toast } from 'sonner';
import { myApi } from '@/api/fetcher';

interface PackageImageEditProps {
  packageId: string;
  currentImage?: string;
  onImageUpdated?: () => void;
}

const PackageImageEdit: React.FC<PackageImageEditProps> = ({
  packageId,
  currentImage,
  onImageUpdated,
}) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [packageImage, setPackageImage] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(
    currentImage || null
  );

  // Reset state when modal is closed
  const handleModalOpenChange = (open: boolean) => {
    setIsModalOpen(open);

    // If the modal is being closed, reset the image state to the original
    if (!open) {
      setPackageImage(null);
      setImagePreview(currentImage || null);
    }
  };

  // Update state when currentImage changes (e.g., after a successful update)
  React.useEffect(() => {
    setImagePreview(currentImage || null);
  }, [currentImage]);

  const handleImageChange = (file: File | null) => {
    setPackageImage(file);
  };

  const handlePreviewChange = (preview: string | null) => {
    setImagePreview(preview);
  };

  const handleSaveImage = async () => {
    if (!packageImage) {
      toast.error('Please select an image to upload');
      return;
    }

    try {
      setIsLoading(true);
      const formData = new FormData();
      formData.append('image', packageImage);
      formData.append('packageId', packageId);

      const res = await myApi.patch('/package/update-package-image', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      if (res.status === 200) {
        toast.success('Package image updated successfully');
        handleModalOpenChange(false);

        if (onImageUpdated) {
          onImageUpdated();
        }
      }
    } catch (error) {
      console.error('Error updating package image:', error);
      toast.error('Failed to update package image');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      <div
        className="cursor-pointer"
        onClick={() => handleModalOpenChange(true)}
        title="Edit package image"
      >
        <CameraIcon className="edit-button w-6 h-6" />
      </div>

      <Modal
        open={isModalOpen}
        setOpen={handleModalOpenChange}
        title="Edit Package Image"
        description="Upload a new image for this package"
        isLoading={isLoading}
      >
        <div className="space-y-4">
          <div className="space-y-2">
            {currentImage && !imagePreview && (
              <div className="mb-4">
                <p className="text-sm text-muted-foreground mb-2">
                  Current Image:
                </p>
                <img
                  src={currentImage}
                  alt="Current package"
                  className="w-full max-h-48 object-contain rounded-md border"
                />
              </div>
            )}
            <ImageUpload
              onImageChange={handleImageChange}
              onPreviewChange={handlePreviewChange}
              initialPreview={imagePreview}
            />
            {!packageImage && !currentImage && (
              <p className="text-sm text-red-500 mt-1">
                Please select an image
              </p>
            )}
          </div>
          <div className="flex justify-end gap-2 mt-4">
            <Button
              variant="outline"
              onClick={() => handleModalOpenChange(false)}
              type="button"
            >
              Cancel
            </Button>
            <Button
              onClick={handleSaveImage}
              type="button"
              disabled={
                isLoading ||
                (packageImage === null &&
                  ((currentImage && imagePreview === currentImage) ||
                    (!currentImage && !imagePreview)))
              }
            >
              {isLoading ? 'Saving...' : 'Save Changes'}
            </Button>
          </div>
        </div>
      </Modal>
    </>
  );
};

export default PackageImageEdit;
