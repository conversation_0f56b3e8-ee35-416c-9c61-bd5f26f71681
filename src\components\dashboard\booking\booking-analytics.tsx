'use client';

import { useState } from 'react';
import {
  TrendingUp,
  TrendingDown,
  Calendar,
  CheckCircle,
  Clock,
  XCircle,
  DollarSign,
  BarChart3,
  <PERSON><PERSON><PERSON>,
  <PERSON>ader2,
} from 'lucide-react';
import {
  <PERSON>,
  <PERSON><PERSON>hart,
  CartesianGrid,
  XAxis,
  YAxis,
  Tooltip,
  Legend,
  Line,
  LineChart,
  ResponsiveContainer,
  Pie,
  <PERSON>hart as RePieChart,
  Cell,
  Area,
  AreaChart,
} from 'recharts';
import YearSelect from '../../common/year-select';
import { GetEnhancedBookingAnalytics } from '@/api/analytics';
import { formatValue, numberFormat } from '@/lib/utils';

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';

const STATUS_COLORS = {
  Completed: '#149949',
  Pending: '#BD9A3D',
  Draft: '#dee2e6',
};

export function EnhancedBookingAnalytics() {
  const [year, setYear] = useState<string>(new Date().getFullYear().toString());
  const [activeTab, setActiveTab] = useState<string>('trends');

  const handleYearChange = (selectedYear: string) => {
    setYear(selectedYear);
  };

  // Use the real API function with the year parameter
  const { bookingStats, bookingStatsLoading, bookingStatsError } =
    GetEnhancedBookingAnalytics(`year=${year}`);

  // Handle loading state
  if (bookingStatsLoading) {
    return (
      <div className="flex flex-col items-center justify-center h-[400px]">
        <Loader2 className="w-8 h-8 animate-spin text-primary mb-4" />
        <p className="text-muted-foreground">Loading booking analytics...</p>
      </div>
    );
  }

  // Handle error state
  if (bookingStatsError) {
    return (
      <div className="flex flex-col items-center justify-center h-[400px] text-red-500">
        <XCircle className="w-8 h-8 mb-4" />
        <p>Error loading booking analytics. Please try again later.</p>
      </div>
    );
  }

  // If no data is available
  if (!bookingStats || !bookingStats.data) {
    return (
      <div className="flex flex-col items-center justify-center h-[400px] text-amber-500">
        <Calendar className="w-8 h-8 mb-4" />
        <p>No booking data available for the selected period.</p>
      </div>
    );
  }

  // Create a safe data object with fallbacks for all properties
  const data = {
    totalBookings: bookingStats.data.data.totalBookings || 0,
    completedBookings: bookingStats.data.data.completedBookings || 0,
    pendingBookings: bookingStats.data.data.pendingBookings || 0,
    draftBookings: bookingStats.data.data.draftBookings || 0,
    totalRevenue: bookingStats.data.data.totalRevenue || 0,
    averageBookingValue: bookingStats.data.data.averageBookingValue || 0,
    conversionRate: bookingStats.data.data.conversionRate || 0,
    recentTrend: bookingStats.data.data.recentTrend || {
      direction: 'up',
      percent: 0,
    },
    monthlyData: bookingStats.data.data.monthlyData || [],
    locationData: bookingStats.data.data.locationData || [],
    statusDistribution: bookingStats.data.data.statusDistribution || [],
    topPackages: bookingStats.data.data.topPackages || [],
    weekdayDistribution: bookingStats.data.data.weekdayDistribution || [],
  };

  return (
    <div className="space-y-4">
      {/* Summary Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Calendar className="w-4 h-4 text-primary" />
              Total Bookings
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.totalBookings}</div>
            <div className="flex items-center mt-1 text-xs">
              {data.recentTrend.direction === 'up' ? (
                <TrendingUp className="w-3 h-3 mr-1 text-green-500" />
              ) : (
                <TrendingDown className="w-3 h-3 mr-1 text-red-500" />
              )}
              <span
                className={
                  data.recentTrend.direction === 'up'
                    ? 'text-green-500'
                    : 'text-red-500'
                }
              >
                {data.recentTrend.percent}% from last month
              </span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <CheckCircle className="w-4 h-4 text-green-500" />
              Completion Rate
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.conversionRate}%</div>
            <div className="flex items-center mt-1 text-xs">
              <span className="text-muted-foreground">
                {data.completedBookings} of {data.totalBookings} bookings
              </span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <span className="text-blue-500 text-lg">₦</span>
              Total Revenue
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {numberFormat(data.totalRevenue)}
            </div>
            <div className="flex items-center mt-1 text-xs">
              <span className="text-muted-foreground">
                Avg. {numberFormat(data.averageBookingValue)} per booking
              </span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Clock className="w-4 h-4 text-amber-500" />
              Pending Bookings
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.pendingBookings}</div>
            <div className="flex items-center mt-1 text-xs">
              <span className="text-muted-foreground">
                {((data.pendingBookings / data.totalBookings) * 100).toFixed(1)}
                % of total bookings
              </span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Tabs for different charts */}
      <Card>
        <CardHeader>
          <div className="flex flex-wrap gap-4 justify-between">
            <div>
              <CardTitle>Booking Analytics</CardTitle>
              <CardDescription>
                Comprehensive booking performance metrics
              </CardDescription>
            </div>
            <div>
              <YearSelect onChange={handleYearChange} />
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <Tabs
            defaultValue="trends"
            className="w-full"
            onValueChange={setActiveTab}
          >
            <TabsList className="grid grid-cols-4 mb-4">
              <TabsTrigger value="trends" className="flex items-center gap-1">
                <BarChart3 className="w-4 h-4" /> Trends
              </TabsTrigger>
              <TabsTrigger value="status" className="flex items-center gap-1">
                <PieChart className="w-4 h-4" /> Status
              </TabsTrigger>
              <TabsTrigger value="revenue" className="flex items-center gap-1">
                <DollarSign className="w-4 h-4" /> Revenue
              </TabsTrigger>
              <TabsTrigger value="packages" className="flex items-center gap-1">
                <Calendar className="w-4 h-4" /> Packages
              </TabsTrigger>
            </TabsList>

            <TabsContent value="trends" className="mt-0">
              <div className="h-[350px]">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={data.monthlyData}>
                    <CartesianGrid strokeDasharray="3 3" vertical={false} />
                    <XAxis
                      dataKey="month"
                      tickLine={false}
                      tickMargin={10}
                      axisLine={false}
                      tickFormatter={(value) => value.slice(0, 3)}
                    />
                    <YAxis axisLine={false} tickLine={false} tickMargin={10} />
                    <Tooltip
                      formatter={(value) => [`${value} bookings`, '']}
                      labelFormatter={(label) => `Month: ${label}`}
                    />
                    <Legend />
                    <Bar
                      dataKey="completed"
                      name="Completed"
                      stackId="a"
                      fill="#149949"
                      radius={[4, 4, 0, 0]}
                    />
                    <Bar
                      dataKey="pending"
                      name="Pending"
                      stackId="a"
                      fill="#BD9A3D"
                      radius={[0, 0, 0, 0]}
                    />
                    <Bar
                      dataKey="draft"
                      name="Draft"
                      stackId="a"
                      fill="#ced4da"
                      radius={[0, 0, 0, 0]}
                    />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </TabsContent>

            <TabsContent value="status" className="mt-0">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="h-[350px] flex items-center justify-center">
                  <ResponsiveContainer width="100%" height="100%">
                    <RePieChart>
                      <Pie
                        data={data.statusDistribution}
                        dataKey="count"
                        nameKey="status"
                        cx="50%"
                        cy="50%"
                        outerRadius={120}
                        label={({ status, count, percent }) =>
                          `${status}: ${count} (${(percent * 100).toFixed(0)}%)`
                        }
                      >
                        {data.statusDistribution.map(
                          (entry: any, index: number) => (
                            <Cell
                              key={`cell-${index}`}
                              fill={
                                STATUS_COLORS[
                                  entry.status as keyof typeof STATUS_COLORS
                                ]
                              }
                            />
                          )
                        )}
                      </Pie>
                      <Tooltip
                        formatter={(value) => [`${value} bookings`, 'Count']}
                      />
                      <Legend />
                    </RePieChart>
                  </ResponsiveContainer>
                </div>
                <div className="h-[350px] flex items-center justify-center">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart data={data.weekdayDistribution} layout="vertical">
                      <CartesianGrid strokeDasharray="3 3" horizontal={false} />
                      <XAxis type="number" />
                      <YAxis
                        dataKey="day"
                        type="category"
                        axisLine={false}
                        tickLine={false}
                        width={100}
                      />
                      <Tooltip
                        formatter={(value) => [`${value} bookings`, 'Count']}
                      />
                      <Bar
                        dataKey="bookings"
                        fill="#2196F3"
                        radius={4}
                        name="Bookings"
                      />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="revenue" className="mt-0">
              <div className="h-[350px]">
                <ResponsiveContainer width="100%" height="100%">
                  <AreaChart data={data.monthlyData}>
                    <CartesianGrid strokeDasharray="3 3" vertical={false} />
                    <XAxis
                      dataKey="month"
                      tickLine={false}
                      tickMargin={10}
                      axisLine={false}
                      tickFormatter={(value) => value.slice(0, 3)}
                    />
                    <YAxis
                      axisLine={false}
                      tickLine={false}
                      tickMargin={10}
                      tickFormatter={(value) => `₦${value / 1000}k`}
                    />
                    <Tooltip
                      formatter={(value) => [
                        `${numberFormat(value as number)}`,
                        'Revenue',
                      ]}
                      labelFormatter={(label) => `Month: ${label}`}
                    />
                    <Area
                      type="monotone"
                      dataKey="revenue"
                      stroke="#149949"
                      fill="#149949"
                      fillOpacity={0.3}
                      name="Revenue"
                    />
                  </AreaChart>
                </ResponsiveContainer>
              </div>
            </TabsContent>

            <TabsContent value="packages" className="mt-0">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Package Name</TableHead>
                    <TableHead className="text-right">Bookings</TableHead>
                    <TableHead className="text-right">Revenue</TableHead>
                    <TableHead className="text-right">Avg. Value</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {data.topPackages.map((pkg: any) => (
                    <TableRow key={pkg.name}>
                      <TableCell className="font-medium">{pkg.name}</TableCell>
                      <TableCell className="text-right">
                        {pkg.bookings}
                      </TableCell>
                      <TableCell className="text-right">
                        {numberFormat(pkg.revenue)}
                      </TableCell>
                      <TableCell className="text-right">
                        {numberFormat(pkg.revenue / pkg.bookings)}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Location Distribution */}
      <Card>
        <CardHeader>
          <CardTitle>Booking Distribution by Location</CardTitle>
          <CardDescription>
            Bookings and revenue across different locations
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="h-[300px]">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart
                data={data.locationData}
                margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
              >
                <CartesianGrid strokeDasharray="3 3" vertical={false} />
                <XAxis dataKey="location" />
                <YAxis yAxisId="left" orientation="left" stroke="#BD9A3D" />
                <YAxis
                  yAxisId="right"
                  orientation="right"
                  stroke="#149949"
                  tickFormatter={(value) => `₦${value / 1000000}M`}
                />
                <Tooltip
                  formatter={(value, name) => {
                    if (name === 'bookings')
                      return [`${value} bookings`, 'Bookings'];
                    if (name === 'revenue')
                      return [`${numberFormat(value as number)}`, 'Revenue'];
                    return [value, name];
                  }}
                />
                <Legend />
                <Bar
                  yAxisId="left"
                  dataKey="bookings"
                  fill="#BD9A3D"
                  name="Bookings"
                />
                <Bar
                  yAxisId="right"
                  dataKey="revenue"
                  fill="#149949"
                  name="Revenue"
                />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
