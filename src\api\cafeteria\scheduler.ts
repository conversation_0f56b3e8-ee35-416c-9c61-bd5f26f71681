import useSWR from 'swr';
import { useAuthSWR } from '../useAuthSWR';

export const GetWeeklyMenus = (week?: string) => {
  const qs = week ? `?week=${week}` : '';
  const { data, isLoading, mutate } = useAuthSWR(`/cafeteria/menu/weekly${qs}`);

  return {
    weeklyMenus: data,
    weeklyMenusLoading: isLoading,
    mutate,
  };
};

export const GetPatientOrders = (patientId: string, date?: string) => {
  const qs = date ? `?date=${date}` : '';
  const { data, isLoading, mutate } = useSWR(
    `/cafeteria/patient-orders/${patientId}${qs}`
  );

  return {
    patientOrders: data,
    patientOrdersLoading: isLoading,
    mutate,
  };
};
