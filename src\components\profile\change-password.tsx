'use client';

import React, { useState } from 'react';
import { Modal } from '@/components/common/modal';
import { Form } from '@/components/ui/form';
import { PasswordField } from '@/components/common/form';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  changePasswordSchema,
  ChangePasswordFormValues,
} from '@/components/validations/changePassword';
import { myApi } from '@/api/fetcher';
import { toast } from 'sonner';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { AlertCircle } from 'lucide-react';

interface ChangePasswordProps {
  open: boolean;
  profile: any;
  setOpen: (open: boolean) => void;
}

const ChangePassword: React.FC<ChangePasswordProps> = ({
  open,
  profile,
  setOpen,
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const form = useForm<ChangePasswordFormValues>({
    resolver: zod<PERSON><PERSON>olver(changePasswordSchema),
    defaultValues: {
      currentPassword: '',
      newPassword: '',
      confirmPassword: '',
    },
    mode: 'onChange',
  });

  const onSubmit = async (data: ChangePasswordFormValues) => {
    try {
      setIsLoading(true);
      setError(null);

      // Ensure profile.id is included in the payload
      const payload = {
        currentPassword: data.currentPassword,
        password: data.newPassword,
        id: profile?.id,
      };

      const response = await myApi.patch('/staff/update', payload);

      if (response.status === 200) {
        toast.success('Password changed successfully');
        setOpen(false);
        form.reset();
      }
    } catch (error: any) {
      setError(
        error?.response?.data?.message ||
          'Failed to change password. Please try again.'
      );
      toast.error('Failed to change password');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Modal
      open={open}
      setOpen={setOpen}
      title="Change Password"
      description="Update your account password"
      isLoading={isLoading}
      onSubmit={form.handleSubmit(onSubmit)}
    >
      <Form {...form}>
        <form className="space-y-4">
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <PasswordField
            control={form.control}
            name="currentPassword"
            label="Current Password"
            placeholder="Enter your current password"
          />

          <PasswordField
            control={form.control}
            name="newPassword"
            label="New Password"
            placeholder="Enter your new password"
          />

          <PasswordField
            control={form.control}
            name="confirmPassword"
            label="Confirm New Password"
            placeholder="Confirm your new password"
          />
        </form>
      </Form>
    </Modal>
  );
};

export default ChangePassword;
