import React, { useState } from 'react';
import { InputField, FormRow } from '@/components/common/form';
import { Form } from '@/components/ui/form';
import { Modal } from '@/components/common/modal';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { myApi } from '@/api/fetcher';
import { toast } from 'sonner';
import { ModalProps } from '../../types';
import {
  RewardFormSchema,
  RewardFormValues,
} from '@/components/validations/reward';
import { CustomSelectForm } from '@/components/common/another';

const Create: React.FC<ModalProps> = ({ setOpen, mutate, open }) => {
  const [isLoading, setIsLoading] = useState(false);

  const rewardTypes = [
    {
      id: 'PERCENTAGE',
      name: 'Percentage',
    },
    {
      id: 'POINTS',
      name: 'Points',
    },
    {
      id: 'CASH',
      name: 'Cash',
    },
  ];

  const rewardNames = [
    {
      id: 'STAFF_PACKAGE_REFERRAL_BONUS',
      name: 'Staff Package Referral Bonus',
    },
  ];

  const form = useForm<RewardFormValues>({
    resolver: zodResolver(RewardFormSchema),
    defaultValues: {
      description: '',
      value: '',
    },
  });

  const onSubmit = async (data: RewardFormValues) => {
    try {
      setIsLoading(true);
      const res = await myApi.post('/reward/create', {
        name: data.name,
        description: data.description,
        value: Number(data.value),
        type: data.type,
      });
      setIsLoading(false);
      if (res.status === 200) {
        toast.success(res.data.data.message || 'Reward created successfully');
        if (mutate) {
          mutate();
        }
        setOpen(false);
        form.reset();
      }
    } catch (error) {
      setIsLoading(false);
      toast.error('Failed to create reward');
    }
  };

  return (
    <Modal
      open={open}
      setOpen={setOpen}
      title="Add New Reward"
      description="Create a new reward for the system"
      isLoading={isLoading}
      onSubmit={form.handleSubmit(onSubmit)}
    >
      <Form {...form}>
        <form className="space-y-4">
          <FormRow>
            <CustomSelectForm
              control={form.control}
              name="type"
              label="Reward Type"
              placeholder="Select reward type"
              options={rewardTypes}
            />
            <InputField
              control={form.control}
              name="value"
              label="Value"
              placeholder="Enter reward value"
              type="number"
            />
          </FormRow>
          <CustomSelectForm
            control={form.control}
            name="name"
            label="Reward Name"
            placeholder="Select reward type"
            options={rewardNames}
          />
          {/* <InputField
            control={form.control}
            name="name"
            label="Reward Name"
            placeholder="Enter reward name"
            type="text"
          /> */}
          <InputField
            control={form.control}
            name="description"
            label="Reward Description"
            placeholder="Enter reward description"
            type="text"
          />
        </form>
      </Form>
    </Modal>
  );
};

export default Create;
