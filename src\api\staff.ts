import useSWR from 'swr';
import { useAuthSWR } from './useAuthSWR';

export const GetRoles = () => {
  const { data, mutate, isLoading } = useAuthSWR(`/admin/roles`);

  return {
    roles: data,
    roleLoading: isLoading,
    mutate: mutate,
  };
};

export const GetSpecialty = () => {
  const { data, isLoading, mutate } = useSWR(`/staff/list-specialty`);

  return {
    specialty: data,
    specialtyLoading: isLoading,
    mutate: mutate,
  };
};

//Admin profile data
export const GetProfile = () => {
  const { data, isLoading, error } = useAuthSWR('/staff/profile', {
    refreshInterval: 0,
    revalidateOnFocus: false,
    revalidateOnReconnect: false,
    revalidateOnMount: true,
    dedupingInterval: 0,
  });

  return {
    profile: data,
    isLoading: isLoading,
    error: error,
  };
};

// Get staff list
export const GetStaffList = () => {
  const { data, isLoading, mutate, error } = useAuthSWR('/staff/list');

  return {
    staffList: data?.data || [],
    isLoading,
    error,
    mutate: mutate,
  };
};

//Roles & Permissions
export const GetRoleList = () => {
  const { data, isLoading, mutate, error } = useAuthSWR('/staff/list-role');

  return {
    roleList: data?.data,
    isLoading,
    error,
    mutate: mutate,
  };
};

export const GetPermissionList = () => {
  const { data, isLoading, error } = useAuthSWR('/staff/list-permission');

  return {
    permissionList: data?.data,
    permissionLoading: isLoading,
    error,
  };
};
