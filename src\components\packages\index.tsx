'use client';

import { Tick<PERSON><PERSON><PERSON><PERSON>, Per<PERSON>, Group } from 'lucide-react';
import Link from 'next/link';
import { Paths } from '@/components/navigations/data';
import { Button } from '@/components/ui/button';
import Booking from './booking/components/data-table';
import { hasPermission, PERMISSIONS } from '@/lib/types/permissions';

export default function PackageBooking() {
  const permitView = hasPermission(PERMISSIONS.PACKAGE_VIEW);
  return (
    <>
      <div className="flex flex-wrap justify-between">
        <h2 className="text-lg font-bold text-gray-900 dark:text-white mb-4 text-left flex items-center gap-2">
          <TicketCheck className="w-5 h-5 text-zinc-900 dark:text-zinc-50" />
          Booking Information
        </h2>
        <div>
          {permitView && (
            <div className="flex flex-wrap gap-2">
              <Link href={`${Paths.Packages}/manage-category`}>
                <Button variant="outline" className="cursor-pointer">
                  <Group className="w-3.5 h-3.5" /> Category
                </Button>
              </Link>
              <Link href={`${Paths.Packages}/manage-package`}>
                <Button className="cursor-pointer">
                  <Percent className="w-3.5 h-3.5" /> Package
                </Button>
              </Link>
            </div>
          )}
        </div>
      </div>
      <div className="mt-4 bg-white dark:bg-[#0F0F12] rounded-xl p-4 flex flex-col items-start justify-start border border-gray-200 dark:border-[#1F1F23]">
        <Booking />
      </div>
    </>
  );
}
