'use client';

import { useCallback } from 'react';
import { useSnapshot } from 'valtio';
import socketService from '@/services/websocket';
import { forumStore } from '@/store/forumStore';

export function useWebSocket() {
  const { connectionStatus } = useSnapshot(forumStore);
  const isConnected = connectionStatus === 'connected';

  const on = useCallback((event: string, callback: (data: any) => void) => {
    socketService.on(event, callback);
    return () => {
      socketService.off(event, callback);
    };
  }, []);

  const off = useCallback((event: string, callback: (data: any) => void) => {
    socketService.off(event, callback);
  }, []);

  const emit = useCallback((event: string, data: any) => {
    socketService.emit(event, data);
  }, []);

  const connect = useCallback(() => {
    socketService.connect();
  }, []);

  const disconnect = useCallback(() => {
    socketService.disconnect();
  }, []);

  return {
    isConnected,
    connectionStatus,
    on,
    off,
    emit,
    connect,
    disconnect,
  };
}
