import { useState } from 'react';
import { Modal } from '@/components/common/modal';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { AlertTriangle, ArrowLeftRight, Banknote } from 'lucide-react';
import { currencyFormat } from '@/lib/utils';
import { myApi } from '@/api/fetcher';
import { toast } from 'sonner';

interface WithdrawModalProps {
  open: boolean;
  setOpen: (open: boolean) => void;
  walletBalance: number;
  setProfileOpen?: (open: boolean) => void;
}

export const WithdrawModal = ({ open, setOpen, walletBalance, setProfileOpen }: WithdrawModalProps) => {
  const [amount, setAmount] = useState('');
  const [transferAmount, setTransferAmount] = useState('');
  const [bankDetails, setBankDetails] = useState('');
  const [recipientStaffId, setRecipientStaffId] = useState('');
  const [isLoading, setIsLoading] = useState(false)
  const [displayValue, setDisplayValue] = useState('');
  const [transferDisplayValue, setTransferDisplayValue] = useState('');
  const [withdrawPassword, setWithdrawPassword] = useState('');
  const [transferPassword, setTransferPassword] = useState('');
  const minWithdrawal = 10000;
  const withholdingTax = 0.05;

  const handleWithdraw = async() => {
    if (!amount || !bankDetails || !withdrawPassword) {
      toast.error('Please fill in all fields');
      return;
    }
    setIsLoading(true)
    const payload = {
      amount: amount,
      details: bankDetails,
      password: withdrawPassword,
    };
    try {
      const res = await myApi.post('/transaction/withdrawal-request', payload);
      if (res.status === 200) {
        toast.success(res.data.data.message);
        setIsLoading(false)
        setOpen(false);
        setProfileOpen?.(false);
        setAmount('');
        setBankDetails('');
        setWithdrawPassword('');
      }
    }catch(error){
      console.log(error);
      setIsLoading(false)
    }
  };

  const handleTransfer = async() => {
    if (!transferAmount || !recipientStaffId || !transferPassword) {
      toast.error('Please fill in all fields');
      return;
    }
    setIsLoading(true)
    const payload = {
      amount: transferAmount,
      toStaffId: recipientStaffId,
      password: transferPassword,
    };
    console.log(payload);
    try {
      const res = await myApi.post('/transaction/transfer-funds', payload);
      if (res.status === 200) {
        toast.success(res.data.data.message);
        setIsLoading(false)
        setOpen(false);
        setProfileOpen?.(false);
        setTransferAmount('');
        setRecipientStaffId('');
        setTransferPassword('');
      }
    }catch(error){
      console.log(error);
      setIsLoading(false)
    }
  };

  const inputAmount = parseFloat(amount) || 0;
  const transferInputAmount = parseFloat(transferAmount) || 0;
  const netAmount = inputAmount * (1 - withholdingTax);
  const isValidAmount = inputAmount >= minWithdrawal && inputAmount <= walletBalance;
  const isValidTransferAmount = transferInputAmount > 0 && transferInputAmount <= walletBalance;
  const exceedsBalance = inputAmount > walletBalance;
  const transferExceedsBalance = transferInputAmount > walletBalance;

  return (
    <Modal
      open={open}
      setOpen={setOpen}
      title="Wallet Operations"
      description="Withdraw funds or transfer to another staff member"
      size="lg"
    >
      <Tabs defaultValue="withdraw" className="w-full">
        <TabsList className="grid grid-cols-2 mb-4">
          <TabsTrigger value="withdraw" className="flex items-center gap-2">
            <Banknote className="h-4 w-4" />
            Withdraw Funds
          </TabsTrigger>
          <TabsTrigger value="transfer" className="flex items-center gap-2">
            <ArrowLeftRight className="h-4 w-4" />
            Transfer Funds
          </TabsTrigger>
        </TabsList>

        <TabsContent value="withdraw" className="space-y-4">
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-2">
            <div className="flex items-start gap-3">
              <AlertTriangle className="h-5 w-5 text-yellow-600 mt-0.5" />
              <div className="space-y-1 text-xs">
                <p className="font-medium text-yellow-800">Important Information:</p>
                <ul className="space-y-1 text-yellow-700">
                  <li>• Minimum withdrawal limit: ₦{minWithdrawal.toLocaleString()}</li>
                  <li>• 5% withholding tax will be deducted</li>
                  <li>• Processing may take 1-3 business days</li>
                  <li>• Withdrawal is limited to once per month</li>
                </ul>
              </div>
            </div>
          </div>

          <div className="space-y-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Amount to Withdraw</label>
              <Input
                placeholder="Enter amount"
                value={displayValue}
                onChange={(e) => {
                  const rawValue = e.target.value.replace(/[^\d]/g, '');
                  const formattedValue = rawValue
                    ? new Intl.NumberFormat('en-US').format(parseInt(rawValue))
                    : '';
                  setDisplayValue(formattedValue);
                  setAmount(rawValue);
                }}
              />
              <p className="text-xs text-green-600">
                Available balance: {currencyFormat(walletBalance)}
              </p>
              {exceedsBalance && (
                <p className="text-sm text-red-600">
                  Amount exceeds available balance
                </p>
              )}
              {inputAmount > 0 && inputAmount < minWithdrawal && !exceedsBalance && (
                <p className="text-sm text-red-600">
                  Minimum withdrawal amount is ₦{minWithdrawal.toLocaleString()}
                </p>
              )}
              {inputAmount > 0 && !exceedsBalance && (
                <p className="text-sm text-muted-foreground">
                  Net amount after 5% tax: ₦{netAmount.toLocaleString()}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Bank Details</label>
              <Input
                placeholder="Account Number - Bank Name"
                value={bankDetails}
                onChange={(e) => setBankDetails(e.target.value)}
              />
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Enter Password</label>
              <Input
                type="password"
                placeholder="Enter your password"
                value={withdrawPassword}
                onChange={(e) => setWithdrawPassword(e.target.value)}
              />
            </div>
          </div>

          <div className="flex gap-2 pt-4">
            <Button variant="outline" onClick={() => setOpen(false)} className="flex-1">
              Cancel
            </Button>
            <Button 
              onClick={handleWithdraw}
              disabled={walletBalance === 0 || !isValidAmount || !bankDetails.trim() || !withdrawPassword.trim() || isLoading}
              className="flex-1"
            >
              Withdraw
            </Button>
          </div>
        </TabsContent>

        <TabsContent value="transfer" className="space-y-4">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-2">
            <div className="flex items-start gap-3">
              <ArrowLeftRight className="h-5 w-5 text-blue-600 mt-0.5" />
              <div className="space-y-1 text-xs">
                <p className="font-medium text-blue-800">Transfer Information:</p>
                <ul className="space-y-1 text-blue-700">
                  <li>• Transfer funds instantly to another staff member</li>
                  <li>• No fees or taxes applied</li>
                  <li>• Recipient will be notified immediately</li>
                </ul>
              </div>
            </div>
          </div>

          <div className="space-y-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Amount to Transfer</label>
              <Input
                placeholder="Enter amount"
                value={transferDisplayValue}
                onChange={(e) => {
                  const rawValue = e.target.value.replace(/[^\d]/g, '');
                  const formattedValue = rawValue
                    ? new Intl.NumberFormat('en-US').format(parseInt(rawValue))
                    : '';
                  setTransferDisplayValue(formattedValue);
                  setTransferAmount(rawValue);
                }}
              />
              <p className="text-xs text-green-600">
                Available balance: {currencyFormat(walletBalance)}
              </p>
              {transferExceedsBalance && (
                <p className="text-sm text-red-600">
                  Amount exceeds available balance
                </p>
              )}
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Recipient Staff ID</label>
              <Input
                placeholder="Enter staff ID"
                value={recipientStaffId}
                onChange={(e) => setRecipientStaffId(e.target.value)}
              />
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Enter Password</label>
              <Input
                type="password"
                placeholder="Enter your password"
                value={transferPassword}
                onChange={(e) => setTransferPassword(e.target.value)}
              />
            </div>
          </div>

          <div className="flex gap-2 pt-4">
            <Button variant="outline" onClick={() => setOpen(false)} className="flex-1">
              Cancel
            </Button>
            <Button 
              onClick={handleTransfer}
              disabled={walletBalance === 0 || !isValidTransferAmount || !recipientStaffId.trim() || !transferPassword.trim() || isLoading}
              className="flex-1"
            >
              Transfer
            </Button>
          </div>
        </TabsContent>
      </Tabs>
    </Modal>
  );
};