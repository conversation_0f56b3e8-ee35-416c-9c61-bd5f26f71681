'use client';

import React, { useState } from 'react';
import {
  LineChart,
  Line,
  BarChart,
  Bar,
  AreaChart,
  Area,
  ComposedChart,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from 'recharts';
import { ChartType } from './chart-type-selector';
import { AnalysisResult } from '@/lib/data-analysis/parser';

interface DateVisualizationProps {
  columnName: string;
  analysisResult: AnalysisResult;
  chartType: ChartType;
}

const COLORS = [
  '#BD9A3D',
  '#2196F3',
  '#1A1A1A',
  '#8884d8',
  '#82ca9d',
  '#ffc658',
];

export const DateVisualization: React.FC<DateVisualizationProps> = ({
  columnName,
  analysisResult,
  chartType,
}) => {
  // For demonstration purposes, we'll generate some time series data
  // In a real implementation, this would come from the analysisResult
  const generateTimeSeriesData = () => {
    const today = new Date();
    const data = [];

    for (let i = 0; i < 12; i++) {
      const date = new Date(today);
      date.setMonth(today.getMonth() - 11 + i);

      data.push({
        date: date.toLocaleDateString('en-US', {
          month: 'short',
          year: 'numeric',
        }),
        value: Math.floor(Math.random() * 100) + 20,
        trend: Math.floor(Math.random() * 80) + 10,
      });
    }

    return data;
  };

  const timeSeriesData = generateTimeSeriesData();

  return (
    <div className="h-64">
      <ResponsiveContainer width="100%" height="100%">
        <>
          {chartType === 'line' && (
            <LineChart
              data={timeSeriesData}
              margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
            >
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="date" />
              <YAxis />
              <Tooltip />
              <Legend />
              <Line
                type="monotone"
                dataKey="value"
                name={columnName}
                stroke={COLORS[0]}
                strokeWidth={2}
                activeDot={{ r: 8 }}
              />
            </LineChart>
          )}

          {chartType === 'bar-numeric' && (
            <BarChart
              data={timeSeriesData}
              margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
            >
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="date" />
              <YAxis />
              <Tooltip />
              <Legend />
              <Bar dataKey="value" name={columnName} fill={COLORS[0]} />
            </BarChart>
          )}

          {chartType === 'area' && (
            <AreaChart
              data={timeSeriesData}
              margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
            >
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="date" />
              <YAxis />
              <Tooltip />
              <Legend />
              <Area
                type="monotone"
                dataKey="value"
                name={columnName}
                fill={COLORS[0]}
                stroke={COLORS[0]}
                fillOpacity={0.6}
              />
            </AreaChart>
          )}

          {/* Composite chart showing both line and bar */}
          {chartType === 'composite' && (
            <ComposedChart
              data={timeSeriesData}
              margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
            >
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="date" />
              <YAxis />
              <Tooltip />
              <Legend />
              <Bar
                dataKey="value"
                name={`${columnName} Value`}
                fill={COLORS[0]}
                barSize={20}
              />
              <Line
                type="monotone"
                dataKey="trend"
                name={`${columnName} Trend`}
                stroke={COLORS[1]}
                strokeWidth={2}
              />
            </ComposedChart>
          )}
        </>
      </ResponsiveContainer>
    </div>
  );
};

export default DateVisualization;
