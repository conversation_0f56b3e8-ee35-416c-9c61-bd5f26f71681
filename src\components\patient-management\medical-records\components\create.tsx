import React, { useState, useEffect } from 'react';
import { InputField } from '@/components/common/form';
import { Form } from '@/components/ui/form';
import { Modal } from '@/components/common/modal';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { myApi } from '@/api/fetcher';
import { toast } from 'sonner';
import { GetProfile } from '@/api/staff';
import { GetPatients } from '@/api/crm/data';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { ModalProps } from '@/components/crm/types';
import { useSearchParams } from 'next/navigation';
import { z } from 'zod';

// Define a schema for the medical record form
const medicalRecordFormSchema = z.object({
  patientId: z.number().or(z.string().transform((val) => Number(val))),
  recordType: z.enum(['consultation', 'lab', 'procedure', 'other']),
  recordDate: z.string().min(1, { message: 'Record date is required' }),
  doctorId: z
    .number()
    .or(z.string().transform((val) => Number(val)))
    .optional(),
  diagnosis: z.string().optional(),
  treatment: z.string().optional(),
  notes: z.string().optional(),
});

type MedicalRecordFormValues = z.infer<typeof medicalRecordFormSchema>;

const Create: React.FC<ModalProps> = ({ setOpen, open, mutate }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [patients, setPatients] = useState<any[]>([]);
  const [doctors, setDoctors] = useState<any[]>([]);
  const [patientsLoading, setPatientsLoading] = useState(false);
  const [doctorsLoading, setDoctorsLoading] = useState(false);
  const { profile } = GetProfile();
  const searchParams = useSearchParams();
  const patientIdFromUrl = searchParams.get('patientId');

  const form = useForm<MedicalRecordFormValues>({
    resolver: zodResolver(medicalRecordFormSchema),
    defaultValues: {
      patientId: patientIdFromUrl ? parseInt(patientIdFromUrl) : 0,
      recordType: 'consultation',
      recordDate: new Date().toISOString().split('T')[0],
      doctorId: 0,
      diagnosis: '',
      treatment: '',
      notes: '',
    },
  });

  // Fetch patients for dropdown
  useEffect(() => {
    const fetchPatients = async () => {
      if (open) {
        try {
          setPatientsLoading(true);
          const response = await myApi.get('/crm/patients?limit=100');
          if (response.status === 200) {
            setPatients(response.data.data.patients);
          }
        } catch (error) {
          console.error('Error fetching patients:', error);
        } finally {
          setPatientsLoading(false);
        }
      }
    };

    const fetchDoctors = async () => {
      if (open) {
        try {
          setDoctorsLoading(true);
          const response = await myApi.get('/crm/doctors?limit=100');
          if (response.status === 200) {
            setDoctors(response.data.data.doctors);
          }
        } catch (error) {
          console.error('Error fetching doctors:', error);
        } finally {
          setDoctorsLoading(false);
        }
      }
    };

    fetchPatients();
    fetchDoctors();
  }, [open]);

  const onSubmit = async (data: MedicalRecordFormValues) => {
    try {
      setIsLoading(true);
      const res = await myApi.post('/crm/medical-records', {
        ...data,
        createdBy: profile.data.fullName,
      });
      setIsLoading(false);
      if (res.status === 200) {
        toast.success('Medical record added successfully');
        if (mutate) {
          mutate();
        }
        setOpen(false);
        form.reset();
      }
    } catch (error: any) {
      setIsLoading(false);
      toast.error(error?.response?.data?.message || 'An error occurred');
    }
  };

  return (
    <Modal
      title="Add Medical Record"
      open={open}
      setOpen={setOpen}
      isLoading={isLoading}
      description="Add a new medical record for a patient"
      onSubmit={form.handleSubmit(onSubmit)}
    >
      <Form {...form}>
        <form className="space-y-4">
          <div>
            <label className="text-sm font-medium">Patient</label>
            <Select
              onValueChange={(value) =>
                form.setValue('patientId', parseInt(value))
              }
              disabled={patientsLoading || !!patientIdFromUrl}
              defaultValue={patientIdFromUrl || undefined}
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Select a patient" />
              </SelectTrigger>
              <SelectContent>
                {patients.map((patient) => (
                  <SelectItem key={patient.id} value={patient.id.toString()}>
                    {patient.firstName} {patient.lastName}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {form.formState.errors.patientId && (
              <p className="text-xs text-red-500 mt-1">
                {form.formState.errors.patientId.message}
              </p>
            )}
          </div>

          <div>
            <label className="text-sm font-medium">Record Type</label>
            <Select
              onValueChange={(value) =>
                form.setValue('recordType', value as any)
              }
              defaultValue={form.getValues('recordType')}
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Select record type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="consultation">Consultation</SelectItem>
                <SelectItem value="lab">Lab Results</SelectItem>
                <SelectItem value="procedure">Procedure</SelectItem>
                <SelectItem value="other">Other</SelectItem>
              </SelectContent>
            </Select>
            {form.formState.errors.recordType && (
              <p className="text-xs text-red-500 mt-1">
                {form.formState.errors.recordType.message}
              </p>
            )}
          </div>

          <div>
            <label className="text-sm font-medium">Record Date</label>
            <input
              type="date"
              className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
              {...form.register('recordDate')}
            />
            {form.formState.errors.recordDate && (
              <p className="text-xs text-red-500 mt-1">
                {form.formState.errors.recordDate.message}
              </p>
            )}
          </div>

          <div>
            <label className="text-sm font-medium">Doctor</label>
            <Select
              onValueChange={(value) =>
                form.setValue('doctorId', parseInt(value))
              }
              disabled={doctorsLoading}
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Select a doctor" />
              </SelectTrigger>
              <SelectContent>
                {doctors.map((doctor) => (
                  <SelectItem key={doctor.id} value={doctor.id.toString()}>
                    {doctor.firstName} {doctor.lastName}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div>
            <label className="text-sm font-medium">Diagnosis</label>
            <Textarea
              placeholder="Enter diagnosis details"
              className="resize-none"
              {...form.register('diagnosis')}
            />
          </div>

          <div>
            <label className="text-sm font-medium">Treatment</label>
            <Textarea
              placeholder="Enter treatment details"
              className="resize-none"
              {...form.register('treatment')}
            />
          </div>

          <div>
            <label className="text-sm font-medium">Notes</label>
            <Textarea
              placeholder="Additional notes"
              className="resize-none"
              {...form.register('notes')}
            />
          </div>
        </form>
      </Form>
    </Modal>
  );
};

export default Create;
