import React, { useState } from 'react';
import { InputField } from '@/components/common/form';
import { Form } from '@/components/ui/form';
import { Modal } from '@/components/common/modal';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { formSchema } from '@/components/validations/createCategory';
import { myApi } from '@/api/fetcher';
import { toast } from 'sonner';
import { ModalProps } from '../../../types';

interface FormData {
  name: string;
}

const NewInvestigation: React.FC<ModalProps> = ({ setOpen, mutate, open }) => {
  const [isLoading, setIsLoading] = useState(false);

  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: '',
    },
  });

  const onSubmit = async (data: FormData) => {
    try {
      setIsLoading(true);
      const res = await myApi.post('/package/add-investigation', {
        name: data.name,
      });
      setIsLoading(false);
      if (res.status === 200) {
        toast.success(res.data.data.message);
        if (mutate) {
          mutate();
        }
        setOpen(false);
        form.reset();
      }
    } catch (error) {
      setIsLoading(false);
    }
  };

  return (
    <Modal
      open={open}
      setOpen={setOpen}
      title="Add Investigation"
      description="Enter an investigation name to add to the list of existing investigations"
      isLoading={isLoading}
      onSubmit={form.handleSubmit(onSubmit)}
    >
      <Form {...form}>
        <form className="space-y-8">
          <InputField
            control={form.control}
            name="name"
            label="Investigation Name"
            placeholder="colonoscopy"
            type="text"
          />
        </form>
      </Form>
    </Modal>
  );
};

export default NewInvestigation;
