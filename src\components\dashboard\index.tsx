'use client';

import { useState } from 'react';
import {
  ChartNoAxesCombined,
  MessageCircleMore,
  BadgePercent,
  Calendar,
  Users,
} from 'lucide-react';
import { hasPermission, PERMISSIONS } from '@/lib/types/permissions';

import { EnhancedBookingAnalytics } from './booking/booking-analytics';
import { FeedbackAnalytics } from './feedback/feedback-analytics';
import { TransactionAnalytics } from './transaction/transaction-analytics';
import { DiscountAnalytics } from './discount/discount-analytics';
import { StaffReferralAnalytics } from './referral/staff-referral-analytics';

import { Ta<PERSON>, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs';

export default function DashboardContent() {
  const [activeTab, setActiveTab] = useState('booking');

  return (
    <div className="space-y-6">
      <h2 className="text-lg font-bold text-gray-900 dark:text-white mb-4 text-left flex items-center gap-2">
        <ChartNoAxesCombined className="w-5 h-5 text-zinc-900 dark:text-zinc-50" />
        Dashboard Analytics
      </h2>

      <Tabs
        value={activeTab}
        onValueChange={setActiveTab}
        className="w-full animate-in fade-in-50"
      >
        <TabsList className="grid grid-cols-5 mb-6">
          <TabsTrigger
            value="booking"
            className="flex items-center gap-2 data-[state=active]:bg-background data-[state=active]:text-primary"
          >
            <Calendar
              className="w-4 h-4"
              style={{ color: activeTab === 'booking' ? '#BD9A3D' : 'inherit' }}
            />
            Booking
          </TabsTrigger>
          <TabsTrigger
            value="feedback"
            className="flex items-center gap-2 data-[state=active]:bg-background data-[state=active]:text-primary"
          >
            <MessageCircleMore
              className="w-4 h-4"
              style={{
                color: activeTab === 'feedback' ? '#BD9A3D' : 'inherit',
              }}
            />
            Feedback
          </TabsTrigger>
          <TabsTrigger
            value="transaction"
            className="flex items-center gap-2 data-[state=active]:bg-background data-[state=active]:text-primary"
          >
            <span
              style={{
                color: activeTab === 'transaction' ? '#BD9A3D' : 'inherit',
              }}
            >
              ₦
            </span>
            Transaction
          </TabsTrigger>
          <TabsTrigger
            value="discount"
            className="flex items-center gap-2 data-[state=active]:bg-background data-[state=active]:text-primary"
          >
            <BadgePercent
              className="w-4 h-4"
              style={{
                color: activeTab === 'discount' ? '#BD9A3D' : 'inherit',
              }}
            />
            Discount
          </TabsTrigger>
          <TabsTrigger
            value="referral"
            className="flex items-center gap-2 data-[state=active]:bg-background data-[state=active]:text-primary"
          >
            <Users
              className="w-4 h-4"
              style={{
                color: activeTab === 'referral' ? '#BD9A3D' : 'inherit',
              }}
            />
            Referrals
          </TabsTrigger>
        </TabsList>

        {/* Booking Analytics Tab */}
        <TabsContent
          value="booking"
          className="mt-0 animate-in fade-in-50 duration-300"
        >
          <div className="space-y-4">
            <h3 className="text-md font-semibold text-gray-800 dark:text-gray-200 flex items-center gap-2">
              <Calendar className="w-4 h-4" style={{ color: '#BD9A3D' }} />
              Booking Analytics
            </h3>
            <div>
              {hasPermission(PERMISSIONS.PACKAGE_VIEW) && (
                <EnhancedBookingAnalytics />
              )}
            </div>
          </div>
        </TabsContent>

        {/* Feedback Analytics Tab */}
        <TabsContent
          value="feedback"
          className="mt-0 animate-in fade-in-50 duration-300"
        >
          <div className="space-y-4">
            <h3 className="text-md font-semibold text-gray-800 dark:text-gray-200 flex items-center gap-2">
              <MessageCircleMore
                className="w-4 h-4"
                style={{ color: '#BD9A3D' }}
              />
              Feedback Analytics
            </h3>
            <div>
              <FeedbackAnalytics />
            </div>
          </div>
        </TabsContent>

        {/* Transaction Analytics Tab */}
        <TabsContent
          value="transaction"
          className="mt-0 animate-in fade-in-50 duration-300"
        >
          <div className="space-y-4">
            <h3 className="text-md font-semibold text-gray-800 dark:text-gray-200 flex items-center gap-2">
              <span className="text-[#BD9A3D]">₦</span>
              Transaction Analytics
            </h3>
            <div>
              {hasPermission(PERMISSIONS.TRANSACTION_VIEW) && (
                <TransactionAnalytics />
              )}
            </div>
          </div>
        </TabsContent>

        {/* Discount Analytics Tab */}
        <TabsContent
          value="discount"
          className="mt-0 animate-in fade-in-50 duration-300"
        >
          <div className="space-y-4">
            <h3 className="text-md font-semibold text-gray-800 dark:text-gray-200 flex items-center gap-2">
              <BadgePercent className="w-4 h-4" style={{ color: '#BD9A3D' }} />
              Discount Analytics
            </h3>
            <div>
              {hasPermission(PERMISSIONS.REWARD_VIEW) && <DiscountAnalytics />}
            </div>
          </div>
        </TabsContent>

        {/* Staff Referral Analytics Tab */}
        <TabsContent
          value="referral"
          className="mt-0 animate-in fade-in-50 duration-300"
        >
          <div className="space-y-4">
            <h3 className="text-md font-semibold text-gray-800 dark:text-gray-200 flex items-center gap-2">
              <Users className="w-4 h-4" style={{ color: '#BD9A3D' }} />
              Staff Referral Analytics
            </h3>
            <div>
              {hasPermission(PERMISSIONS.STAFF_VIEW) && (
                <StaffReferralAnalytics />
              )}
            </div>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
