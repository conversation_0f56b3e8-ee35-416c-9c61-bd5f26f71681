import React, { useState } from 'react';
import { format } from 'date-fns';
import { MoreHorizontal, Pin, Trash, Edit, PinOff } from 'lucide-react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Textarea } from '@/components/ui/textarea';
import { ForumMessage } from '@/api/forum/data';
import { hasPermission, PERMISSIONS } from '@/lib/types/permissions';
import { GetProfile } from '@/api/staff';
import {
  editMessage,
  deleteMessage,
  pinMessage,
  unpinMessage,
} from '@/api/forum/data';

interface ChatMessageProps {
  message: ForumMessage;
  onMessageUpdated: () => void;
}

export const ChatMessage: React.FC<ChatMessageProps> = ({
  message,
  onMessageUpdated,
}) => {
  const { profile } = GetProfile();
  const [isEditing, setIsEditing] = useState(false);
  const [editedContent, setEditedContent] = useState(message.content);

  const isOwnMessage = profile?.data?.id === message.senderId;
  const canEditMessage =
    isOwnMessage || hasPermission(PERMISSIONS.FORUM_EDIT_MESSAGE);
  const canDeleteMessage =
    isOwnMessage || hasPermission(PERMISSIONS.FORUM_DELETE_MESSAGE);
  const canPinMessage = hasPermission(PERMISSIONS.FORUM_PIN_MESSAGE);
  const canModerate = hasPermission(PERMISSIONS.FORUM_MODERATE_MESSAGES);

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map((n) => n[0])
      .join('')
      .toUpperCase()
      .substring(0, 2);
  };

  const handleEdit = async () => {
    if (isEditing) {
      try {
        await editMessage(message.id, editedContent);
        setIsEditing(false);
        onMessageUpdated();
      } catch (error) {
        console.error('Error editing message:', error);
      }
    } else {
      setIsEditing(true);
    }
  };

  const handleDelete = async () => {
    if (confirm('Are you sure you want to delete this message?')) {
      try {
        await deleteMessage(message.id);
        onMessageUpdated();
      } catch (error) {
        console.error('Error deleting message:', error);
      }
    }
  };

  const handlePin = async () => {
    try {
      if (message.isPinned) {
        await unpinMessage(message.id);
      } else {
        await pinMessage(message.id);
      }
      onMessageUpdated();
    } catch (error) {
      console.error('Error pinning/unpinning message:', error);
    }
  };

  const formatMessageTime = (dateString: string) => {
    return format(new Date(dateString), 'h:mm a');
  };

  return (
    <div
      className={`p-2 md:p-3 rounded-lg mb-2 group ${message.isPinned ? 'bg-muted/50' : ''}`}
    >
      <div className="flex items-start gap-2 md:gap-3">
        <Avatar className="h-6 w-6 md:h-8 md:w-8 shrink-0">
          <AvatarImage src={message.senderAvatar} />
          <AvatarFallback className="text-xs md:text-sm">
            {getInitials(message.senderName)}
          </AvatarFallback>
        </Avatar>

        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-1 md:gap-2 flex-wrap">
            <span className="font-medium text-sm md:text-base truncate max-w-[120px] md:max-w-full">
              {message.senderName}
            </span>
            <span className="text-[10px] md:text-xs text-muted-foreground">
              {formatMessageTime(message.createdAt)}
            </span>
            {message.isPinned && (
              <Pin className="h-3 w-3 text-muted-foreground" />
            )}
          </div>

          {isEditing ? (
            <div className="mt-1">
              <Textarea
                value={editedContent}
                onChange={(e) => setEditedContent(e.target.value)}
                className="min-h-[40px] md:min-h-[60px] text-sm md:text-base"
              />
              <div className="flex justify-end gap-2 mt-2">
                <Button
                  variant="outline"
                  size="sm"
                  className="text-xs h-7 md:h-8"
                  onClick={() => {
                    setIsEditing(false);
                    setEditedContent(message.content);
                  }}
                >
                  Cancel
                </Button>
                <Button
                  size="sm"
                  className="text-xs h-7 md:h-8"
                  onClick={handleEdit}
                >
                  Save
                </Button>
              </div>
            </div>
          ) : (
            <div className="mt-1 whitespace-pre-wrap text-sm md:text-base break-words">
              {message.content}
            </div>
          )}

          {message.attachments && message.attachments.length > 0 && (
            <div className="mt-2 flex flex-wrap gap-1 md:gap-2">
              {message.attachments.map((attachment) => (
                <a
                  key={attachment.id}
                  href={attachment.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-xs md:text-sm text-blue-600 hover:underline flex items-center gap-1 bg-muted/50 px-2 py-1 rounded truncate max-w-[150px] md:max-w-[200px]"
                >
                  {attachment.name}
                </a>
              ))}
            </div>
          )}
        </div>

        {(canEditMessage || canDeleteMessage || canPinMessage || canModerate) &&
          !isEditing && (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-6 w-6 md:h-8 md:w-8 p-0 opacity-70 md:opacity-0 md:group-hover:opacity-100 shrink-0"
                >
                  <MoreHorizontal className="h-3 w-3 md:h-4 md:w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-[120px] md:w-auto">
                {(canEditMessage || canModerate) && (
                  <DropdownMenuItem
                    onClick={handleEdit}
                    className="text-xs md:text-sm py-1.5 md:py-2"
                  >
                    <Edit className="mr-2 h-3 w-3 md:h-4 md:w-4" />
                    Edit
                  </DropdownMenuItem>
                )}
                {(canDeleteMessage || canModerate) && (
                  <DropdownMenuItem
                    onClick={handleDelete}
                    className="text-xs md:text-sm py-1.5 md:py-2"
                  >
                    <Trash className="mr-2 h-3 w-3 md:h-4 md:w-4" />
                    Delete
                  </DropdownMenuItem>
                )}
                {(canPinMessage || canModerate) && (
                  <DropdownMenuItem
                    onClick={handlePin}
                    className="text-xs md:text-sm py-1.5 md:py-2"
                  >
                    {message.isPinned ? (
                      <>
                        <PinOff className="mr-2 h-3 w-3 md:h-4 md:w-4" />
                        Unpin
                      </>
                    ) : (
                      <>
                        <Pin className="mr-2 h-3 w-3 md:h-4 md:w-4" />
                        Pin
                      </>
                    )}
                  </DropdownMenuItem>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          )}
      </div>
    </div>
  );
};
