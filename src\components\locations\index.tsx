'use client';

import React, { useState } from 'react';
import { Plus, Group, MapPin } from 'lucide-react';
import { Button } from '@/components/ui/button';
import Breadcrumb from '@/components/common/breadcrumb';
import { Badge } from '../ui/badge';
import Location from './components/data-table';
import { hasPermission, PERMISSIONS } from '@/lib/types/permissions';

export default function LocationPage() {
  const [open, setOpen] = useState(false);
  const permitEdit = hasPermission(PERMISSIONS.PACKAGE_EDIT);

  const data = [
    { label: 'bookings', href: '/packages' },
    { label: 'category' },
  ];

  return (
    <>
      <h2 className="text-lg font-bold text-gray-900 dark:text-white mb-4 text-left flex items-center gap-2">
        <MapPin className="w-5 h-5 text-zinc-900 dark:text-zinc-50" />
        Hospital Locations
      </h2>
      <div className="flex gap-2">
        {hasPermission(PERMISSIONS.LOCATION_CREATE) && (
          <Badge
            className="cursor-pointer py-1.5 px-3"
            onClick={() => setOpen(true)}
          >
            Add location
          </Badge>
        )}
      </div>
      <div className="mt-4 dark:bg-[#0F0F12] rounded-xl p-4 flex flex-col items-start justify-start border border-gray-200 dark:border-[#1F1F23]">
        <Location
          permitEdit={permitEdit}
          openCreate={open}
          setOpenCreate={setOpen}
        />
      </div>
    </>
  );
}
