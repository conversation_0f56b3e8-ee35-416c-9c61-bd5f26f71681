'use client';

import { Arrow<PERSON><PERSON>Left, Users, BanknoteArrowUp } from 'lucide-react';
import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/button';
import Referral from './referral/data-table';
import { Paths } from '@/components/navigations/data';

export default function ReferringPage() {
  return (
    <>
      <div className="flex flex-wrap justify-between">
        <h2 className="text-lg font-bold text-gray-900 dark:text-white mb-4 text-left flex items-center gap-2">
          <ArrowRightLeft className="w-5 h-5 text-zinc-900 dark:text-zinc-50" />
          Referral Management
        </h2>
        <div>
          <div className="flex flex-wrap gap-2">
            <Link href={`${Paths.Referral}/referring-entities`}>
              <Button className="cursor-pointer">
                <Users className="w-3.5 h-3.5" /> Referring Entities
              </Button>
            </Link>
            {/* <Link href={`${Paths.Referral}/rewards`}>
              <Button variant="outline" className="cursor-pointer">
                <BanknoteArrowUp className="w-3.5 h-3.5" /> Rewards
              </Button>
            </Link> */}
          </div>
        </div>
      </div>
      <div className="mt-4 bg-white dark:bg-[#0F0F12] rounded-xl p-4 flex flex-col items-start justify-start border border-gray-200 dark:border-[#1F1F23]">
        <Referral />
      </div>
    </>
  );
}
