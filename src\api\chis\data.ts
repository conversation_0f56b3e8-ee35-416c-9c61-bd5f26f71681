import useSWR from 'swr';
import { myApi } from '@/api/fetcher';
import { toast } from 'sonner';

// Types for CHIS
export interface AuthorizationRequest {
  id: string;
  patientId: string;
  patientName: string;
  serviceType: string;
  requestedAmount: number;
  diagnosis?: string;
  notes?: string;
  status: 'pending' | 'approved' | 'rejected';
  requestDate: string;
  requestedBy: string;
  approvedBy?: string;
  approvedDate?: string;
  rejectionReason?: string;
  createdAt: string;
  updatedAt: string;
}

export interface AuthorizationCode {
  id: string;
  authorizationCode: string;
  requestId: string;
  patientId: string;
  patientName: string;
  serviceType: string;
  approvedAmount: number;
  expiryDate: string;
  status: 'active' | 'used' | 'expired' | 'cancelled';
  generatedBy: string;
  generatedDate: string;
  usedDate?: string;
  usedBy?: string;
  createdAt: string;
  updatedAt: string;
}

export interface CHISAnalytics {
  totalRequests: number;
  pendingRequests: number;
  approvedRequests: number;
  rejectedRequests: number;
  totalApprovedAmount: number;
  activeCodes: number;
  expiredCodes: number;
  usedCodes: number;
  monthlyStats: {
    month: string;
    requests: number;
    approved: number;
    amount: number;
  }[];
  serviceTypeBreakdown: {
    serviceType: string;
    count: number;
    percentage: number;
  }[];
}

export interface CreateAuthorizationRequestData {
  patientId: string;
  patientName?: string;
  serviceType: string;
  requestedAmount: number;
  diagnosis?: string;
  notes?: string;
}

export interface ApproveRequestData {
  approvedAmount?: number;
  notes?: string;
}

export interface RejectRequestData {
  rejectionReason: string;
  notes?: string;
}

// API Functions

// Get all authorization requests
export const GetAuthorizationRequests = (params?: string) => {
  const qs = new URLSearchParams(params);
  const { data, error, isLoading, mutate } = useSWR(
    `/chis/authorization-requests?${qs.toString()}`
  );

  return {
    requests: data?.data || [],
    pagination: data?.pagination,
    isLoading,
    error,
    mutate,
  };
};

// Get a specific authorization request
export const GetAuthorizationRequest = (requestId: string) => {
  const { data, error, isLoading, mutate } = useSWR(
    requestId ? `/chis/authorization-requests/${requestId}` : null
  );

  return {
    request: data?.data,
    isLoading,
    error,
    mutate,
  };
};

// Create new authorization request
export const createAuthorizationRequest = async (
  requestData: CreateAuthorizationRequestData
) => {
  try {
    const response = await myApi.post(
      '/chis/authorization-requests',
      requestData
    );
    toast.success('Authorization request created successfully');
    return response.data;
  } catch (error) {
    console.error('Error creating authorization request:', error);
    toast.error('Failed to create authorization request');
    throw error;
  }
};

// Approve authorization request
export const approveAuthorizationRequest = async (
  requestId: string,
  approvalData: ApproveRequestData
) => {
  try {
    const response = await myApi.patch(
      `/chis/authorization-requests/${requestId}/approve`,
      approvalData
    );
    toast.success('Authorization request approved successfully');
    return response.data;
  } catch (error) {
    console.error('Error approving authorization request:', error);
    toast.error('Failed to approve authorization request');
    throw error;
  }
};

// Reject authorization request
export const rejectAuthorizationRequest = async (
  requestId: string,
  rejectionData: RejectRequestData
) => {
  try {
    const response = await myApi.patch(
      `/chis/authorization-requests/${requestId}/reject`,
      rejectionData
    );
    toast.success('Authorization request rejected');
    return response.data;
  } catch (error) {
    console.error('Error rejecting authorization request:', error);
    toast.error('Failed to reject authorization request');
    throw error;
  }
};

// Get all authorization codes
export const GetAuthorizationCodes = (params?: string) => {
  const qs = new URLSearchParams(params);
  const { data, error, isLoading, mutate } = useSWR(
    `/chis/authorization-codes?${qs.toString()}`
  );

  return {
    codes: data?.data || [],
    pagination: data?.pagination,
    isLoading,
    error,
    mutate,
  };
};

// Get a specific authorization code
export const GetAuthorizationCode = (codeId: string) => {
  const { data, error, isLoading, mutate } = useSWR(
    codeId ? `/chis/authorization-codes/${codeId}` : null
  );

  return {
    code: data?.data,
    isLoading,
    error,
    mutate,
  };
};

// Generate authorization code from approved request
export const generateAuthorizationCode = async (requestId: string) => {
  try {
    const response = await myApi.post(
      `/chis/authorization-requests/${requestId}/generate-code`
    );
    toast.success('Authorization code generated successfully');
    return response.data;
  } catch (error) {
    console.error('Error generating authorization code:', error);
    toast.error('Failed to generate authorization code');
    throw error;
  }
};

// Use authorization code
export const useAuthorizationCode = async (
  codeId: string,
  usageData: { usedAmount: number; notes?: string }
) => {
  try {
    const response = await myApi.patch(
      `/chis/authorization-codes/${codeId}/use`,
      usageData
    );
    toast.success('Authorization code used successfully');
    return response.data;
  } catch (error) {
    console.error('Error using authorization code:', error);
    toast.error('Failed to use authorization code');
    throw error;
  }
};

// Cancel authorization code
export const cancelAuthorizationCode = async (
  codeId: string,
  reason: string
) => {
  try {
    const response = await myApi.patch(
      `/chis/authorization-codes/${codeId}/cancel`,
      { reason }
    );
    toast.success('Authorization code cancelled successfully');
    return response.data;
  } catch (error) {
    console.error('Error cancelling authorization code:', error);
    toast.error('Failed to cancel authorization code');
    throw error;
  }
};

// Get CHIS analytics
export const GetCHISAnalytics = (params?: string) => {
  const qs = new URLSearchParams(params);
  const { data, error, isLoading, mutate } = useSWR(
    `/chis/analytics?${qs.toString()}`
  );

  return {
    analytics: data?.data as CHISAnalytics,
    isLoading,
    error,
    mutate,
  };
};

// Verify patient eligibility
export const verifyPatientEligibility = async (patientId: string) => {
  try {
    const response = await myApi.get(`/chis/patients/${patientId}/eligibility`);
    return response.data;
  } catch (error) {
    console.error('Error verifying patient eligibility:', error);
    toast.error('Failed to verify patient eligibility');
    throw error;
  }
};

// Get patient CHIS history
export const GetPatientCHISHistory = (patientId: string) => {
  const { data, error, isLoading, mutate } = useSWR(
    patientId ? `/chis/patients/${patientId}/history` : null
  );

  return {
    history: data?.data || [],
    isLoading,
    error,
    mutate,
  };
};

// Export CHIS reports
export const exportCHISReport = async (
  reportType: 'monthly' | 'codes' | 'claims',
  params?: Record<string, any>
) => {
  try {
    const response = await myApi.post(`/chis/reports/export`, {
      reportType,
      ...params,
    });

    // Handle file download
    const blob = new Blob([response.data], { type: 'application/pdf' });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `chis-${reportType}-report-${new Date().toISOString().split('T')[0]}.pdf`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);

    toast.success('Report exported successfully');
    return response.data;
  } catch (error) {
    console.error('Error exporting CHIS report:', error);
    toast.error('Failed to export report');
    throw error;
  }
};
