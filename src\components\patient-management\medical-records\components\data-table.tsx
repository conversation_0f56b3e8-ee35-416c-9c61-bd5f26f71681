'use client';

import React, { useState, useEffect } from 'react';
import Pagination from '@/components/common/pagination';
import dayjs from 'dayjs';
import { Loader2, Ellipsis, Search, ClipboardList } from 'lucide-react';
import { StatusBadge } from '@/components/common/status-badge';
import { EmptyState, LoadingState } from '@/components/common/dataState';
import { Input } from '@/components/ui/input';
import DateRangeFilter from '@/components/common/date-range-filter';
import { GetAllMedicalRecords } from '@/api/crm/data';
import { useDebounce } from '@/hooks/useDebounce';
import Details from './details';
import Create from './create';

interface MedicalRecordsTableProps {
  openCreate: boolean;
  setOpenCreate: React.Dispatch<React.SetStateAction<boolean>>;
}

export default function MedicalRecordsTable({
  openCreate,
  setOpenCreate,
}: MedicalRecordsTableProps) {
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(10);
  const [queryParam, setQueryParam] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [recordTypeFilter, setRecordTypeFilter] = useState('');
  const [startDate, setStartDate] = useState<Date | null>(null);
  const [endDate, setEndDate] = useState<Date | null>(null);
  const [openDetails, setOpenDetails] = useState(false);
  const [selectedRecord, setSelectedRecord] = useState<any | null>(null);
  const debouncedSearchTerm = useDebounce(searchTerm, 500);

  const handleEventFromModal = (record: any) => {
    setSelectedRecord(record);
    setOpenDetails(true);
  };

  useEffect(() => {
    let newQueryParam = '';

    if (debouncedSearchTerm) {
      newQueryParam = `search=${debouncedSearchTerm}`;
    }

    if (recordTypeFilter) {
      newQueryParam = newQueryParam
        ? `${newQueryParam}&recordType=${recordTypeFilter}`
        : `recordType=${recordTypeFilter}`;
    }

    if (startDate) {
      const formattedStartDate = dayjs(startDate).format('YYYY-MM-DD');
      newQueryParam = newQueryParam
        ? `${newQueryParam}&startDate=${formattedStartDate}`
        : `startDate=${formattedStartDate}`;
    }

    if (endDate) {
      const formattedEndDate = dayjs(endDate).format('YYYY-MM-DD');
      newQueryParam = newQueryParam
        ? `${newQueryParam}&endDate=${formattedEndDate}`
        : `endDate=${formattedEndDate}`;
    }

    setCurrentPage(1); // Reset to first page on new search
    setQueryParam(newQueryParam);
  }, [debouncedSearchTerm, recordTypeFilter, startDate, endDate]);

  const { medicalRecords, medicalRecordsLoading, mutate } =
    GetAllMedicalRecords(
      `?page=${currentPage}&limit=${pageSize}${queryParam ? `&${queryParam}` : ''}`
    );

  const medicalRecordData = medicalRecords?.data?.medicalRecords || [];
  const totalPages = medicalRecords?.data?.totalPages ?? 0;

  const handlePageChange = (pageNumber: number) => {
    setCurrentPage(pageNumber);
  };

  return (
    <div className="w-full">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4 gap-4">
        <div className="relative w-full sm:w-64">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search medical records..."
            className="pl-8"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <div className="flex flex-col sm:flex-row gap-2 w-full sm:w-auto">
          <div className="flex gap-2 flex-wrap">
            <button
              onClick={() => setRecordTypeFilter('')}
              className={`px-3 py-1 text-xs rounded-full ${
                recordTypeFilter === ''
                  ? 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-400'
                  : 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300'
              }`}
            >
              All Types
            </button>
            <button
              onClick={() => setRecordTypeFilter('consultation')}
              className={`px-3 py-1 text-xs rounded-full ${
                recordTypeFilter === 'consultation'
                  ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400'
                  : 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300'
              }`}
            >
              Consultation
            </button>
            <button
              onClick={() => setRecordTypeFilter('lab')}
              className={`px-3 py-1 text-xs rounded-full ${
                recordTypeFilter === 'lab'
                  ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'
                  : 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300'
              }`}
            >
              Lab Results
            </button>
            <button
              onClick={() => setRecordTypeFilter('procedure')}
              className={`px-3 py-1 text-xs rounded-full ${
                recordTypeFilter === 'procedure'
                  ? 'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-400'
                  : 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300'
              }`}
            >
              Procedure
            </button>
          </div>
          <DateRangeFilter
            startDate={startDate}
            endDate={endDate}
            onStartDateChange={setStartDate}
            onEndDateChange={setEndDate}
            onClear={() => {
              setStartDate(null);
              setEndDate(null);
            }}
          />
        </div>
      </div>

      {medicalRecordsLoading ? (
        <LoadingState />
      ) : medicalRecordData.length > 0 ? (
        <div className="overflow-x-auto">
          <table className="w-full border-collapse">
            <thead>
              <tr className="bg-gray-100 dark:bg-[#1F1F23] text-xs text-gray-500 dark:text-gray-400">
                <th className="table-style">#</th>
                <th className="table-style">Date</th>
                <th className="table-style">Patient</th>
                <th className="table-style">Record Type</th>
                <th className="table-style">Doctor</th>
                <th className="table-style">Diagnosis</th>
                <th className="table-style">Actions</th>
              </tr>
            </thead>
            <tbody className="relative overflow-x-auto whitespace-nowrap text-sm shadow-md">
              {medicalRecordData.map((record: any, index: number) => (
                <tr
                  className="text-xs text-[#062A55] dark:text-white"
                  key={record.id}
                >
                  <td className="table-style">
                    {currentPage === 1
                      ? index + 1
                      : (currentPage - 1) * pageSize + (index + 1)}
                  </td>
                  <td className="table-style">
                    {dayjs(record.recordDate).format('MMM D, YYYY')}
                  </td>
                  <td className="table-style">{record.patientName}</td>
                  <td className="table-style">
                    <span
                      className={`px-2 py-1 rounded-full text-xs ${
                        record.recordType === 'consultation'
                          ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400'
                          : record.recordType === 'lab'
                            ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'
                            : record.recordType === 'procedure'
                              ? 'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-400'
                              : 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300'
                      }`}
                    >
                      {record.recordType.charAt(0).toUpperCase() +
                        record.recordType.slice(1)}
                    </span>
                  </td>
                  <td className="table-style">{record.doctorName}</td>
                  <td className="table-style max-w-[200px] truncate">
                    {record.diagnosis}
                  </td>
                  <td className="table-style">
                    <Ellipsis
                      onClick={() => handleEventFromModal(record)}
                      className="w-4 h-4 cursor-pointer"
                    />
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
          <div className="mt-4">
            <Pagination
              currentPage={currentPage}
              totalPages={totalPages}
              onPageChange={handlePageChange}
            />
          </div>
        </div>
      ) : (
        <EmptyState message="No medical records found" />
      )}

      {selectedRecord && (
        <Details
          open={openDetails}
          setOpen={setOpenDetails}
          data={selectedRecord}
          mutate={mutate}
        />
      )}

      <Create open={openCreate} setOpen={setOpenCreate} mutate={mutate} />
    </div>
  );
}
