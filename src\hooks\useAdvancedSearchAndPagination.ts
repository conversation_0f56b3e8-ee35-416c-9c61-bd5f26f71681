import { useState, useEffect, useCallback } from 'react';

interface UseAdvancedSearchAndPaginationProps {
  initialPageSize?: number;
  debounceDelay?: number;
}

interface UseAdvancedSearchAndPaginationReturn {
  // Search state
  searchTerm: string;
  debouncedSearchTerm: string;
  handleSearchChange: (e: React.ChangeEvent<HTMLInputElement>) => void;

  // Pagination state
  currentPage: number;
  pageSize: number;
  handlePageChange: (pageNumber: number) => void;

  // Query building
  queryParam: string;
  buildQueryParam: (
    additionalParams: Record<string, string | undefined>
  ) => void;
}

export function useAdvancedSearchAndPagination({
  initialPageSize = 15,
  debounceDelay = 500,
}: UseAdvancedSearchAndPaginationProps = {}): UseAdvancedSearchAndPaginationReturn {
  const [pageSize] = useState(initialPageSize);
  const [currentPage, setCurrentPage] = useState(1);
  const [searchTerm, setSearchTerm] = useState('');
  const [queryParam, setQueryParam] = useState('');
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('');

  // Debounce search term
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
    }, debounceDelay);

    return () => clearTimeout(timer);
  }, [searchTerm, debounceDelay]);

  const buildQueryParam = useCallback(
    (additionalParams: Record<string, string | undefined> = {}) => {
      const params = [];

      // Add search parameter if exists
      if (debouncedSearchTerm) {
        params.push(`search=${debouncedSearchTerm}`);
      }

      // Add additional parameters
      Object.entries(additionalParams).forEach(([key, value]) => {
        if (value && value !== 'all') {
          params.push(`${key}=${value}`);
        }
      });

      setCurrentPage(1);
      setQueryParam(params.join('&'));
    },
    [debouncedSearchTerm]
  );

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  const handlePageChange = (pageNumber: number) => {
    setCurrentPage(pageNumber);
  };

  return {
    searchTerm,
    debouncedSearchTerm,
    handleSearchChange,
    currentPage,
    pageSize,
    handlePageChange,
    queryParam,
    buildQueryParam,
  };
}
