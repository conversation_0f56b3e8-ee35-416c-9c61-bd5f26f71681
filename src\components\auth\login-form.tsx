'use client';

import React, { useState, Suspense, useEffect } from 'react';
import Image from 'next/image';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { Button } from '@/components/ui/button';
import { InputField } from '../common/form';
import { PasswordField } from '../common/form';
import { Form } from '../ui/form';
import { formSchema, staffIdSchema } from '../validations/login';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { toast } from 'sonner';
import { myApi } from '@/api/fetcher';
import { useRouter, useSearchParams } from 'next/navigation';
import { Loader2 } from 'lucide-react';
import Logo from '@/assets/logo.png';
import storage from '@/lib/storage';
import { accessTokenStore, staffAccessToken } from '@/store/accessToken';
import { PWAInstallPrompt } from '@/components/auth/pwa-install-prompt';
import { WelcomeScreen } from '@/components/auth/welcome-screen';

function LoginForm() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const redirect = searchParams.get('redirect');
  const [isLoading, setIsLoading] = useState(false);
  const [checkingAuth, setCheckingAuth] = useState(true);
  const [forgotLoading, setForgotLoading] = useState(false);
  const [staffExists, setStaffExists] = useState(false);
  const [staffId, setStaffId] = useState('');
  const [response, setResponse] = useState('');
  const [showWelcome, setShowWelcome] = useState(false);
  const [showLogin, setShowLogin] = useState(false);

  // Check if user is already logged in and redirect to dashboard
  useEffect(() => {
    const token = accessTokenStore.accessToken;
    if (token) {
      router.push(redirect || '/dashboard');
    } else {
      setCheckingAuth(false);
      // Show welcome screen for non-logged-in users
      setShowWelcome(true);
    }
  }, [router, redirect]);

  const handleWelcomeComplete = () => {
    setShowWelcome(false);
    setShowLogin(true);
  };

  const staffIdForm = useForm({
    resolver: zodResolver(staffIdSchema),
    defaultValues: {
      code: '',
    },
  });

  const passwordForm = useForm({
    resolver: zodResolver(formSchema),
    defaultValues: {
      code: '',
      password: '',
    },
  });

  const checkStaffExists = async (data: { code: string }) => {
    try {
      setIsLoading(true);
      const res = await myApi.post('/staff/check', {
        code: data.code,
      });
      if (res.status === 200) {
        setStaffExists(true);
        setStaffId(data.code);
        passwordForm.setValue('code', data.code);
        toast.success(res.data.data.message);
      }
      if (res.data.data.statusCode === 201) {
        setResponse(res.data.data.message);
      }
    } catch (error) {
    } finally {
      setIsLoading(false);
    }
  };

  const forgotPassword = async (code: string) => {
    try {
      setForgotLoading(true);
      const res = await myApi.post('/staff/forgot-password', {
        code: code,
      });
      if (res.status === 200) {
        setResponse(res.data.data.message);
        toast.success(res.data.data.message);
      }
    } catch (error) {
    } finally {
      setForgotLoading(false);
    }
  };

  const onSubmit = async (data: { code: string; password: string }) => {
    try {
      setIsLoading(true);
      const res = await myApi.post('/staff/login', {
        code: data.code,
        password: data.password,
      });
      if (res.status === 200) {
        const token = res.data.data.token;
        accessTokenStore.accessToken = token;
        storage.set(staffAccessToken, token);
        toast.success(res.data.message);
        router.push(redirect || '/dashboard');
      }
    } catch (error) {
      setIsLoading(false);
    }
  };

  const resetFlow = () => {
    setStaffExists(false);
    setStaffId('');
    staffIdForm.reset();
  };

  if (checkingAuth) {
    return (
      <div className="flex flex-col items-center justify-center gap-3">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  // Show welcome screen first
  if (showWelcome) {
    return <WelcomeScreen onComplete={handleWelcomeComplete} />;
  }

  return (
    <>
      <div className="flex flex-col gap-3">
        <div className="flex justify-center gap-3">
          <Image
            src={Logo}
            alt="Cedarcrest"
            width={150}
            height={150}
            className="flex-shrink-0"
            unoptimized
          />
        </div>
        <Card>
          <CardHeader>
            <CardTitle className="text-2xl">Welcome!</CardTitle>
            <CardDescription>
              Sign in to your account to continue
            </CardDescription>
            <CardDescription>
              <p className="text-xs text-red-300">{response}</p>
            </CardDescription>
          </CardHeader>
          <CardContent>
            {!staffExists ? (
              <Form {...staffIdForm}>
                <form
                  onSubmit={staffIdForm.handleSubmit(checkStaffExists)}
                  className="space-y-8"
                >
                  <InputField
                    control={staffIdForm.control}
                    name="code"
                    label="Staff ID"
                    placeholder="Enter a valid staff ID"
                    type="text"
                  />
                  <Button
                    className="w-full cursor-pointer"
                    disabled={isLoading}
                    type="submit"
                  >
                    {isLoading ? (
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    ) : null}
                    {isLoading ? 'Please wait...' : 'Verify Staff ID'}
                  </Button>
                </form>
              </Form>
            ) : (
              <Form {...passwordForm}>
                <form
                  onSubmit={passwordForm.handleSubmit(onSubmit)}
                  className="space-y-8"
                >
                  <div className="flex items-center justify-between">
                    <p className="text-sm font-medium">Staff ID: {staffId}</p>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={resetFlow}
                      type="button"
                    >
                      Change
                    </Button>
                  </div>
                  <div>
                    <PasswordField
                      control={passwordForm.control}
                      name="password"
                      label="Password"
                      placeholder="********"
                    />
                    {forgotLoading ? (
                      <span className="flex justify-end text-xs mt-1 text-gray-400">
                        Please wait...
                      </span>
                    ) : (
                      <span
                        onClick={() => forgotPassword(staffId)}
                        className="flex justify-end text-xs mt-1 cursor-pointer text-red-500"
                      >
                        Forgot Password
                      </span>
                    )}
                  </div>
                  <Button
                    className="w-full cursor-pointer"
                    disabled={isLoading}
                    type="submit"
                  >
                    {isLoading ? (
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    ) : null}
                    {isLoading ? 'Please wait...' : 'Sign in'}
                  </Button>
                </form>
              </Form>
            )}
          </CardContent>
        </Card>
        <p className="text-xs text-gray-500 dark:text-gray-400 text-center">
          © {new Date().getFullYear()} Cedarcrest Hospitals Innovations
        </p>
      </div>
      <PWAInstallPrompt />
    </>
  );
}

export default function LoginPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <LoginForm />
    </Suspense>
  );
}
