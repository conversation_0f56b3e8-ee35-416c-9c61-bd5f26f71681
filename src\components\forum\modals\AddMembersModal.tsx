import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>nt,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Di<PERSON>Footer,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Search } from 'lucide-react';
import { addGroupMembers } from '@/api/forum/data';
import { toast } from 'sonner';
import { ForumMember } from '@/api/forum/data';

// This would typically come from an API call
const mockStaffMembers = [
  { id: '1', name: '<PERSON>', role: 'Doctor' },
  { id: '2', name: '<PERSON>', role: 'Nurse' },
  { id: '3', name: '<PERSON>', role: 'Admin' },
  { id: '4', name: '<PERSON>', role: 'Receptionist' },
  { id: '5', name: '<PERSON>', role: 'Doctor' },
  { id: '6', name: '<PERSON>', role: 'Nurse' },
];

interface AddMembersModalProps {
  isOpen: boolean;
  onClose: () => void;
  groupId: string;
  currentMembers: ForumMember[];
  onMembersAdded: () => void;
}

export const AddMembersModal: React.FC<AddMembersModalProps> = ({
  isOpen,
  onClose,
  groupId,
  currentMembers,
  onMembersAdded,
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedMembers, setSelectedMembers] = useState<string[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Filter out staff who are already members
  const currentMemberIds = currentMembers.map((member) => member.id);
  const availableStaff = mockStaffMembers.filter(
    (staff) => !currentMemberIds.includes(staff.id)
  );

  const filteredStaff = availableStaff.filter((staff) =>
    staff.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (selectedMembers.length === 0) {
      toast.error('Please select at least one member to add');
      return;
    }

    setIsSubmitting(true);

    try {
      await addGroupMembers(groupId, selectedMembers);
      onMembersAdded();
      onClose();
      setSelectedMembers([]);
    } catch (error) {
      console.error('Error adding members:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const toggleMember = (memberId: string) => {
    setSelectedMembers((prev) =>
      prev.includes(memberId)
        ? prev.filter((id) => id !== memberId)
        : [...prev, memberId]
    );
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Add Members</DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit}>
          <div className="py-4">
            <div className="relative mb-4">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search staff..."
                className="pl-8"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>

            <div className="max-h-[300px] overflow-y-auto border rounded-md">
              {filteredStaff.length > 0 ? (
                filteredStaff.map((staff) => (
                  <div
                    key={staff.id}
                    className="flex items-center space-x-2 p-3 hover:bg-muted/50"
                  >
                    <Checkbox
                      id={`staff-${staff.id}`}
                      checked={selectedMembers.includes(staff.id)}
                      onCheckedChange={() => toggleMember(staff.id)}
                    />
                    <Label
                      htmlFor={`staff-${staff.id}`}
                      className="flex-1 cursor-pointer"
                    >
                      <div>{staff.name}</div>
                      <div className="text-sm text-muted-foreground">
                        {staff.role}
                      </div>
                    </Label>
                  </div>
                ))
              ) : (
                <div className="p-4 text-center text-muted-foreground">
                  No staff members found
                </div>
              )}
            </div>
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting || selectedMembers.length === 0}
            >
              {isSubmitting ? 'Adding...' : 'Add Members'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};
