import React, { useState } from 'react';
import { Modal } from '@/components/common/modal';
import { ModalProps } from '@/components/crm/types';
import dayjs from 'dayjs';
import { myApi } from '@/api/fetcher';
import { toast } from 'sonner';
import { GetProfile } from '@/api/staff';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { UserCog, Calendar, ClipboardList, Star } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { hasPermission, PERMISSIONS } from '@/lib/types/permissions';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  doctorFormSchema,
  DoctorFormValues,
} from '@/components/validations/crm';
import { InputField } from '@/components/common/form';
import { Form } from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';

interface TabProps {
  label: string;
  icon: React.ReactNode;
  content: React.ReactNode;
}

const Details: React.FC<ModalProps> = ({ open, setOpen, mutate, data }) => {
  const [isLoading, setIsLoading] = useState(false);
  // Using activeTab state to track the current tab
  const [, setActiveTab] = useState(0);
  const [editMode, setEditMode] = useState(false);
  const { profile } = GetProfile();
  const permitUpdate = hasPermission(PERMISSIONS.REFERRAL_EDIT);

  const form = useForm<DoctorFormValues>({
    resolver: zodResolver(doctorFormSchema),
    defaultValues: {
      name: data?.name || '',
      email: data?.email || '',
      phoneNumber: data?.phoneNumber || '',
      department: data?.department || '',
      specialization: data?.specialization || '',
      qualifications: data?.qualifications || '',
      experience: data?.experience || '',
      bio: data?.bio || '',
      isAvailable: data?.isAvailable || false,
      consultationFee: data?.consultationFee?.toString() || '',
    },
  });

  const handleUpdateDoctor = async (formData: DoctorFormValues) => {
    try {
      setIsLoading(true);
      const res = await myApi.patch(`/crm/doctors/${data.id}`, {
        ...formData,
        updatedBy: profile.data.fullName,
      });
      setIsLoading(false);
      if (res.status === 200) {
        toast.success('Doctor updated successfully');
        setEditMode(false);
        if (mutate) {
          mutate();
        }
      }
    } catch (error: any) {
      setIsLoading(false);
      toast.error(error?.response?.data?.message || 'An error occurred');
    }
  };

  const toggleAvailability = async () => {
    try {
      setIsLoading(true);
      const res = await myApi.patch(`/crm/doctors/${data.id}/availability`, {
        isAvailable: !data.isAvailable,
        updatedBy: profile.data.fullName,
      });
      setIsLoading(false);
      if (res.status === 200) {
        toast.success(
          `Doctor marked as ${!data.isAvailable ? 'available' : 'unavailable'}`
        );
        if (mutate) {
          mutate();
        }
      }
    } catch (error: any) {
      setIsLoading(false);
      toast.error(error?.response?.data?.message || 'An error occurred');
    }
  };

  if (!data) return null;

  // Tab content components
  const DoctorInfo = () => (
    <div className="space-y-6">
      {editMode ? (
        <Form {...form}>
          <form
            className="space-y-4"
            onSubmit={form.handleSubmit(handleUpdateDoctor)}
          >
            <InputField
              control={form.control}
              name="name"
              label="Full Name"
              placeholder="Dr. John Doe"
            />

            <div className="grid grid-cols-2 gap-4">
              <InputField
                control={form.control}
                name="email"
                label="Email"
                placeholder="<EMAIL>"
                type="email"
              />

              <InputField
                control={form.control}
                name="phoneNumber"
                label="Phone Number"
                placeholder="+234 ************"
              />
            </div>

            <InputField
              control={form.control}
              name="department"
              label="Department"
              placeholder="Cardiology"
            />

            <InputField
              control={form.control}
              name="specialization"
              label="Specialization"
              placeholder="Cardiology, Pediatrics, etc."
            />

            <InputField
              control={form.control}
              name="qualifications"
              label="Qualifications"
              placeholder="MBBS, MD, MS, etc."
            />

            <InputField
              control={form.control}
              name="experience"
              label="Experience (years)"
              placeholder="10"
              type="number"
            />

            <InputField
              control={form.control}
              name="consultationFee"
              label="Consultation Fee"
              placeholder="5000"
              type="number"
            />

            <div>
              <label className="text-sm font-medium">Bio</label>
              <Textarea
                placeholder="Brief description about the doctor"
                className="resize-none"
                {...form.register('bio')}
              />
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="availability-edit"
                checked={form.getValues('isAvailable')}
                onCheckedChange={(checked) =>
                  form.setValue('isAvailable', checked)
                }
              />
              <label
                htmlFor="availability-edit"
                className="text-sm font-medium"
              >
                Available for Appointments
              </label>
            </div>

            <div className="flex justify-end gap-2">
              <Button
                type="button"
                variant="outline"
                onClick={() => setEditMode(false)}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isLoading}>
                Save Changes
              </Button>
            </div>
          </form>
        </Form>
      ) : (
        <>
          <div className="flex justify-between items-start">
            <div>
              <h3 className="text-lg font-semibold">Dr. {data.name}</h3>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {data.specialization} • {data.department}
              </p>
            </div>
            {permitUpdate && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => setEditMode(true)}
              >
                Edit
              </Button>
            )}
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <h4 className="text-sm font-medium">Email</h4>
              <p className="text-sm">{data.email}</p>
            </div>
            <div>
              <h4 className="text-sm font-medium">Phone</h4>
              <p className="text-sm">{data.phoneNumber}</p>
            </div>
            <div>
              <h4 className="text-sm font-medium">Qualifications</h4>
              <p className="text-sm">{data.qualifications}</p>
            </div>
            <div>
              <h4 className="text-sm font-medium">Experience</h4>
              <p className="text-sm">{data.experience} years</p>
            </div>
            <div>
              <h4 className="text-sm font-medium">Consultation Fee</h4>
              <p className="text-sm">₦{data.consultationFee}</p>
            </div>
            <div>
              <h4 className="text-sm font-medium">Availability</h4>
              <div className="flex items-center gap-2">
                <span
                  className={`px-2 py-1 text-xs rounded-full ${
                    data.isAvailable
                      ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'
                      : 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400'
                  }`}
                >
                  {data.isAvailable ? 'Available' : 'Unavailable'}
                </span>
                {permitUpdate && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={toggleAvailability}
                    disabled={isLoading}
                  >
                    Toggle
                  </Button>
                )}
              </div>
            </div>
          </div>

          <div>
            <h4 className="text-sm font-medium">Bio</h4>
            <p className="text-sm">{data.bio}</p>
          </div>
        </>
      )}
    </div>
  );

  const ScheduleInfo = () => (
    <div className="space-y-4">
      <h3 className="text-md font-semibold">Weekly Schedule</h3>
      <div className="border rounded-md p-4">
        <p className="text-sm text-gray-500 dark:text-gray-400">
          Schedule information will be displayed here.
        </p>
      </div>
    </div>
  );

  const tabs: TabProps[] = [
    {
      label: 'Profile',
      icon: <UserCog className="h-4 w-4" />,
      content: <DoctorInfo />,
    },
    {
      label: 'Schedule',
      icon: <Calendar className="h-4 w-4" />,
      content: <ScheduleInfo />,
    },
  ];

  return (
    <Modal
      open={open}
      setOpen={setOpen}
      title="Doctor Details"
      description={`Dr. ${data.name}`}
      size="lg"
    >
      <Tabs
        defaultValue="0"
        className="w-full"
        onValueChange={(value) => setActiveTab(parseInt(value))}
      >
        <TabsList className="grid grid-cols-2 mb-4">
          {tabs.map((tab, index) => (
            <TabsTrigger
              key={index}
              value={index.toString()}
              className="flex items-center gap-2"
            >
              {tab.icon}
              {tab.label}
            </TabsTrigger>
          ))}
        </TabsList>
        {tabs.map((tab, index) => (
          <TabsContent key={index} value={index.toString()}>
            {tab.content}
          </TabsContent>
        ))}
      </Tabs>
    </Modal>
  );
};

export default Details;
