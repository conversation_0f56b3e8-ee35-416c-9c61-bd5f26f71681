'use client';

import { useState } from 'react';
import { Badge } from '@/components/ui/badge';
import RoleList from './role-list';
import CreateRole from './create-role';
import RoleDetails from './role-details';

export default function ManageRolePage() {
  const [openCreate, setOpenCreate] = useState(false);
  const [selectedRole, setSelectedRole] = useState<any>(null);
  const [openDetails, setOpenDetails] = useState(false);
  const [mutateRoles, setMutateRoles] = useState<(() => void) | undefined>(
    undefined
  );

  const handleRoleSelect = (role: any) => {
    setSelectedRole(role);
    setOpenDetails(true);
  };

  const handleMutateReady = (mutate: () => void) => {
    setMutateRoles(() => mutate);
  };

  return (
    <>
      <div className="mb-4">
        <Badge
          className="h-7 cursor-pointer"
          onClick={() => setOpenCreate(true)}
        >
          Create New Role
        </Badge>
      </div>
      <RoleList
        onRoleSelect={handleRoleSelect}
        onMutateReady={handleMutateReady}
      />
      <CreateRole
        open={openCreate}
        setOpen={setOpenCreate}
        mutate={mutateRoles}
      />
      <RoleDetails
        open={openDetails}
        setOpen={setOpenDetails}
        role={selectedRole}
      />
    </>
  );
}
