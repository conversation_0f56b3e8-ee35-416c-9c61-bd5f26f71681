'use client';

import { useState } from 'react';
import { Settings, Mail } from 'lucide-react';
import { hasPermission, PERMISSIONS } from '@/lib/types/permissions';
import { Tabs, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs';
import NotificationEmailsSettings from './notification-emails';

export default function SystemSettingsContent() {
  const [activeTab, setActiveTab] = useState('notification-emails');
  const permitRead = hasPermission(PERMISSIONS.SETTINGS_VIEW);
  const permitUpdate = hasPermission(PERMISSIONS.SETTINGS_EDIT);

  if (!permitRead) {
    return (
      <div className="flex items-center justify-center h-64">
        <p className="text-gray-500 dark:text-gray-400">
          You do not have permission to view system settings.
        </p>
      </div>
    );
  }

  return (
    <>
      <h2 className="text-lg font-bold text-gray-900 dark:text-white mb-4 text-left flex items-center gap-2">
        <Settings className="w-5 h-5 text-zinc-900 dark:text-zinc-50" />
        System Settings
      </h2>

      <div className="mt-4 bg-white dark:bg-[#0F0F12] rounded-xl p-4 flex flex-col items-start justify-start border border-gray-200 dark:border-[#1F1F23]">
        <Tabs
          value={activeTab}
          onValueChange={setActiveTab}
          className="w-full animate-in fade-in-50"
        >
          <TabsList className="mb-6">
            <TabsTrigger
              value="notification-emails"
              className="flex items-center gap-2 data-[state=active]:bg-background data-[state=active]:text-primary"
            >
              <Mail
                className="w-4 h-4"
                style={{
                  color:
                    activeTab === 'notification-emails' ? '#BD9A3D' : 'inherit',
                }}
              />
              Notification Emails
            </TabsTrigger>
          </TabsList>

          <TabsContent
            value="notification-emails"
            className="mt-0 animate-in fade-in-50 duration-300"
          >
            <div className="space-y-4">
              <h3 className="text-md font-semibold text-gray-800 dark:text-gray-200 flex items-center gap-2">
                <Mail className="w-4 h-4" style={{ color: '#BD9A3D' }} />
                Notification Email Settings
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Configure email addresses that will receive notifications when a
                package booking is completed.
              </p>
              <div>
                <NotificationEmailsSettings canEdit={permitUpdate} />
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </>
  );
}
