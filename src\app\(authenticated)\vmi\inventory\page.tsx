'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { vmiApi } from '@/api/vmi';
import { InventoryItem } from '@/lib/types/vmi';
import { formatCurrency, getStockStatus } from '@/lib/vmi-utils';
import {
  AlertTriangle,
  CheckCircle,
  Clock,
  Building,
  Warehouse,
} from 'lucide-react';

type StoreView = 'combined' | 'hospital' | 'vmi';

export default function VMIInventory() {
  const [inventory, setInventory] = useState<InventoryItem[]>([]);
  const [storeView, setStoreView] = useState<StoreView>('combined');

  useEffect(() => {
    vmiApi.getInventory().then(setInventory);
  }, []);

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'low':
        return (
          <Badge variant="destructive" className="flex items-center gap-1">
            <AlertTriangle className="h-3 w-3" />
            Low Stock
          </Badge>
        );
      case 'high':
        return (
          <Badge variant="default" className="flex items-center gap-1">
            <CheckCircle className="h-3 w-3" />
            Well Stocked
          </Badge>
        );
      default:
        return (
          <Badge variant="secondary" className="flex items-center gap-1">
            <Clock className="h-3 w-3" />
            Normal
          </Badge>
        );
    }
  };

  const getStoreStock = (item: InventoryItem) => {
    switch (storeView) {
      case 'hospital':
        return item.hospitalStore;
      case 'vmi':
        return item.vmiStore;
      default:
        return item.hospitalStore + item.vmiStore;
    }
  };

  const getTotalValue = () => {
    return inventory.reduce((sum, item) => {
      const stock = getStoreStock(item);
      return sum + stock * item.product.unitPrice;
    }, 0);
  };

  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Inventory Tracking</h1>
        <div className="flex gap-2">
          <Button
            variant={storeView === 'combined' ? 'default' : 'outline'}
            onClick={() => setStoreView('combined')}
            size="sm"
          >
            Combined View
          </Button>
          <Button
            variant={storeView === 'hospital' ? 'default' : 'outline'}
            onClick={() => setStoreView('hospital')}
            size="sm"
          >
            <Building className="h-4 w-4 mr-1" />
            Hospital Store
          </Button>
          <Button
            variant={storeView === 'vmi' ? 'default' : 'outline'}
            onClick={() => setStoreView('vmi')}
            size="sm"
          >
            <Warehouse className="h-4 w-4 mr-1" />
            VMI Store
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader>
            <CardTitle className="text-sm font-medium">Total Items</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{inventory.length}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-sm font-medium">
              Low Stock Items
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-500">
              {
                inventory.filter((item) => getStockStatus(item) === 'low')
                  .length
              }
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-sm font-medium">
              {storeView === 'hospital'
                ? 'Hospital'
                : storeView === 'vmi'
                  ? 'VMI'
                  : 'Total'}{' '}
              Value
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatCurrency(getTotalValue())}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-sm font-medium">Current View</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-lg font-semibold capitalize">
              {storeView === 'combined' ? 'Both Stores' : `${storeView} Store`}
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>
            Inventory Status -{' '}
            {storeView === 'combined'
              ? 'Combined View'
              : `${storeView.charAt(0).toUpperCase() + storeView.slice(1)} Store`}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Product</TableHead>
                {storeView === 'combined' && (
                  <>
                    <TableHead>Hospital Store</TableHead>
                    <TableHead>VMI Store</TableHead>
                  </>
                )}
                <TableHead>
                  {storeView === 'combined' ? 'Total Stock' : 'Stock Level'}
                </TableHead>
                <TableHead>Unit Price</TableHead>
                <TableHead>Value</TableHead>
                <TableHead>Reorder Level</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Last Updated</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {inventory.map((item) => {
                const stock = getStoreStock(item);
                return (
                  <TableRow key={item.id}>
                    <TableCell>
                      <div>
                        <div className="font-medium">{item.product.name}</div>
                        <div className="text-sm text-muted-foreground">
                          {item.product.category}
                        </div>
                      </div>
                    </TableCell>
                    {storeView === 'combined' && (
                      <>
                        <TableCell>{item.hospitalStore}</TableCell>
                        <TableCell>{item.vmiStore}</TableCell>
                      </>
                    )}
                    <TableCell className="font-medium">{stock}</TableCell>
                    <TableCell>
                      {formatCurrency(item.product.unitPrice)}
                    </TableCell>
                    <TableCell className="font-medium">
                      {formatCurrency(stock * item.product.unitPrice)}
                    </TableCell>
                    <TableCell>{item.reorderLevel}</TableCell>
                    <TableCell>
                      {getStatusBadge(getStockStatus(item))}
                    </TableCell>
                    <TableCell>
                      {new Date(item.lastUpdated).toLocaleDateString()}
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}
