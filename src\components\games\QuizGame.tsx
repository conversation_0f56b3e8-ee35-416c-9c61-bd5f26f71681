import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  Clock,
  Trophy,
  Zap,
  HelpCircle,
  CheckCircle,
  XCircle,
  SkipForward,
  Users,
  Target,
  Award,
} from 'lucide-react';
import {
  Game,
  GameQuestion,
  QuizSession,
  QuizTimer,
  startQuizSession,
  submitQuizAnswer,
  getQuestionHint,
  skipQuestion,
  completeQuizSession,
  GetQuizSession,
  GetLiveLeaderboard,
} from '@/api/games/data';
import { useWebSocket } from '@/hooks/useWebSocket';
import websocketService from '@/services/websocket';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';

interface QuizGameProps {
  game: any;
  onGameComplete?: (session: QuizSession) => void;
  onExit?: () => void;
}

export const QuizGame: React.FC<QuizGameProps> = ({
  game,
  onGameComplete,
  onExit,
}) => {
  const [session, setSession] = useState<QuizSession | null>(null);
  const [currentQuestion, setCurrentQuestion] = useState<GameQuestion | null>(
    null
  );
  const [selectedAnswer, setSelectedAnswer] = useState<string | string[]>('');
  const [timeRemaining, setTimeRemaining] = useState(0);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showHint, setShowHint] = useState(false);
  const [hint, setHint] = useState<string>('');
  const [gameStatus, setGameStatus] = useState<
    'loading' | 'playing' | 'completed' | 'error'
  >('loading');
  const [questionStartTime, setQuestionStartTime] = useState<number>(0);

  const { session: sessionData, mutate: mutateSession } = GetQuizSession(
    game.id
  );
  const { leaderboard, mutate: mutateLeaderboard } = GetLiveLeaderboard(
    game.id
  );
  const { isConnected, on, off } = useWebSocket();

  // Initialize quiz session
  useEffect(() => {
    const initializeSession = async () => {
      try {
        if (!sessionData) {
          const newSession = await startQuizSession(game.id);
          setSession(newSession);
        } else {
          setSession(sessionData);
        }
        setGameStatus('playing');
      } catch (error) {
        console.error('Error initializing quiz session:', error);
        setGameStatus('error');
        toast.error('Failed to start quiz session');
      }
    };

    initializeSession();
  }, [game.id, sessionData]);

  // Set current question based on session
  useEffect(() => {
    if (session && game.questions) {
      const question = game.questions[session.currentQuestionIndex];
      if (question) {
        setCurrentQuestion(question);
        setTimeRemaining(question.timeLimit);
        setQuestionStartTime(Date.now());
        setSelectedAnswer('');
        setShowHint(false);
        setHint('');
      } else {
        // Quiz completed
        handleQuizComplete();
      }
    }
  }, [session, game.questions]);

  // Timer countdown
  useEffect(() => {
    if (timeRemaining > 0 && gameStatus === 'playing') {
      const timer = setTimeout(() => {
        setTimeRemaining((prev) => prev - 1);
      }, 1000);

      return () => clearTimeout(timer);
    } else if (timeRemaining === 0 && currentQuestion) {
      // Time's up - auto submit or skip
      handleTimeUp();
    }
  }, [timeRemaining, gameStatus, currentQuestion]);

  // WebSocket listeners for real-time updates
  useEffect(() => {
    if (isConnected) {
      const handleQuizUpdate = (data: any) => {
        if (data.gameId === game.id) {
          switch (data.type) {
            case 'time_update':
              setTimeRemaining(data.timeRemaining);
              break;
            case 'leaderboard_update':
              mutateLeaderboard();
              break;
            case 'question_end':
              // Move to next question or complete quiz
              mutateSession();
              break;
          }
        }
      };

      on('quiz_update', handleQuizUpdate);
      return () => off('quiz_update', handleQuizUpdate);
    }
  }, [isConnected, game.id, mutateLeaderboard, mutateSession, on, off]);

  const handleAnswerSelect = (answer: string) => {
    if (currentQuestion?.type === 'multiple_choice') {
      const currentAnswers = Array.isArray(selectedAnswer)
        ? selectedAnswer
        : [];
      if (currentAnswers.includes(answer)) {
        setSelectedAnswer(currentAnswers.filter((a) => a !== answer));
      } else {
        setSelectedAnswer([...currentAnswers, answer]);
      }
    } else {
      setSelectedAnswer(answer);
    }
  };

  const handleSubmitAnswer = async () => {
    if (!currentQuestion || !session) return;

    setIsSubmitting(true);
    const timeSpent = Math.floor((Date.now() - questionStartTime) / 1000);

    try {
      await submitQuizAnswer(
        game.id,
        currentQuestion.id,
        selectedAnswer,
        timeSpent,
        showHint
      );

      // Send real-time update
      websocketService.send({
        type: 'quiz_answer_submitted',
        data: {
          gameId: game.id,
          questionId: currentQuestion.id,
          userId: session.participantId,
          timeSpent,
        },
      });

      // Refresh session to get next question
      mutateSession();
      toast.success('Answer submitted!');
    } catch (error) {
      console.error('Error submitting answer:', error);
      toast.error('Failed to submit answer');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleSkipQuestion = async () => {
    if (!currentQuestion || !session) return;

    try {
      await skipQuestion(game.id, currentQuestion.id);
      mutateSession();
      toast.info('Question skipped');
    } catch (error) {
      console.error('Error skipping question:', error);
      toast.error('Failed to skip question');
    }
  };

  const handleGetHint = async () => {
    if (!currentQuestion || showHint) return;

    try {
      const hintData = await getQuestionHint(game.id, currentQuestion.id);
      setHint(hintData.hint);
      setShowHint(true);
    } catch (error) {
      console.error('Error getting hint:', error);
      toast.error('Failed to get hint');
    }
  };

  const handleTimeUp = async () => {
    if (!currentQuestion || !session) return;

    const timeSpent = currentQuestion.timeLimit;

    try {
      // Auto-submit current answer or skip if no answer
      if (selectedAnswer) {
        await submitQuizAnswer(
          game.id,
          currentQuestion.id,
          selectedAnswer,
          timeSpent,
          showHint
        );
      } else {
        await skipQuestion(game.id, currentQuestion.id);
      }

      mutateSession();
      toast.warning("Time's up!");
    } catch (error) {
      console.error('Error handling time up:', error);
    }
  };

  const handleQuizComplete = async () => {
    if (!session) return;

    try {
      const completedSession = await completeQuizSession(game.id);
      setGameStatus('completed');

      if (onGameComplete) {
        onGameComplete(completedSession);
      }
    } catch (error) {
      console.error('Error completing quiz:', error);
      toast.error('Failed to complete quiz');
    }
  };

  const getTimeColor = () => {
    if (!currentQuestion) return 'text-muted-foreground';
    const percentage = (timeRemaining / currentQuestion.timeLimit) * 100;
    if (percentage > 50) return 'text-green-600';
    if (percentage > 20) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getProgressPercentage = () => {
    if (!session || !game.totalQuestions) return 0;
    return (session.currentQuestionIndex / game.totalQuestions) * 100;
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  if (gameStatus === 'loading') {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p>Loading quiz...</p>
        </div>
      </div>
    );
  }

  if (gameStatus === 'error') {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <XCircle className="h-12 w-12 text-destructive mx-auto mb-4" />
          <p className="text-destructive">Failed to load quiz</p>
          <Button onClick={onExit} className="mt-4">
            Exit Quiz
          </Button>
        </div>
      </div>
    );
  }

  if (gameStatus === 'completed') {
    return (
      <div className="text-center space-y-6">
        <div className="flex items-center justify-center">
          <Trophy className="h-16 w-16 text-yellow-500" />
        </div>
        <div>
          <h2 className="text-2xl font-bold mb-2">Quiz Completed!</h2>
          <p className="text-muted-foreground">
            Great job! You've completed the quiz.
          </p>
        </div>

        {session && (
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 max-w-md mx-auto">
            <div className="text-center">
              <div className="text-2xl font-bold text-primary">
                {session.score}
              </div>
              <div className="text-sm text-muted-foreground">Score</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {session.answers.filter((a) => a.isCorrect).length}
              </div>
              <div className="text-sm text-muted-foreground">Correct</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {Math.floor(session.totalTimeSpent / 60)}m{' '}
                {session.totalTimeSpent % 60}s
              </div>
              <div className="text-sm text-muted-foreground">Time</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">
                {Math.round(
                  (session.answers.filter((a) => a.isCorrect).length /
                    session.answers.length) *
                    100
                )}
                %
              </div>
              <div className="text-sm text-muted-foreground">Accuracy</div>
            </div>
          </div>
        )}

        <div className="flex gap-4 justify-center">
          <Button onClick={onExit}>Exit Quiz</Button>
          <Button variant="outline" onClick={() => window.location.reload()}>
            Play Again
          </Button>
        </div>
      </div>
    );
  }

  if (!currentQuestion || !session) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <p>No questions available</p>
          <Button onClick={onExit} className="mt-4">
            Exit Quiz
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Quiz Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">{game.name}</h1>
          <p className="text-muted-foreground">
            Question {session.currentQuestionIndex + 1} of {game.totalQuestions}
          </p>
        </div>

        <div className="flex items-center gap-4">
          <div className="text-center">
            <div className="text-lg font-bold">{session.score}</div>
            <div className="text-xs text-muted-foreground">Score</div>
          </div>

          <div className={cn('text-center', getTimeColor())}>
            <div className="text-lg font-bold flex items-center gap-1">
              <Clock className="h-4 w-4" />
              {formatTime(timeRemaining)}
            </div>
            <div className="text-xs text-muted-foreground">Remaining</div>
          </div>
        </div>
      </div>

      {/* Progress Bar */}
      <div className="space-y-2">
        <div className="flex justify-between text-sm">
          <span>Progress</span>
          <span>{Math.round(getProgressPercentage())}%</span>
        </div>
        <Progress value={getProgressPercentage()} className="h-2" />
      </div>

      {/* Question Card */}
      <Card>
        <CardHeader>
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <CardTitle className="text-lg leading-relaxed">
                {currentQuestion.question}
              </CardTitle>
              {currentQuestion.media && (
                <div className="mt-4">
                  {currentQuestion.media.type === 'image' && (
                    <img
                      src={currentQuestion.media.url}
                      alt={currentQuestion.media.caption || 'Question image'}
                      className="max-w-full h-auto rounded-lg"
                    />
                  )}
                </div>
              )}
            </div>

            <div className="flex items-center gap-2 ml-4">
              <Badge variant="secondary">{currentQuestion.points} pts</Badge>
              <Badge variant="outline">{currentQuestion.difficulty}</Badge>
            </div>
          </div>
        </CardHeader>

        <CardContent className="space-y-4">
          {/* Answer Options */}
          <div className="space-y-3">
            {currentQuestion.options?.map((option) => (
              <Button
                key={option.id}
                variant={
                  Array.isArray(selectedAnswer)
                    ? selectedAnswer.includes(option.value)
                      ? 'default'
                      : 'outline'
                    : selectedAnswer === option.value
                      ? 'default'
                      : 'outline'
                }
                className="w-full justify-start text-left h-auto p-4"
                onClick={() => handleAnswerSelect(option.value)}
                disabled={isSubmitting}
              >
                <div className="flex items-center gap-3">
                  <div
                    className={cn(
                      'w-6 h-6 rounded-full border-2 flex items-center justify-center',
                      (Array.isArray(selectedAnswer)
                        ? selectedAnswer.includes(option.value)
                        : selectedAnswer === option.value) &&
                        'bg-primary border-primary'
                    )}
                  >
                    {(Array.isArray(selectedAnswer)
                      ? selectedAnswer.includes(option.value)
                      : selectedAnswer === option.value) && (
                      <CheckCircle className="h-4 w-4 text-primary-foreground" />
                    )}
                  </div>
                  <span className="flex-1">{option.text}</span>
                </div>
              </Button>
            ))}
          </div>

          {/* Hint */}
          {showHint && hint && (
            <div className="bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
              <div className="flex items-start gap-2">
                <HelpCircle className="h-5 w-5 text-blue-600 mt-0.5" />
                <div>
                  <div className="font-medium text-blue-900 dark:text-blue-100">
                    Hint
                  </div>
                  <div className="text-blue-700 dark:text-blue-300">{hint}</div>
                </div>
              </div>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex items-center justify-between pt-4">
            <div className="flex gap-2">
              {currentQuestion.hint && !showHint && session.canUseHint && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleGetHint}
                  disabled={isSubmitting}
                >
                  <HelpCircle className="h-4 w-4 mr-2" />
                  Get Hint
                </Button>
              )}

              <Button
                variant="ghost"
                size="sm"
                onClick={handleSkipQuestion}
                disabled={isSubmitting}
              >
                <SkipForward className="h-4 w-4 mr-2" />
                Skip
              </Button>
            </div>

            <Button
              onClick={handleSubmitAnswer}
              disabled={!selectedAnswer || isSubmitting}
              className="min-w-[120px]"
            >
              {isSubmitting ? 'Submitting...' : 'Submit Answer'}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Live Leaderboard */}
      {leaderboard && leaderboard.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Trophy className="h-5 w-5" />
              Live Leaderboard
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {leaderboard.slice(0, 5).map((entry: any, index: number) => (
                <div
                  key={entry.userId}
                  className={cn(
                    'flex items-center justify-between p-2 rounded-lg',
                    entry.isCurrentUser &&
                      'bg-primary/10 border border-primary/20'
                  )}
                >
                  <div className="flex items-center gap-3">
                    <div
                      className={cn(
                        'w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold',
                        index === 0 && 'bg-yellow-500 text-white',
                        index === 1 && 'bg-gray-400 text-white',
                        index === 2 && 'bg-amber-600 text-white',
                        index > 2 && 'bg-muted text-muted-foreground'
                      )}
                    >
                      {entry.rank}
                    </div>
                    <Avatar className="h-6 w-6">
                      <AvatarImage src={entry.userAvatar} />
                      <AvatarFallback className="text-xs">
                        {entry.userName.charAt(0)}
                      </AvatarFallback>
                    </Avatar>
                    <span className="font-medium">{entry.userName}</span>
                    {entry.isCurrentUser && (
                      <Badge variant="secondary" className="text-xs">
                        You
                      </Badge>
                    )}
                  </div>
                  <div className="text-right">
                    <div className="font-bold">{entry.score}</div>
                    <div className="text-xs text-muted-foreground">
                      {entry.correctAnswers}/{entry.completedQuestions}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
