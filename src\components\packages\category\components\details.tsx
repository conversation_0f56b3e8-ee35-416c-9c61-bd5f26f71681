import { Modal } from '@/components/common/modal';
import React, { useState, useEffect } from 'react';
import { ModalProps } from '../../../types';
import { InputField, SwitchField } from '@/components/common/form';
import { Form } from '@/components/ui/form';
import { useForm } from 'react-hook-form';
import { myApi } from '@/api/fetcher';
import { toast } from 'sonner';
import { GetProfile } from '@/api/staff';
import dayjs from 'dayjs';

interface FormData {
  id: number;
  name: string;
  isActive: boolean;
}

const Details: React.FC<ModalProps> = ({ setOpen, open, mutate, data }) => {
  const [isLoading, setIsLoading] = useState(false);

  const { profile } = GetProfile();

  const form = useForm<FormData>({
    defaultValues: {
      name: data?.name,
      isActive: data?.isActive,
    },
  });

  useEffect(() => {
    if (data) {
      form.reset(data);
    }
  }, [data, form]);

  const onSubmit = async (data: FormData) => {
    try {
      setIsLoading(true);
      const res = await myApi.patch('/package/update-category', {
        id: data.id,
        name: data.name,
        updatedBy: profile.data.fullName,
        isActive: data.isActive,
      });
      setIsLoading(false);
      if (res.status === 200) {
        toast.success(res.data.message);
        setOpen(false);
        if (mutate) {
          mutate();
        }
      }
    } catch (error) {
      setIsLoading(false);
    }
  };

  return (
    <Modal
      open={open}
      setOpen={setOpen}
      title="Edit Category"
      description="Edit category details"
      isLoading={isLoading}
      onSubmit={form.handleSubmit(onSubmit)}
    >
      <>
        <Form {...form}>
          <form className="space-y-8">
            <InputField
              control={form.control}
              name="name"
              label="Category Name"
              placeholder="Wellness Packages"
              type="text"
            />
            <SwitchField
              control={form.control}
              name="isActive"
              label="Change Status"
            />
          </form>
        </Form>
        {data && data.updatedBy && (
          <p className="text-gray-300 text-xs">
            Last updated by: {data.updatedBy} -{' '}
            {dayjs(data.updatedAt).format('MMMM D, YYYY h:mm A')}
          </p>
        )}
      </>
    </Modal>
  );
};

export default Details;
