'use client';

import { useState, useEffect } from 'react';
import { toast } from 'sonner';
import { X, PencilIcon } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { GetLocations } from '@/api/data';
import { myApi } from '@/api/fetcher';
import Price from '../../new-package/components/price';
import { currencyFormat } from '@/lib/utils';
import type { LocationPriceForm } from '@/lib/types/types';

interface PackagePriceEditorProps {
  packageId: string;
  packageName: string;
  initialPrices: LocationPriceForm[];
  onUpdateSuccess?: () => void;
}

const PackagePriceEditor = ({
  packageId,
  packageName,
  initialPrices,
  onUpdateSuccess,
}: PackagePriceEditorProps) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isFieldsEditable, setIsFieldsEditable] = useState(false);
  const [locationPrices, setLocationPrices] = useState<LocationPriceForm[]>([]);
  const [newLocation, setNewLocation] = useState<LocationPriceForm>({
    location: { id: '', name: '' },
    amount: '',
    currency: 'NGN',
    endDate: new Date(new Date().getTime() + 86400000), // Set to tomorrow by default
  });
  const [editingLocationIndex, setEditingLocationIndex] = useState<
    number | null
  >(null);

  // Get locations data
  const { locations } = GetLocations();
  const locationData = locations?.data || [];

  // Initialize location prices from props, ensuring endDate is a Date object
  // and 'id' from initialPrices is preserved.
  useEffect(() => {
    if (initialPrices && Array.isArray(initialPrices)) {
      setLocationPrices(
        initialPrices.map((price) => ({
          ...price, // This carries over all properties, including 'id'
          endDate: new Date(price.endDate), // Ensure endDate is a Date object
        }))
      );
    }
  }, [initialPrices]);

  const handleAddLocationPrice = () => {
    try {
      // Validate inputs
      if (!newLocation || !newLocation.location) {
        toast.error('Invalid location data');
        return;
      }

      const trimmedPrice = (newLocation.amount || '').trim();
      const isValidNumber = /^\d*\.?\d+$/.test(trimmedPrice);
      const isLocationValid =
        newLocation.location &&
        newLocation.location.name &&
        newLocation.location.name.trim() !== '';

      if (!isLocationValid) {
        toast.error('Please select a location');
        return;
      }

      if (!isValidNumber) {
        toast.error('Please enter a valid price');
        return;
      }

      const today = new Date();
      const endDate = new Date(newLocation.endDate);
      if (
        endDate <= today ||
        (endDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24) < 2
      ) {
        toast.error(
          'End Date must be in the future and at least 2 days from now.'
        );
        return;
      }
      // Create a clean location price object
      const cleanLocationPrice: LocationPriceForm = {
        // id will be undefined for new items, or preserved if editing (see below)
        id:
          editingLocationIndex !== null
            ? locationPrices[editingLocationIndex].id
            : undefined,
        location: {
          id: newLocation.location.id, // Assuming newLocation.location.id is always set
          name: newLocation.location.name || '',
        },
        amount: trimmedPrice,
        currency: newLocation.currency || 'NGN',
        endDate: new Date(newLocation.endDate),
      };

      if (
        editingLocationIndex !== null &&
        editingLocationIndex >= 0 &&
        editingLocationIndex < locationPrices.length
      ) {
        // Update existing location price
        const updatedPrices = [...locationPrices];
        updatedPrices[editingLocationIndex] = {
          ...cleanLocationPrice,
          id: locationPrices[editingLocationIndex].id, // Ensure ID is preserved
        };

        setLocationPrices(updatedPrices);
        setNewLocation({
          location: { id: '', name: '' },
          amount: '',
          currency: 'NGN',
          endDate: new Date(new Date().getTime() + 86400000),
        });
        setEditingLocationIndex(null);

        toast.success(
          `Price for ${cleanLocationPrice.location.name} updated successfully`
        );
      } else {
        // Add new location price
        // New items won't have a backend ID yet.
        const newPriceEntry: LocationPriceForm = {
          ...cleanLocationPrice,
          id: undefined,
        };
        // If your backend assigns IDs, you'll get it back after saving.
        // If you need a temporary client-side ID, you could generate one here (e.g., using a UUID library).
        setLocationPrices([...locationPrices, newPriceEntry]);
        setNewLocation({
          location: { id: '', name: '' },
          amount: '',
          currency: 'NGN',
          endDate: new Date(new Date().getTime() + 86400000),
        });

        toast.success(
          `Price for ${cleanLocationPrice.location.name} added successfully`
        );
      }
    } catch (error) {
      console.error('Error in handleAddLocationPrice:', error);
      toast.error('An error occurred while adding/updating the location price');
    }
  };

  const handleRemoveLocationPrice = (index: number) => {
    // If removing the location that's currently being edited, reset editing state
    if (editingLocationIndex === index) {
      setEditingLocationIndex(null);
    } else if (editingLocationIndex !== null && editingLocationIndex > index) {
      // If removing a location before the one being edited, adjust the index
      setEditingLocationIndex(editingLocationIndex - 1);
    }

    const updatedPrices = [...locationPrices];
    updatedPrices.splice(index, 1);
    setLocationPrices(updatedPrices);
  };

  const handleEditLocationPrice = (index: number) => {
    try {
      if (index < 0 || index >= locationPrices.length) {
        toast.error('Error editing location price');
        return;
      }

      const locationToEdit = locationPrices[index];

      // Ensure the location has all required properties
      if (!locationToEdit || !locationToEdit.location) {
        toast.error('Error editing location price');
        return;
      }

      // Make sure endDate is a valid Date object
      let endDate = locationToEdit.endDate;
      if (!(endDate instanceof Date) && typeof endDate !== 'string') {
        const tomorrow = new Date();
        tomorrow.setDate(tomorrow.getDate() + 1);
        endDate = tomorrow;
      }

      // Create a clean copy of the location price
      const cleanLocationPrice: LocationPriceForm = {
        id: locationToEdit.id, // Preserve the ID
        location: {
          // Ensure location and its id/name are valid
          id: locationToEdit.location.id || '',
          name: locationToEdit.location.name || '',
        },
        amount: locationToEdit.amount || '',
        currency: locationToEdit.currency || 'NGN',
        endDate: endDate instanceof Date ? endDate : new Date(endDate),
      };

      // Set the location to edit
      setNewLocation(cleanLocationPrice);
      setEditingLocationIndex(index);

      toast.info(`Editing price for ${cleanLocationPrice.location.name}`);
    } catch (error) {
      console.error('Error in handleEditLocationPrice:', error);
      toast.error('An error occurred while editing the location price');
    }
  };

  const handleSavePrices = async () => {
    if (locationPrices.length === 0) {
      toast.error('At least one location price is required');
      return;
    }

    try {
      setIsSubmitting(true);
      const res = await myApi.patch('/package/update-package-prices', {
        packageId: packageId,
        prices: locationPrices,
      });

      setIsSubmitting(false);

      if (res.status === 200) {
        toast.success('Package prices updated successfully');

        // Reset to view mode after successful update
        setIsFieldsEditable(false);

        // Notify parent component if callback provided
        if (onUpdateSuccess) {
          onUpdateSuccess();
        }
      }
    } catch (error) {
      setIsSubmitting(false);
      toast.error('An error occurred while updating package prices');
      console.error(error);
    }
  };

  return (
    <div className="bg-white dark:bg-zinc-900/70 rounded-md border p-6 mt-6">
      {/* Edit button */}
      <div className="flex justify-between mb-4">
        <div>
          <h3 className="text-lg font-semibold">Edit Package Prices</h3>
          <p className="text-sm text-muted-foreground">
            Update pricing for package:{' '}
            <span className="font-medium">{packageName}</span>
          </p>
        </div>
        <Button
          variant="outline"
          type="button"
          onClick={() => setIsFieldsEditable(!isFieldsEditable)}
          className="flex items-center gap-2"
        >
          <PencilIcon className="w-4 h-4" />
          {isFieldsEditable ? 'Cancel Edit' : 'Edit Prices'}
        </Button>
      </div>

      {/* Display existing location prices */}
      {locationPrices.length > 0 && (
        <div className="mb-6">
          <h4 className="text-sm font-medium mb-2">Current Prices</h4>
          <div className="flex flex-wrap gap-2">
            {locationPrices.map((item, index) => (
              // Use item.id for the key if available and stable.
              // Fallback to index if item.id might be temporarily undefined (e.g., for new unsaved items).
              // For existing items from initialPrices, item.id should be present.
              <div
                key={item.id || `price-${index}`}
                className="flex items-center gap-1"
              >
                <div className="flex items-center gap-1">
                  <Badge
                    className={`h-8 ${new Date(item.endDate) < new Date() ? 'bg-red-100 text-red-600 dark:bg-red-900/30 dark:text-red-400' : ''}`}
                  >
                    {item.location.name} - {item.currency}{' '}
                    {currencyFormat(item.amount)}
                    {new Date(item.endDate) < new Date() && (
                      <span className="ml-1 text-xs">(Expired)</span>
                    )}
                  </Badge>

                  {isFieldsEditable && (
                    <div className="flex items-center gap-1">
                      <button
                        type="button"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleEditLocationPrice(index);
                        }}
                        className="p-1 hover:bg-gray-100 rounded-full"
                        title="Edit location price"
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="14"
                          height="14"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        >
                          <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                          <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
                        </svg>
                      </button>
                      <button
                        type="button"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleRemoveLocationPrice(index);
                        }}
                        className="p-1 hover:bg-gray-100 rounded-full"
                        title="Remove location price"
                      >
                        <X className="w-3.5 h-3.5" />
                      </button>
                    </div>
                  )}
                </div>

                {editingLocationIndex === index && (
                  <span className="text-xs text-primary font-medium">
                    Editing
                  </span>
                )}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Add/Edit location price form - only show when editing is enabled */}
      {isFieldsEditable && (
        <div className="mb-6">
          <h4 className="text-sm font-medium mb-2">
            {editingLocationIndex !== null ? 'Edit Price' : 'Add New Price'}
          </h4>
          <Price
            locations={locationData}
            newLocation={newLocation}
            setNewLocation={setNewLocation}
            onAddLocationPrice={handleAddLocationPrice}
            isEditing={editingLocationIndex !== null}
            onCancelEdit={() => {
              setEditingLocationIndex(null);
              setNewLocation({
                location: { id: '', name: '' },
                amount: '',
                currency: 'NGN',
                endDate: new Date(new Date().getTime() + 86400000),
              });
            }}
          />
        </div>
      )}

      {/* Save button - only show when editing is enabled */}
      {isFieldsEditable && (
        <div className="flex justify-end">
          <Button
            type="button"
            onClick={handleSavePrices}
            disabled={isSubmitting || locationPrices.length === 0}
          >
            {isSubmitting ? 'Saving...' : 'Save Prices'}
          </Button>
        </div>
      )}
    </div>
  );
};

export default PackagePriceEditor;
