import useSWR from 'swr';
import { myApi } from '../fetcher';
import { toast } from 'sonner';

// Types
export interface ForumGroup {
  id: string;
  name: string;
  description: string;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
  members: ForumMember[];
  type: 'public' | 'private' | 'channel';
  avatar?: string;
  isArchived: boolean;
  memberCount: number;
  lastActivity: string;
  settings: GroupSettings;
}

export interface GroupSettings {
  allowMemberInvites: boolean;
  allowFileSharing: boolean;
  allowReactions: boolean;
  allowThreads: boolean;
  moderationEnabled: boolean;
}

export interface ForumMember {
  id: string;
  name: string;
  avatar?: string;
  role: 'admin' | 'moderator' | 'member';
  joinedAt: string;
  lastSeen: string;
  isOnline: boolean;
  permissions: string[];
}

export interface ForumMessage {
  id: string;
  content: string;
  senderId: string;
  senderName: string;
  senderAvatar?: string;
  groupId?: string;
  recipientId?: string;
  createdAt: string;
  updatedAt: string;
  isPinned: boolean;
  isEdited: boolean;
  editedAt?: string;
  parentMessageId?: string; // For threading
  threadReplies?: number;
  deliveryStatus: 'sent' | 'delivered' | 'read';
  mentions: string[]; // User IDs mentioned in the message
  reactions: MessageReaction[];
  attachments: MessageAttachment[];
  messageType: 'text' | 'image' | 'file' | 'system' | 'game_result';
  metadata?: Record<string, any>; // For game results, system messages, etc.
}

export interface MessageReaction {
  id: string;
  messageId: string;
  userId: string;
  userName: string;
  emoji: string;
  createdAt: string;
}

export interface MessageAttachment {
  id: string;
  url: string;
  name: string;
  type: string;
  size: number;
  thumbnailUrl?: string;
  uploadedBy: string;
  uploadedAt: string;
}

export interface MessageThread {
  id: string;
  parentMessageId: string;
  messages: ForumMessage[];
  participantCount: number;
  lastReplyAt: string;
  isActive: boolean;
}

export interface UserPresence {
  userId: string;
  userName: string;
  status: 'online' | 'offline' | 'away' | 'busy';
  lastSeen: string;
  currentActivity?: string;
}

export interface DirectMessageConversation {
  id: string;
  participants: ForumMember[];
  lastMessage?: ForumMessage;
  unreadCount: number;
  isArchived: boolean;
  createdAt: string;
  updatedAt: string;
}

// Get all forum groups
export const GetForumGroups = () => {
  const { data, error, isLoading, mutate } = useSWR('/forum/groups');

  return {
    groups: data?.data || [],
    isLoading,
    error,
    mutate,
  };
};

// Get a specific forum group
export const GetForumGroup = (groupId: string) => {
  const { data, error, isLoading, mutate } = useSWR(
    groupId ? `/forum/groups/${groupId}` : null
  );

  return {
    group: data?.data,
    isLoading,
    error,
    mutate,
  };
};

// Get messages for a group
export const GetGroupMessages = (groupId: string) => {
  const { data, error, isLoading, mutate } = useSWR(
    groupId ? `/forum/groups/${groupId}/messages` : null
  );

  return {
    messages: data?.data || [],
    isLoading,
    error,
    mutate,
  };
};

// Get direct messages between staff members
export const GetDirectMessages = (staffId: string) => {
  const { data, error, isLoading, mutate } = useSWR(
    staffId ? `/forum/direct-messages/${staffId}` : null
  );

  return {
    messages: data?.data || [],
    isLoading,
    error,
    mutate,
  };
};

// Create a new forum group
export const createForumGroup = async (groupData: {
  name: string;
  description: string;
}) => {
  try {
    const response = await myApi.post('/forum/groups', groupData);
    toast.success('Group created successfully');
    return response.data;
  } catch (error) {
    console.error('Error creating forum group:', error);
    throw error;
  }
};

// Update a forum group
export const updateForumGroup = async (
  groupId: string,
  groupData: {
    name?: string;
    description?: string;
  }
) => {
  try {
    const response = await myApi.put(`/forum/groups/${groupId}`, groupData);
    toast.success('Group updated successfully');
    return response.data;
  } catch (error) {
    console.error('Error updating forum group:', error);
    throw error;
  }
};

// Delete a forum group
export const deleteForumGroup = async (groupId: string) => {
  try {
    const response = await myApi.delete(`/forum/groups/${groupId}`);
    toast.success('Group deleted successfully');
    return response.data;
  } catch (error) {
    console.error('Error deleting forum group:', error);
    throw error;
  }
};

// Add members to a group
export const addGroupMembers = async (groupId: string, memberIds: string[]) => {
  try {
    const response = await myApi.post(`/forum/groups/${groupId}/members`, {
      memberIds,
    });
    toast.success('Members added successfully');
    return response.data;
  } catch (error) {
    console.error('Error adding group members:', error);
    throw error;
  }
};

// Remove members from a group
export const removeGroupMembers = async (
  groupId: string,
  memberIds: string[]
) => {
  try {
    const response = await myApi.delete(`/forum/groups/${groupId}/members`, {
      data: { memberIds },
    });
    toast.success('Members removed successfully');
    return response.data;
  } catch (error) {
    console.error('Error removing group members:', error);
    throw error;
  }
};

// Join a group
export const joinGroup = async (groupId: string) => {
  try {
    const response = await myApi.post(`/forum/groups/${groupId}/join`);
    toast.success('Joined group successfully');
    return response.data;
  } catch (error) {
    console.error('Error joining group:', error);
    throw error;
  }
};

// Send a message to a group
export const sendGroupMessage = async (
  groupId: string,
  content: string,
  attachments?: File[]
) => {
  try {
    const formData = new FormData();
    formData.append('content', content);

    if (attachments && attachments.length > 0) {
      attachments.forEach((file) => {
        formData.append('attachments', file);
      });
    }

    const response = await myApi.post(
      `/forum/groups/${groupId}/messages`,
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      }
    );
    return response.data;
  } catch (error) {
    console.error('Error sending group message:', error);
    throw error;
  }
};

// Send a direct message to another staff member
export const sendDirectMessage = async (
  recipientId: string,
  content: string,
  attachments?: File[],
  parentMessageId?: string,
  mentions: string[] = []
) => {
  try {
    const formData = new FormData();
    formData.append('content', content);
    formData.append('recipientId', recipientId);

    if (parentMessageId) {
      formData.append('parentMessageId', parentMessageId);
    }

    if (mentions.length > 0) {
      formData.append('mentions', JSON.stringify(mentions));
    }

    if (attachments && attachments.length > 0) {
      attachments.forEach((file) => {
        formData.append('attachments', file);
      });
    }

    const response = await myApi.post('/forum/direct-messages', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  } catch (error) {
    console.error('Error sending direct message:', error);
    throw error;
  }
};

// Edit a message
export const editMessage = async (messageId: string, content: string) => {
  try {
    const response = await myApi.put(`/forum/messages/${messageId}`, {
      content,
    });
    toast.success('Message updated successfully');
    return response.data;
  } catch (error) {
    console.error('Error editing message:', error);
    throw error;
  }
};

// Delete a message
export const deleteMessage = async (messageId: string) => {
  try {
    const response = await myApi.delete(`/forum/messages/${messageId}`);
    toast.success('Message deleted successfully');
    return response.data;
  } catch (error) {
    console.error('Error deleting message:', error);
    throw error;
  }
};

// Pin a message
export const pinMessage = async (messageId: string) => {
  try {
    const response = await myApi.put(`/forum/messages/${messageId}/pin`);
    toast.success('Message pinned successfully');
    return response.data;
  } catch (error) {
    console.error('Error pinning message:', error);
    throw error;
  }
};

// Unpin a message
export const unpinMessage = async (messageId: string) => {
  try {
    const response = await myApi.put(`/forum/messages/${messageId}/unpin`);
    toast.success('Message unpinned successfully');
    return response.data;
  } catch (error) {
    console.error('Error unpinning message:', error);
    throw error;
  }
};

// Message Reactions API
export const addMessageReaction = async (messageId: string, emoji: string) => {
  try {
    const response = await myApi.post(
      `/forum/messages/${messageId}/reactions`,
      {
        emoji,
      }
    );
    return response.data;
  } catch (error) {
    console.error('Error adding reaction:', error);
    throw error;
  }
};

export const removeMessageReaction = async (
  messageId: string,
  emoji: string
) => {
  try {
    const response = await myApi.delete(
      `/forum/messages/${messageId}/reactions/${emoji}`
    );
    return response.data;
  } catch (error) {
    console.error('Error removing reaction:', error);
    throw error;
  }
};

export const getMessageReactions = (messageId: string) => {
  const { data, error, isLoading, mutate } = useSWR(
    messageId ? `/forum/messages/${messageId}/reactions` : null
  );

  return {
    reactions: data?.data || [],
    isLoading,
    error,
    mutate,
  };
};

// Message Threading API
export const getMessageThread = (messageId: string) => {
  const { data, error, isLoading, mutate } = useSWR(
    messageId ? `/forum/messages/${messageId}/thread` : null
  );

  return {
    thread: data?.data,
    isLoading,
    error,
    mutate,
  };
};

export const replyToMessage = async (
  parentMessageId: string,
  content: string,
  attachments?: File[]
) => {
  try {
    const formData = new FormData();
    formData.append('content', content);
    formData.append('parentMessageId', parentMessageId);

    if (attachments && attachments.length > 0) {
      attachments.forEach((file) => {
        formData.append('attachments', file);
      });
    }

    const response = await myApi.post('/forum/messages/reply', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  } catch (error) {
    console.error('Error replying to message:', error);
    throw error;
  }
};

// User Presence API
export const getUserPresence = (userId: string) => {
  const { data, error, isLoading, mutate } = useSWR(
    userId ? `/forum/users/${userId}/presence` : null
  );

  return {
    presence: data?.data,
    isLoading,
    error,
    mutate,
  };
};

export const getOnlineUsers = () => {
  const { data, error, isLoading, mutate } = useSWR('/forum/users/online');

  return {
    onlineUsers: data?.data || [],
    isLoading,
    error,
    mutate,
  };
};

// Direct Message Conversations API
export const getDirectMessageConversations = () => {
  const { data, error, isLoading, mutate } = useSWR(
    '/forum/direct-messages/conversations'
  );

  return {
    conversations: data?.data || [],
    isLoading,
    error,
    mutate,
  };
};

export const createDirectMessageConversation = async (
  participantIds: string[]
) => {
  try {
    const response = await myApi.post('/forum/direct-messages/conversations', {
      participantIds,
    });
    return response.data;
  } catch (error) {
    console.error('Error creating conversation:', error);
    throw error;
  }
};

// Search API
export const searchMessages = async (query: string, groupId?: string) => {
  try {
    const params = new URLSearchParams({ query });
    if (groupId) {
      params.append('groupId', groupId);
    }

    const response = await myApi.get(
      `/forum/search/messages?${params.toString()}`
    );
    return response.data;
  } catch (error) {
    console.error('Error searching messages:', error);
    throw error;
  }
};

export const searchGroups = async (query: string) => {
  try {
    const response = await myApi.get(
      `/forum/search/groups?query=${encodeURIComponent(query)}`
    );
    return response.data;
  } catch (error) {
    console.error('Error searching groups:', error);
    throw error;
  }
};

// Get forum analytics
export const GetForumAnalytics = (timeRange?: string) => {
  const params = timeRange ? `?timeRange=${timeRange}` : '';
  const { data, error, isLoading, mutate } = useSWR(
    `/forum/analytics${params}`
  );

  return {
    analytics: data?.data,
    isLoading,
    error,
    mutate,
  };
};
