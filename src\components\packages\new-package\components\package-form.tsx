'use client';

import { useEffect, useState } from 'react';
import { Form } from '@/components/ui/form';
import {
  InputField,
  FormRow,
  CurrencyInputField,
  FileUpload,
  InputTextArea,
} from '@/components/common/form';
import { X, Check } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { toast } from 'sonner';
import {
  formSchema,
  type ProductFormValues,
} from '@/components/validations/package';
import { Label } from '@/components/ui/label';
import { MultiSelect } from '@/components/common/multi-select';
import { Badge } from '@/components/ui/badge';
import { GetPackageTest } from '@/api/data';
import { GetLocations } from '@/api/data';
import { GetPublicCategories } from '@/api/data';
import { GetProfile } from '@/api/staff';
import Price from './price';
import { currencyFormat } from '@/lib/utils';
import { ComboBox } from '@/components/common/single-select';
import { myApi } from '@/api/fetcher';
import { useRouter } from 'next/navigation';
import type { LocationPriceForm } from '@/lib/types/types';

const PackageForm = () => {
  const router = useRouter();
  const { profile } = GetProfile();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const [selectedTests, setSelectedTests] = useState<string[]>([]);
  const [locationPrices, setLocationPrices] = useState<LocationPriceForm[]>([]);
  const [newLocation, setNewLocation] = useState<LocationPriceForm>({
    location: { id: '', name: '' },
    amount: '',
    currency: 'NGN',
    endDate: new Date(),
  });

  const { test } = GetPackageTest();
  const { locations } = GetLocations();
  const { category } = GetPublicCategories();
  const data = test?.data;
  const locationData = locations?.data;
  const categoryData = category?.data;

  interface Test {
    id: string;
    name: string;
  }

  const form = useForm<ProductFormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: '',
      description: '',
      image: undefined,
      test: [],
      slot: 0,
      basePrice: undefined,
      categoryId: 0,
    },
  });

  const handleAddLocationPrice = () => {
    const trimmedPrice = newLocation.amount.trim();
    const isValidNumber = /^\d*\.?\d+$/.test(trimmedPrice);
    const isLocationValid = newLocation.location.name.trim() !== '';
    const isValid = isLocationValid && isValidNumber;
    if (isValid) {
      setLocationPrices([...locationPrices, { ...newLocation }]);
      setNewLocation({
        location: { id: '', name: '' },
        amount: '',
        currency: 'NGN',
        endDate: new Date(),
      });
    }
  };

  const handleRemoveLocationPrice = (index: number) => {
    const updatedPrices = [...locationPrices];
    updatedPrices.splice(index, 1);
    setLocationPrices(updatedPrices);
  };

  const onSubmit = async (data: ProductFormValues) => {
    if (!data.image) {
      form.setError('image', {
        type: 'manual',
        message: 'Package image is required',
      });
      return;
    }

    if (selectedTests.length === 0) {
      form.setError('test', {
        type: 'manual',
        message: 'At least one test must be selected',
      });
      return;
    }

    // Create form data to submit
    const formData = {
      ...data,
      test: selectedTests,
      locationPrices: locationPrices,
    };

    try {
      setIsSubmitting(true);
      const fileUpload = new FormData();
      if (formData.image) {
        fileUpload.append('image', formData.image);
      }
      fileUpload.append('name', formData.name);
      fileUpload.append('description', formData.description);
      fileUpload.append('basePrice', formData.basePrice.toString());
      fileUpload.append('testIds', JSON.stringify(formData.test));
      fileUpload.append('prices', JSON.stringify(formData.locationPrices));
      fileUpload.append('categoryId', formData.categoryId.toString());
      fileUpload.append('totalSlot', formData.slot.toString());
      fileUpload.append('createdBy', profile.data.fullName);

      const res = await myApi.post('/package/add-new-package', fileUpload, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      setIsSubmitting(false);
      if (res.status === 200) {
        toast.success(res.data.message);
        router.push('/packages/manage-package');
      }
    } catch (err) {
      setIsSubmitting(false);
      // toast.error("An error occurred. Please try again.")
    }
  };

  return (
    <div className="mt-4">
      <Form {...form}>
        <form className="space-y-4">
          <FileUpload
            control={form.control}
            name="image"
            label="Package Image"
            accept="image/*"
          />

          <FormRow>
            <ComboBox
              control={form.control}
              name="categoryId"
              label="Category"
              placeholder="Select a category"
              options={categoryData || []}
              searchPlaceholder="Search categories..."
              emptyMessage="No categories found."
            />

            <InputField
              control={form.control}
              name="name"
              label="Package Name"
              placeholder="Enter the package name"
              type="text"
            />
            <InputField
              control={form.control}
              name="slot"
              label="Total Slot"
              placeholder="0"
              type="number"
              min={0}
            />
            <CurrencyInputField
              control={form.control}
              name="basePrice"
              label="Base Price"
              placeholder="The normal price"
            />
          </FormRow>
          <Label htmlFor="tests">Select Tests</Label>
          <MultiSelect
            options={test ? data : []}
            selected={selectedTests}
            onChange={setSelectedTests}
            placeholder="Select tests"
            valueField="id"
            labelField="name"
            badgeClassName="bg-primary text-primary-foreground hover:bg-primary/90"
            renderOption={(test, isSelected) => (
              <div className="flex flex-col">
                <div className="flex items-center">
                  <div className="mr-2 flex h-4 w-4 items-center justify-center">
                    {isSelected ? <Check className="h-4 w-4" /> : null}
                  </div>
                  <span>{(test as Test).name}</span>
                </div>
              </div>
            )}
          />
          {selectedTests.length === 0 && form.formState.isSubmitted && (
            <p className="text-sm text-red-500 mt-1">
              At least one test must be selected
            </p>
          )}
          <InputTextArea
            control={form.control}
            name="description"
            label="Package Description"
            placeholder="Enter package description"
          />
        </form>
        <div className="pt-6 w-full">
          {locationPrices.length > 0 && (
            <div className="mb-4 flex flex-wrap gap-2">
              {locationPrices.map((item, index) => (
                <Badge
                  onClick={() => handleRemoveLocationPrice(index)}
                  className="cursor-pointer h-8"
                  key={index}
                >
                  {item.location.name} - {item.currency}{' '}
                  {currencyFormat(item.amount)}
                  <X className="w-4 h-4" />
                </Badge>
              ))}
            </div>
          )}
          <Price
            locations={locations ? locationData : []}
            newLocation={newLocation}
            setNewLocation={setNewLocation}
            onAddLocationPrice={handleAddLocationPrice}
          />
        </div>
        <div className="text-center py-4">
          <Button
            className="cursor-pointer"
            type="button"
            onClick={form.handleSubmit(onSubmit)}
            disabled={isSubmitting || locationPrices.length === 0}
          >
            {isSubmitting ? 'Creating...' : 'Create Package'}
          </Button>
        </div>
      </Form>
    </div>
  );
};

export default PackageForm;
