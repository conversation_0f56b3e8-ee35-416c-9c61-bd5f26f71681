import React from 'react';
import Link from 'next/link';
import { ChevronRight } from 'lucide-react';

interface BreadcrumbItem {
  label: string;
  href?: string;
}

interface BreadcrumbProps {
  data: BreadcrumbItem[];
}

const Breadcrumb: React.FC<BreadcrumbProps> = ({ data }) => {
  return (
    <div className="flex font-medium text-xs">
      {data.map((item, index) => (
        <div key={item.label} className="flex items-center">
          {index > 0 && (
            <ChevronRight className="h-4 w-4 text-gray-500 dark:text-gray-400 mx-1" />
          )}
          {item.href ? (
            <Link
              href={item.href}
              className="text-gray-400 dark:text-gray-500 hover:text-gray-900 dark:hover:text-gray-100 transition-colors"
            >
              {item.label}
            </Link>
          ) : (
            <span className="text-gray-700 dark:text-gray-300">
              {item.label}
            </span>
          )}
        </div>
      ))}
    </div>
  );
};

export default Breadcrumb;
