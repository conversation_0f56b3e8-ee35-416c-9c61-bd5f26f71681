import type { InventoryItem, StockStatus } from './types/vmi';

export function getStockStatus(item: InventoryItem): StockStatus {
  const totalStock = item.hospitalStore + item.vmiStore;
  if (totalStock <= item.reorderLevel) return 'low';
  if (totalStock >= item.maxLevel * 0.8) return 'high';
  return 'normal';
}

export function calculateInventoryValue(items: InventoryItem[]): number {
  return items.reduce(
    (sum, item) =>
      sum + (item.hospitalStore + item.vmiStore) * item.product.unitPrice,
    0
  );
}

export function formatCurrency(amount: number): string {
  return new Intl.NumberFormat('en-NG', {
    style: 'currency',
    currency: 'NGN',
  }).format(amount);
}

export function isExpiringSoon(
  expiryDate: string,
  daysThreshold: number = 30
): boolean {
  const expiry = new Date(expiryDate);
  const today = new Date();
  const diffTime = expiry.getTime() - today.getTime();
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  return diffDays <= daysThreshold && diffDays > 0;
}

export function generateInvoiceNumber(): string {
  const date = new Date();
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const random = Math.floor(Math.random() * 1000)
    .toString()
    .padStart(3, '0');
  return `VMI-${year}${month}-${random}`;
}
