'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import { useRouter } from 'next/navigation';
import { WifiOff, RefreshCw, AlertTriangle } from 'lucide-react';

interface NetworkErrorProps {
  title?: string;
  message?: string;
  isTimeout?: boolean;
  error?: Error | null | unknown;
  onRetry?: () => void;
}

/**
 * A component to display network errors or timeouts
 */
export function NetworkError({
  title = 'Connection Error',
  message = "We're having trouble connecting to the server.",
  isTimeout = false,
  error,
  onRetry,
}: NetworkErrorProps) {
  const router = useRouter();

  const handleRetry = () => {
    if (onRetry) {
      onRetry();
    } else {
      // Refresh the page as a fallback
      window.location.reload();
    }
  };

  const handleGoHome = () => {
    router.push('/dashboard');
  };

  return (
    <div className="flex min-h-screen items-center justify-center bg-gray-50 dark:bg-gray-900">
      <div className="mx-auto max-w-md rounded-lg bg-white p-8 shadow-lg dark:bg-gray-800">
        <div className="flex flex-col items-center text-center">
          {isTimeout ? (
            <AlertTriangle className="h-16 w-16 text-amber-500" />
          ) : (
            <WifiOff className="h-16 w-16 text-red-500" />
          )}

          <h1 className="mt-4 text-2xl font-bold text-gray-900 dark:text-white">
            {isTimeout ? 'Verification Timeout' : title}
          </h1>

          <p className="mt-2 text-gray-600 dark:text-gray-300">
            {isTimeout
              ? "We couldn't verify your permissions in time. This might be due to a slow network connection."
              : message}
          </p>

          {/* {error && (
            <div className="mt-4 rounded-md bg-gray-100 p-3 text-sm text-gray-700 dark:bg-gray-700 dark:text-gray-300">
              <p className="font-medium">Error details:</p>
              <p className="mt-1 font-mono"></p>
            </div>
          )} */}

          <div className="mt-6 flex space-x-4">
            <Button
              variant="outline"
              onClick={handleGoHome}
              className="flex items-center gap-2"
            >
              Go to Dashboard
            </Button>

            <Button onClick={handleRetry} className="flex items-center gap-2">
              <RefreshCw className="h-4 w-4" />
              Try Again
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
