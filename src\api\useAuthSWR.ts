import useSWR, { SWRConfiguration, Key } from 'swr';
import { useSnapshot } from 'valtio';
import { accessTokenStore } from '@/store/accessToken';
import { fetcher } from '@/api/fetcher';

type UseAuthSWRReturn<Data = any, Error = any> = {
  data?: Data;
  error?: Error;
  isLoading: boolean;
  mutate: () => Promise<Data | undefined>;
};

export function useAuthSWR<Data = any, Error = any>(
  key: Key, // e.g. '/profile'
  config?: SWRConfiguration
): UseAuthSWRReturn<Data, Error> {
  const snap = useSnapshot(accessTokenStore);
  const accessToken = snap.accessToken;

  // If no token, skip fetch by passing null key to SWR
  const swrKey = accessToken ? key : null;

  const { data, error, isLoading, mutate } = useSWR<Data, Error>(
    swrKey,
    fetcher,
    {
      revalidateOnFocus: true,
      revalidateOnReconnect: true,
      ...config,
    }
  );

  return { data, error, isLoading, mutate };
}
