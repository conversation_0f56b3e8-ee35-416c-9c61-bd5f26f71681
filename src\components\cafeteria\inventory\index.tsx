'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Plus, Search, Edit, PackageMinus, Eye } from 'lucide-react';
import SupplyEntryModal from './components/supply-entry-modal';
import IssueModal from './components/issue-modal';
import { ViewSupplies } from './components/view-supplies';
import { ViewIssued } from './components/view-issued';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { GetAllInventory } from '@/api/cafeteria/data';
import { GetProfile } from '@/api/staff';
import { EmptyState, LoadingState } from '@/components/common/dataState';
import Analytics from './components/analytics';
import Pagination from '@/components/common/pagination';
import CreateInventoryCat from './components/add-category';
import CreateInventory from './components/add-inventory';
import dayjs from 'dayjs';
import { hasPermission, PERMISSIONS } from '@/lib/types/permissions';
import { useSearchAndPagination } from '@/hooks/useSearchAndPagination';

// Define the inventory item interface
interface InventoryItem {
  id: number;
  name: string;
  quantity: number;
  unit: string;
  category: string;
  lastUpdated: string;
}

export default function InventoryManagement() {
  const { profile } = GetProfile();
  const [openCreateInventoryCat, setOpenCreateInventoryCat] = useState(false);
  const [openCreateInventory, setOpenCreateInventory] = useState(false);

  const [isSupplyModalOpen, setIsSupplyModalOpen] = useState(false);
  const [isViewingSupplies, setIsViewingSupplies] = useState(false);
  const [isViewingIssued, setIsViewingIssued] = useState(false);
  const [isIssueModalOpen, setIsIssueModalOpen] = useState(false);
  const [selectedItem, setSelectedItem] = useState<InventoryItem | null>(null);

  const {
    searchTerm,
    handleSearchChange,
    currentPage,
    pageSize,
    handlePageChange,
    queryParam,
  } = useSearchAndPagination({ initialPageSize: 2 });

  const { inventory, inventoryLoading, mutate } = GetAllInventory(
    `?page=${currentPage}&limit=${pageSize}${queryParam ? `&${queryParam}` : ''}`
  );
  const inventoryData = inventory?.data?.inventory;
  const totalPages = inventory?.data?.totalPages ?? 0;

  const canAdd = hasPermission(PERMISSIONS.LOCATION_ALL);

  if (isViewingSupplies && selectedItem) {
    return (
      <ViewSupplies
        selectedItem={selectedItem}
        onBack={() => setIsViewingSupplies(false)}
      />
    );
  }
  if (isViewingIssued && selectedItem) {
    return (
      <ViewIssued
        selectedItem={selectedItem}
        onBack={() => setIsViewingIssued(false)}
      />
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <div className="relative w-64">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            type="text"
            placeholder="Search ..."
            className="pl-10 pr-4 py-2 w-full"
            value={searchTerm}
            onChange={handleSearchChange}
          />
        </div>

        <div className="flex gap-2">
          <Button
            onClick={() => setOpenCreateInventoryCat(true)}
            variant="secondary"
            className="flex items-center gap-2"
          >
            <Plus className="h-4 w-4" /> Add Item Category
          </Button>
          {canAdd && (
            <Button
              onClick={() => setOpenCreateInventory(true)}
              className="flex items-center gap-2"
            >
              <Plus className="h-4 w-4" /> Add Item
            </Button>
          )}
        </div>
      </div>
      <Analytics stats={inventory?.data} />

      <h1>Inventory List</h1>
      <div className="border rounded-md p-2">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Item Name</TableHead>
              <TableHead>Category</TableHead>
              <TableHead>Unit</TableHead>
              <TableHead>Minimum Stock</TableHead>
              <TableHead>Current Stock</TableHead>
              <TableHead>Last Updated</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {inventoryLoading ? (
              <TableRow>
                <TableCell colSpan={7} className="text-center py-4">
                  <LoadingState />
                </TableCell>
              </TableRow>
            ) : inventoryData?.length > 0 ? (
              inventoryData.map((item: any) => (
                <TableRow key={item.id}>
                  <TableCell className="font-medium">{item.itemName}</TableCell>
                  <TableCell>{item?.inventoryCat.name}</TableCell>
                  <TableCell>{item.unit}</TableCell>
                  <TableCell>{item.minimumStock}</TableCell>
                  <TableCell
                    className={
                      item.currentStock === 0
                        ? 'text-red-500'
                        : Number(item.currentStock) < Number(item.minimumStock)
                          ? 'text-amber-500'
                          : ''
                    }
                  >
                    {item.currentStock}
                  </TableCell>
                  <TableCell>
                    {dayjs(item.updatedAt).format('YYYY-MM-DD HH:MM A')}
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="flex justify-end">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon">
                            <Edit className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem
                            onClick={() => {
                              setSelectedItem(item);
                              setIsSupplyModalOpen(true);
                            }}
                          >
                            <Plus className="h-4 w-4 mr-2" />
                            Add Supplies
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() => {
                              setSelectedItem(item);
                              setIsViewingSupplies(true);
                            }}
                          >
                            <Eye className="h-4 w-4 mr-2" />
                            View Supplies
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon">
                            <PackageMinus className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem
                            onClick={() => {
                              setSelectedItem(item);
                              setIsIssueModalOpen(true);
                            }}
                          >
                            <PackageMinus className="h-4 w-4 mr-2" />
                            Issue Stock
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() => {
                              setSelectedItem(item);
                              setIsViewingIssued(true);
                            }}
                          >
                            <Eye className="h-4 w-4 mr-2" />
                            View Issued Stocks
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={7} className="text-center py-4">
                  <EmptyState />
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      {totalPages > 1 ? (
        <Pagination
          title="Inventories"
          totalPages={totalPages}
          currentPage={currentPage}
          onPageChange={handlePageChange}
          totalCount={inventory?.data?.totalCount}
        />
      ) : (
        ''
      )}

      {/* Supply Entry Modal */}
      <SupplyEntryModal
        open={isSupplyModalOpen}
        setOpen={setIsSupplyModalOpen}
        profile={profile?.data}
        props={selectedItem}
        mutate={mutate}
      />

      {/* Issue Modal */}
      <IssueModal
        open={isIssueModalOpen}
        setOpen={setIsIssueModalOpen}
        props={selectedItem}
        profile={profile?.data}
        mutate={mutate}
      />
      <CreateInventoryCat
        open={openCreateInventoryCat}
        setOpen={setOpenCreateInventoryCat}
        profile={profile?.data}
      />
      <CreateInventory
        open={openCreateInventory}
        setOpen={setOpenCreateInventory}
        mutate={mutate}
        profile={profile?.data}
      />
    </div>
  );
}
