import { fetcher } from '../fetcher';
import type {
  Product,
  InventoryItem,
  Request,
  Invoice,
  InvoiceItem,
  VMIStats,
  VMIChartData,
} from '@/lib/types/vmi';

// Mock data for development
const mockProducts: Product[] = [
  {
    id: '1',
    name: 'Paracetamol 500mg',
    category: 'Analgesics',
    manufacturer: 'PharmaCorp',
    batchNumber: 'PC001',
    expiryDate: '2025-12-31',
    unitPrice: 250,
    description: 'Pain relief medication',
  },
  {
    id: '2',
    name: 'Amoxicillin 250mg',
    category: 'Antibiotics',
    manufacturer: 'MediLab',
    batchNumber: 'ML002',
    expiryDate: '2025-06-30',
    unitPrice: 600,
    description: 'Antibiotic for bacterial infections',
  },
  {
    id: '3',
    name: 'Ibuprofen 400mg',
    category: 'Analgesics',
    manufacturer: 'PainAway Ltd',
    batchNumber: 'PA003',
    expiryDate: '2024-03-15',
    unitPrice: 180,
    description: 'Anti-inflammatory medication',
  },
  {
    id: '4',
    name: 'Metformin 500mg',
    category: 'Antidiabetics',
    manufacturer: 'DiabCare',
    batchNumber: 'DC004',
    expiryDate: '2025-08-20',
    unitPrice: 320,
    description: 'Diabetes management',
  },
  {
    id: '5',
    name: 'Omeprazole 20mg',
    category: 'Gastroenterology',
    manufacturer: 'GastroMed',
    batchNumber: 'GM005',
    expiryDate: '2025-11-10',
    unitPrice: 450,
    description: 'Acid reflux treatment',
  },
];

const mockInventory: InventoryItem[] = [
  {
    id: '1',
    productId: '1',
    product: mockProducts[0],
    hospitalStore: 150,
    vmiStore: 500,
    reorderLevel: 100,
    maxLevel: 1000,
    lastUpdated: '2025-01-15T10:30:00Z',
  },
  {
    id: '2',
    productId: '2',
    product: mockProducts[1],
    hospitalStore: 80,
    vmiStore: 200,
    reorderLevel: 50,
    maxLevel: 500,
    lastUpdated: '2025-01-15T09:15:00Z',
  },
  {
    id: '3',
    productId: '3',
    product: mockProducts[2],
    hospitalStore: 25,
    vmiStore: 75,
    reorderLevel: 50,
    maxLevel: 300,
    lastUpdated: '2025-01-14T14:20:00Z',
  },
  {
    id: '4',
    productId: '4',
    product: mockProducts[3],
    hospitalStore: 200,
    vmiStore: 800,
    reorderLevel: 150,
    maxLevel: 1200,
    lastUpdated: '2025-01-15T11:45:00Z',
  },
  {
    id: '5',
    productId: '5',
    product: mockProducts[4],
    hospitalStore: 60,
    vmiStore: 140,
    reorderLevel: 80,
    maxLevel: 400,
    lastUpdated: '2025-01-15T08:30:00Z',
  },
];

const mockChartData: VMIChartData = {
  monthlyValue: [
    { period: 'Jan', value: 2200000 },
    { period: 'Feb', value: 2450000 },
    { period: 'Mar', value: 2850000 },
    { period: 'Apr', value: 2650000 },
    { period: 'May', value: 3100000 },
    { period: 'Jun', value: 2900000 },
    { period: 'Jul', value: 3200000 },
    { period: 'Aug', value: 2750000 },
    { period: 'Sep', value: 3050000 },
    { period: 'Oct', value: 2850000 },
    { period: 'Nov', value: 3150000 },
    { period: 'Dec', value: 2950000 },
  ],
  yearlyValue: [
    { period: '2021', value: 28500000 },
    { period: '2022', value: 32200000 },
    { period: '2023', value: 35800000 },
    { period: '2024', value: 34200000 },
  ],
  monthlyRequests: [
    { period: 'Jan', value: 45, requests: 45, invoices: 12 },
    { period: 'Feb', value: 52, requests: 52, invoices: 15 },
    { period: 'Mar', value: 38, requests: 38, invoices: 10 },
    { period: 'Apr', value: 61, requests: 61, invoices: 18 },
    { period: 'May', value: 48, requests: 48, invoices: 14 },
    { period: 'Jun', value: 55, requests: 55, invoices: 16 },
    { period: 'Jul', value: 42, requests: 42, invoices: 11 },
    { period: 'Aug', value: 58, requests: 58, invoices: 17 },
    { period: 'Sep', value: 49, requests: 49, invoices: 13 },
    { period: 'Oct', value: 53, requests: 53, invoices: 15 },
    { period: 'Nov', value: 46, requests: 46, invoices: 12 },
    { period: 'Dec', value: 51, requests: 51, invoices: 14 },
  ],
  yearlyRequests: [
    { period: '2021', value: 485, requests: 485, invoices: 142 },
    { period: '2022', value: 562, requests: 562, invoices: 168 },
    { period: '2023', value: 634, requests: 634, invoices: 185 },
    { period: '2024', value: 598, requests: 598, invoices: 167 },
  ],
};

export const vmiApi = {
  // Products
  getProducts: () => Promise.resolve(mockProducts),
  createProduct: (product: Omit<Product, 'id'>) =>
    Promise.resolve({ ...product, id: Date.now().toString() }),

  // Inventory
  getInventory: () => Promise.resolve(mockInventory),
  updateInventory: (id: string, data: Partial<InventoryItem>) =>
    Promise.resolve({ ...mockInventory.find((i) => i.id === id), ...data }),

  // Requests
  getRequests: () => Promise.resolve([]),
  createRequest: (request: Omit<Request, 'id'>) =>
    Promise.resolve({ ...request, id: Date.now().toString() }),

  // Invoices
  getInvoices: () => Promise.resolve([]),
  createInvoice: (invoice: Omit<Invoice, 'id'>) =>
    Promise.resolve({ ...invoice, id: Date.now().toString() }),

  // Statistics
  getStats: (): Promise<VMIStats> =>
    Promise.resolve({
      totalProducts: mockProducts.length,
      lowStockItems: 2,
      pendingRequests: 8,
      monthlyValue: 2850000,
      outstandingInvoices: 5,
      totalInventoryValue: 1250000,
      expiringProducts: 1,
      approvedRequests: 12,
      fulfilledRequests: 18,
      averageOrderValue: 85000,
      topSellingCategory: 'Analgesics',
      stockTurnoverRate: 4.2,
      hospitalStoreValue: 425000,
      vmiStoreValue: 825000,
    }),

  // Chart Data
  getChartData: (): Promise<VMIChartData> => Promise.resolve(mockChartData),
};
