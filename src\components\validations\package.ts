import { z } from 'zod';

export const formSchema = z.object({
  name: z.string().min(1, { message: 'Package name is required' }),
  description: z.string().min(50, { message: 'Description must be detailed' }),
  image: z.instanceof(File).optional(),
  test: z.array(z.string()).optional(),
  bonus: z.boolean().optional(),
  registration: z.boolean().optional(),
  categoryId: z
    .number()
    .or(z.string().transform((val) => Number(val)))
    .refine((val) => val > 0, {
      message: 'Category is required',
    }),
  slot: z.coerce
    .number()
    .int()
    .min(1, { message: 'Slot must be minimum of 10' }),
  basePrice: z
    .string()
    .regex(/^\d*\.?\d+$/, { message: 'Base Price must be a valid amount' })
    .refine((value) => parseFloat(value) >= 0, {
      message: 'Base Price must be a positive amount',
    })
    .transform((value) => parseFloat(value)),
});

export type ProductFormValues = z.infer<typeof formSchema>;
