import React, { useState, useEffect } from 'react';
import { Modal } from '@/components/common/modal';
import { myApi } from '@/api/fetcher';
import { toast } from 'sonner';
import { ModalProps } from '@/components/types';
import { InputField, SwitchField, FormRow } from '@/components/common/form';
import { Form } from '@/components/ui/form';
import { useForm } from 'react-hook-form';
import dayjs from 'dayjs';

interface FormData {
  id: number;
  value: string;
  deactivated: boolean;
}

const Details: React.FC<ModalProps> = ({ open, setOpen, mutate, data }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [editValue, setEditValue] = useState(false);

  const form = useForm<FormData>({
    defaultValues: {
      value: data?.value,
      deactivated: data?.deactivated,
    },
  });

  useEffect(() => {
    if (data) {
      form.reset(data);
    }
  }, [data, form]);

  const onSubmit = async (data: FormData) => {
    try {
      setIsLoading(true);
      const res = await myApi.patch(`/reward/update/`, {
        id: data?.id,
        deactivated: data.deactivated,
        value: Number(data?.value),
      });
      setIsLoading(false);
      if (res.status === 200) {
        toast.success(res.data.data.message);
        setOpen(false);
        setEditValue(false);
        if (mutate) {
          mutate();
        }
      }
    } catch (error) {
      setIsLoading(false);
    }
  };

  return (
    <Modal
      open={open}
      setOpen={setOpen}
      title="Reward Details"
      description="View and manage reward details"
      isLoading={isLoading}
      onSubmit={form.handleSubmit(onSubmit)}
    >
      <Form {...form}>
        <form className="space-y-4">
          <FormRow>
            <div>
              <h3 className="text-sm font-medium">Name</h3>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {data?.name}
              </p>
            </div>
            <div>
              <h3 className="text-sm font-medium">Type</h3>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {data?.type}
              </p>
            </div>
            <div>
              <h3 className="text-sm font-medium">
                Value{' '}
                <span
                  onClick={() => setEditValue(!editValue)}
                  className="text-xs text-[#BD9A3D] underline cursor-pointer"
                >
                  (edit)
                </span>
              </h3>
              {editValue ? (
                <InputField
                  control={form.control}
                  name="value"
                  label="Enter new value"
                  placeholder=""
                  type="number"
                />
              ) : (
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  {data?.value}
                </p>
              )}
            </div>
            <div>
              <h3 className="text-sm font-medium">Date Created</h3>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {data?.createdAt
                  ? dayjs(data.createdAt).format('MMMM D, YYYY')
                  : '-'}
              </p>
            </div>
          </FormRow>
          <SwitchField
            control={form.control}
            name="deactivated"
            label="Is Deactivated?"
          />
          <div>
            <h3 className="text-sm font-medium">Description</h3>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              {data?.description}
            </p>
          </div>
        </form>
      </Form>
    </Modal>
  );
};

export default Details;
