'use client';

import { useState } from 'react';
import {
  TrendingUp,
  TrendingDown,
  ArrowUpCircle,
  ArrowDownCircle,
  Loader2,
  XCircle,
} from 'lucide-react';
import {
  Bar,
  Bar<PERSON>hart,
  CartesianGrid,
  XAxis,
  YAxis,
  Tooltip,
  Legend,
  Line,
  LineChart,
  ResponsiveContainer,
  Pie,
  <PERSON>hart,
  Cell,
} from 'recharts';
import YearSelect from '../../common/year-select';
import { GetTransactionAnalytics } from '@/api/analytics';
import { formatValue, numberFormat } from '@/lib/utils';

import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from '@/components/ui/chart';

const chartConfig = {
  inbound: {
    label: 'Inbound',
    theme: {
      light: '#BD9A3D',
      dark: '#BD9A3D',
    },
  },
  outbound: {
    label: 'Outbound',
    theme: {
      light: '#2196F3',
      dark: '#2196F3',
    },
  },
  inboundAmount: {
    label: 'Inbound Amount',
    theme: {
      light: '#BD9A3D',
      dark: '#BD9A3D',
    },
  },
  outboundAmount: {
    label: 'Outbound Amount',
    theme: {
      light: '#2196F3',
      dark: '#2196F3',
    },
  },
} satisfies ChartConfig;

const COLORS = ['#BD9A3D', '#2196F3'];

export function TransactionAnalytics() {
  const [year, setYear] = useState<string>(new Date().getFullYear().toString());

  const handleYearChange = (selectedYear: string) => {
    setYear(selectedYear);
  };

  // Use the real API function with the year parameter
  const { transactionStats, transactionStatsLoading, transactionStatsError } =
    GetTransactionAnalytics(`year=${year}`);

  // Handle loading state
  if (transactionStatsLoading) {
    return (
      <div className="flex flex-col items-center justify-center h-[400px]">
        <Loader2
          className="w-8 h-8 animate-spin"
          style={{ color: '#BD9A3D' }}
        />
        <p className="text-muted-foreground mt-4">
          Loading transaction analytics...
        </p>
      </div>
    );
  }

  // Handle error state
  if (transactionStatsError) {
    return (
      <div className="flex flex-col items-center justify-center h-[400px]">
        <XCircle className="w-8 h-8 mb-4" style={{ color: '#2196F3' }} />
        <p>Error loading transaction analytics. Please try again later.</p>
      </div>
    );
  }

  // If no data is available, use mock data for development
  const data = transactionStats?.data.data;

  return (
    <div className="space-y-4">
      <div className="flex flex-wrap gap-4">
        {/* Summary Cards */}
        <Card className="w-full md:w-[calc(25%-1rem)]">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">
              Total Transactions
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.totalTransactions}</div>
            <div className="flex items-center mt-1 text-xs">
              {data.recentTrend.direction === 'up' ? (
                <TrendingUp
                  className="w-3 h-3 mr-1"
                  style={{ color: '#BD9A3D' }}
                />
              ) : (
                <TrendingDown
                  className="w-3 h-3 mr-1"
                  style={{ color: '#2196F3' }}
                />
              )}
              <span
                style={{
                  color:
                    data.recentTrend.direction === 'up' ? '#BD9A3D' : '#2196F3',
                }}
              >
                {data.recentTrend.percent}% from last month
              </span>
            </div>
          </CardContent>
        </Card>

        <Card className="w-full md:w-[calc(25%-1rem)]">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Total Amount</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {numberFormat(data.totalAmount)}
            </div>
            <div className="flex items-center mt-1 text-xs">
              <span className="text-[#2196F3] text-lg mr-1">₦</span>
              <span className="text-muted-foreground">
                Across all transactions
              </span>
            </div>
          </CardContent>
        </Card>

        <Card className="w-full md:w-[calc(25%-1rem)]">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Inbound</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {
                data.transactionTypes.find((t: any) => t.type === 'INBOUND')
                  ?.count
              }
            </div>
            <div className="flex items-center mt-1 text-xs">
              <ArrowDownCircle
                className="w-3 h-3 mr-1"
                style={{ color: '#BD9A3D' }}
              />
              <span style={{ color: '#BD9A3D' }}>
                {numberFormat(
                  data.transactionTypes.find((t: any) => t.type === 'INBOUND')
                    ?.amount || 0
                )}
              </span>
            </div>
          </CardContent>
        </Card>

        <Card className="w-full md:w-[calc(25%-1rem)]">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Outbound</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {
                data.transactionTypes.find((t: any) => t.type === 'OUTBOUND')
                  ?.count
              }
            </div>
            <div className="flex items-center mt-1 text-xs">
              <ArrowUpCircle
                className="w-3 h-3 mr-1"
                style={{ color: '#2196F3' }}
              />
              <span style={{ color: '#2196F3' }}>
                {numberFormat(
                  data.transactionTypes.find((t: any) => t.type === 'OUTBOUND')
                    ?.amount || 0
                )}
              </span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Card>
          <CardHeader>
            <div className="flex flex-wrap gap-4 justify-between">
              <div>
                <CardTitle>Transaction Count</CardTitle>
                <CardDescription>
                  Monthly transaction count by type
                </CardDescription>
              </div>
              <div>
                <YearSelect onChange={handleYearChange} />
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <ChartContainer
              className="aspect-auto h-[250px]"
              config={chartConfig}
            >
              <BarChart data={data.monthlyData}>
                <CartesianGrid vertical={false} strokeDasharray="3 3" />
                <XAxis
                  dataKey="month"
                  tickLine={false}
                  tickMargin={10}
                  axisLine={false}
                  tickFormatter={(value) => value.slice(0, 3)}
                />
                <YAxis axisLine={false} tickLine={false} tickMargin={10} />
                <Tooltip
                  formatter={(value) => [`${value} transactions`, '']}
                  labelFormatter={(label) => `Month: ${label}`}
                />
                <Legend />
                <Bar
                  dataKey="inbound"
                  name="Inbound"
                  fill="#BD9A3D"
                  radius={4}
                />
                <Bar
                  dataKey="outbound"
                  name="Outbound"
                  fill="#2196F3"
                  radius={4}
                />
              </BarChart>
            </ChartContainer>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <div className="flex flex-wrap gap-4 justify-between">
              <div>
                <CardTitle>Transaction Amount</CardTitle>
                <CardDescription>
                  Monthly transaction amount by type
                </CardDescription>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <ChartContainer
              className="aspect-auto h-[250px]"
              config={chartConfig}
            >
              <LineChart data={data.monthlyData}>
                <CartesianGrid vertical={false} strokeDasharray="3 3" />
                <XAxis
                  dataKey="month"
                  tickLine={false}
                  tickMargin={10}
                  axisLine={false}
                  tickFormatter={(value) => value.slice(0, 3)}
                />
                <YAxis
                  axisLine={false}
                  tickLine={false}
                  tickMargin={10}
                  tickFormatter={(value) => `₦${value / 1000}k`}
                />
                <Tooltip
                  formatter={(value) => [
                    `${numberFormat(value as number)}`,
                    '',
                  ]}
                  labelFormatter={(label) => `Month: ${label}`}
                />
                <Legend />
                <Line
                  type="monotone"
                  dataKey="inboundAmount"
                  name="Inbound"
                  stroke="#BD9A3D"
                  activeDot={{ r: 8 }}
                />
                <Line
                  type="monotone"
                  dataKey="outboundAmount"
                  name="Outbound"
                  stroke="#2196F3"
                />
              </LineChart>
            </ChartContainer>
          </CardContent>
        </Card>
      </div>

      {/* Transaction Type Distribution */}
      <Card>
        <CardHeader>
          <CardTitle>Transaction Type Distribution</CardTitle>
          <CardDescription>Breakdown of transaction types</CardDescription>
        </CardHeader>
        <CardContent className="flex justify-center">
          <div className="w-full max-w-md">
            <ResponsiveContainer width="100%" height={250}>
              <PieChart>
                <Pie
                  data={data.transactionTypes}
                  dataKey="count"
                  nameKey="type"
                  cx="50%"
                  cy="50%"
                  outerRadius={80}
                  label={({ type, count }) => `${type}: ${count}`}
                >
                  {data.transactionTypes.map((entry: any, index: number) => (
                    <Cell
                      key={`cell-${index}`}
                      fill={COLORS[index % COLORS.length]}
                    />
                  ))}
                </Pie>
                <Tooltip
                  formatter={(value) => [`${value} transactions`, 'Count']}
                />
                <Legend />
              </PieChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
