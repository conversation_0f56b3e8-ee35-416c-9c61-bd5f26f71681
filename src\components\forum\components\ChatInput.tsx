import React, { useState, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Paperclip, Send } from 'lucide-react';
import { sendGroupMessage, sendDirectMessage } from '@/api/forum/data';

interface ChatInputProps {
  groupId?: string;
  recipientId?: string;
  onMessageSent: () => void;
}

export const ChatInput: React.FC<ChatInputProps> = ({
  groupId,
  recipientId,
  onMessageSent,
}) => {
  const [message, setMessage] = useState('');
  const [attachments, setAttachments] = useState<File[]>([]);
  const [isSending, setIsSending] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleSend = async () => {
    if (!message.trim() && attachments.length === 0) return;

    setIsSending(true);

    try {
      if (groupId) {
        await sendGroupMessage(groupId, message, attachments);
      } else if (recipientId) {
        await sendDirectMessage(recipientId, message, attachments);
      }

      setMessage('');
      setAttachments([]);
      onMessageSent();
    } catch (error) {
      console.error('Error sending message:', error);
    } finally {
      setIsSending(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const newFiles = Array.from(e.target.files);
      setAttachments((prev) => [...prev, ...newFiles]);
    }
  };

  const removeAttachment = (index: number) => {
    setAttachments((prev) => prev.filter((_, i) => i !== index));
  };

  return (
    <div className="border-t p-2 md:p-4">
      {attachments.length > 0 && (
        <div className="flex flex-wrap gap-1 md:gap-2 mb-2">
          {attachments.map((file, index) => (
            <div
              key={index}
              className="bg-muted text-xs md:text-sm px-2 py-1 rounded flex items-center gap-1"
            >
              <span className="truncate max-w-[100px] md:max-w-[150px]">
                {file.name}
              </span>
              <button
                type="button"
                className="text-muted-foreground hover:text-destructive"
                onClick={() => removeAttachment(index)}
              >
                &times;
              </button>
            </div>
          ))}
        </div>
      )}

      <div className="flex gap-2 items-end">
        <Textarea
          value={message}
          onChange={(e) => setMessage(e.target.value)}
          onKeyDown={handleKeyDown}
          placeholder="Type a message..."
          className="min-h-[40px] md:min-h-[60px] text-sm md:text-base resize-none"
          disabled={isSending}
        />

        <div className="flex flex-col gap-2 shrink-0">
          <Button
            type="button"
            size="icon"
            variant="outline"
            className="h-8 w-8 md:h-10 md:w-10"
            onClick={() => fileInputRef.current?.click()}
            disabled={isSending}
          >
            <Paperclip className="h-3 w-3 md:h-4 md:w-4" />
            <input
              type="file"
              ref={fileInputRef}
              onChange={handleFileChange}
              className="hidden"
              multiple
            />
          </Button>

          <Button
            type="button"
            size="icon"
            className="h-8 w-8 md:h-10 md:w-10"
            onClick={handleSend}
            disabled={
              (!message.trim() && attachments.length === 0) || isSending
            }
          >
            <Send className="h-3 w-3 md:h-4 md:w-4" />
          </Button>
        </div>
      </div>
    </div>
  );
};
