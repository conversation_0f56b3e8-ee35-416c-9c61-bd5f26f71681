import React, { useState } from 'react';
import { InputField } from '@/components/common/form';
import { Form } from '@/components/ui/form';
import { Modal } from '@/components/common/modal';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { formSchema } from '@/components/validations/createCategory';
import { myApi } from '@/api/fetcher';
import { toast } from 'sonner';
import { GetProfile } from '@/api/staff';
import { ModalProps } from '../../../types';

interface FormData {
  name: string;
}

const Create: React.FC<ModalProps> = ({ setOpen, mutate, open }) => {
  const [isLoading, setIsLoading] = useState(false);
  const { profile } = GetProfile();

  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: '',
    },
    mode: 'onTouched',
  });

  const onSubmit = async (data: FormData) => {
    try {
      setIsLoading(true);
      const res = await myApi.post('/package/create-category', {
        name: data.name,
        createdBy: profile.data.fullName,
      });
      setIsLoading(false);
      if (res.status === 200) {
        toast.success(res.data.data.message);
        if (mutate) {
          mutate();
        }
        setOpen(false);
        form.reset();
      }
    } catch (error) {
      setIsLoading(false);
    }
  };

  return (
    <Modal
      open={open}
      setOpen={setOpen}
      title="Add Category"
      description="Enter a category name to add to the list of package categories"
      isLoading={isLoading}
      onSubmit={form.handleSubmit(onSubmit)}
    >
      <Form {...form}>
        <form className="space-y-8">
          <InputField
            control={form.control}
            name="name"
            label="Category Name"
            placeholder="Wellness Packages"
            type="text"
          />
        </form>
      </Form>
    </Modal>
  );
};

export default Create;
