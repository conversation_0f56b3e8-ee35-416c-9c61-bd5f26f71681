const fs = require('fs');
const path = require('path');

// Directories to exclude
const excludeDirs = ['node_modules', '.next', 'out', 'build', 'dist', '.git'];

// File extensions to include
const includeExtensions = [
  '.js',
  '.jsx',
  '.ts',
  '.tsx',
  '.css',
  '.scss',
  '.html',
  '.json',
  '.md',
];

// Stats object
const stats = {
  totalFiles: 0,
  totalLines: 0,
  byExtension: {},
};

// Function to count lines in a file
function countLinesInFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const lines = content.split('\n').length;
    return lines;
  } catch (error) {
    console.error(`Error reading file ${filePath}: ${error.message}`);
    return 0;
  }
}

// Function to scan directory recursively
function scanDirectory(dirPath) {
  try {
    const entries = fs.readdirSync(dirPath, { withFileTypes: true });

    for (const entry of entries) {
      const fullPath = path.join(dirPath, entry.name);

      if (entry.isDirectory()) {
        // Skip excluded directories
        if (excludeDirs.includes(entry.name)) {
          continue;
        }
        scanDirectory(fullPath);
      } else if (entry.isFile()) {
        const ext = path.extname(entry.name).toLowerCase();

        // Only count files with included extensions
        if (includeExtensions.includes(ext)) {
          const lineCount = countLinesInFile(fullPath);

          // Update stats
          stats.totalFiles++;
          stats.totalLines += lineCount;

          // Update extension stats
          if (!stats.byExtension[ext]) {
            stats.byExtension[ext] = {
              files: 0,
              lines: 0,
            };
          }
          stats.byExtension[ext].files++;
          stats.byExtension[ext].lines += lineCount;
        }
      }
    }
  } catch (error) {
    console.error(`Error scanning directory ${dirPath}: ${error.message}`);
  }
}

// Start scanning from the current directory
const rootDir = process.cwd();
console.log(`Scanning directory: ${rootDir}`);
console.log('This may take a moment...');

scanDirectory(rootDir);

// Sort extensions by line count
const sortedExtensions = Object.keys(stats.byExtension).sort(
  (a, b) => stats.byExtension[b].lines - stats.byExtension[a].lines
);

// Print results
console.log('\n=== Lines of Code Summary ===');
console.log(`Total Files: ${stats.totalFiles}`);
console.log(`Total Lines: ${stats.totalLines}`);
console.log('\nBreakdown by file extension:');

sortedExtensions.forEach((ext) => {
  const { files, lines } = stats.byExtension[ext];
  console.log(`${ext}: ${lines.toLocaleString()} lines in ${files} files`);
});
