'use client';

import React, { useState, useRef, useEffect } from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Bot,
  Send,
  User,
  Trash2,
  Download,
  Copy,
  RotateCcw,
  Sparkles,
  MessageSquare,
  Clock,
  CheckCircle,
} from 'lucide-react';
import { toast } from 'sonner';
import { hasPermission, PERMISSIONS } from '@/lib/types/permissions';
import { GetProfile } from '@/api/staff';
import {
  Message,
  ChatSession,
  sendMessage,
  mockAIResponse,
  getSuggestedPrompts,
} from '@/api/ai-assistant/data';

export default function AIAssistantPage() {
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      role: 'assistant',
      content:
        "Hello! I'm your AI Assistant for Cedarcrest Hospital. I can help you with medical information, hospital procedures, patient care guidelines, administrative tasks and more. I'm currently in development and will be available to assist you once completed. Thank you for your patience!",
      timestamp: new Date(),
    },
  ]);
  const [inputMessage, setInputMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [chatSessions, setChatSessions] = useState<ChatSession[]>([]);
  const [currentSessionId, setCurrentSessionId] = useState<string | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const { profile } = GetProfile();

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const generateMessageId = () => {
    return Date.now().toString() + Math.random().toString(36).substr(2, 9);
  };

  const handleSendMessage = async () => {
    if (!inputMessage.trim() || isLoading) return;

    const userMessage: Message = {
      id: generateMessageId(),
      role: 'user',
      content: inputMessage.trim(),
      timestamp: new Date(),
    };

    setMessages((prev) => [...prev, userMessage]);
    setInputMessage('');
    setIsLoading(true);

    // Add typing indicator
    const typingMessage: Message = {
      id: 'typing',
      role: 'assistant',
      content: '',
      timestamp: new Date(),
      isTyping: true,
    };
    setMessages((prev) => [...prev, typingMessage]);

    try {
      // Call AI Assistant API (using mock for now)
      const aiResponseContent = await mockAIResponse(userMessage.content);

      // Remove typing indicator
      setMessages((prev) => prev.filter((msg) => msg.id !== 'typing'));

      // Generate AI response
      const aiResponse: Message = {
        id: generateMessageId(),
        role: 'assistant',
        content: aiResponseContent,
        timestamp: new Date(),
      };

      setMessages((prev) => [...prev, aiResponse]);
    } catch (error) {
      console.error('Error sending message:', error);
      toast.error('Failed to send message. Please try again.');
      setMessages((prev) => prev.filter((msg) => msg.id !== 'typing'));
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const clearChat = () => {
    setMessages([
      {
        id: '1',
        role: 'assistant',
        content:
          "Hello! I'm your AI Assistant for Cedarcrest Hospital. I can help you with medical information, hospital procedures, patient care guidelines, administrative tasks and more. I'm currently in development and will be available to assist you once completed. Thank you for your patience!",
        timestamp: new Date(),
      },
    ]);
    toast.success('Chat cleared');
  };

  const copyMessage = (content: string) => {
    navigator.clipboard.writeText(content);
    toast.success('Message copied to clipboard');
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const suggestedPrompts = [
    'What are the hospital visiting hours?',
    'How do I schedule a patient appointment?',
    'What are the emergency procedures?',
    'Tell me about patient discharge protocols',
    'How do I access patient medical records?',
    'What are the infection control guidelines?',
  ];

  return (
    <div className="h-[calc(100vh-120px)] flex flex-col">
      {/* Header */}
      <div className="flex items-center justify-between p-3 sm:p-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center space-x-2 sm:space-x-3">
          <div className="relative">
            <Bot className="h-6 w-6 sm:h-8 sm:w-8 text-primary" />
            <div className="absolute -top-1 -right-1 w-2 h-2 sm:w-3 sm:h-3 bg-green-500 rounded-full border-2 border-white dark:border-gray-900"></div>
          </div>
          <div>
            <h1 className="text-lg sm:text-2xl font-bold">AI Assistant</h1>
            <p className="text-xs sm:text-sm text-gray-600 dark:text-gray-400 hidden sm:block">
              Your intelligent healthcare companion
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-1 sm:space-x-2">
          <Badge
            variant="outline"
            className="text-green-600 border-green-600 text-xs"
          >
            <CheckCircle className="w-2 h-2 sm:w-3 sm:h-3 mr-1" />
            <span className="hidden sm:inline">Online</span>
          </Badge>
          <Button
            variant="outline"
            size="sm"
            onClick={clearChat}
            className="px-2 sm:px-3"
          >
            <Trash2 className="h-3 w-3 sm:h-4 sm:w-4 sm:mr-2" />
            <span className="hidden sm:inline">Clear Chat</span>
          </Button>
        </div>
      </div>

      {/* Chat Area */}
      <div className="flex-1 flex">
        {/* Main Chat */}
        <div className="flex-1 flex flex-col">
          {/* Messages */}
          <ScrollArea className="flex-1 p-2 sm:p-4">
            <div className="space-y-3 sm:space-y-4 max-w-4xl mx-auto">
              {messages.length === 1 && (
                <div className="text-center py-4 sm:py-8">
                  <div className="grid grid-cols-1 gap-2 sm:gap-3 mb-4 sm:mb-6">
                    {suggestedPrompts.map((prompt, index) => (
                      <Button
                        key={index}
                        variant="outline"
                        className="text-left justify-start h-auto p-2 sm:p-3 whitespace-normal text-xs sm:text-sm"
                        onClick={() => setInputMessage(prompt)}
                      >
                        <MessageSquare className="h-3 w-3 sm:h-4 sm:w-4 mr-2 flex-shrink-0" />
                        {prompt}
                      </Button>
                    ))}
                  </div>
                </div>
              )}

              {messages.map((message) => (
                <div
                  key={message.id}
                  className={`flex items-start space-x-2 sm:space-x-3 ${
                    message.role === 'user'
                      ? 'flex-row-reverse space-x-reverse'
                      : ''
                  }`}
                >
                  <Avatar className="w-6 h-6 sm:w-8 sm:h-8 flex-shrink-0">
                    {message.role === 'user' ? (
                      <>
                        <AvatarImage src={profile?.data?.profilePicture} />
                        <AvatarFallback>
                          <User className="h-3 w-3 sm:h-4 sm:w-4" />
                        </AvatarFallback>
                      </>
                    ) : (
                      <AvatarFallback className="bg-primary text-primary-foreground">
                        <Bot className="h-3 w-3 sm:h-4 sm:w-4" />
                      </AvatarFallback>
                    )}
                  </Avatar>

                  <div
                    className={`flex-1 max-w-[85%] sm:max-w-[80%] ${
                      message.role === 'user' ? 'text-right' : ''
                    }`}
                  >
                    <div
                      className={`rounded-lg p-2 sm:p-3 text-sm sm:text-base ${
                        message.role === 'user'
                          ? 'bg-primary text-primary-foreground ml-auto'
                          : 'bg-gray-100 dark:bg-gray-800'
                      }`}
                    >
                      {message.isTyping ? (
                        <div className="flex items-center space-x-1">
                          <div className="flex space-x-1">
                            <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                            <div
                              className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"
                              style={{ animationDelay: '0.1s' }}
                            ></div>
                            <div
                              className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"
                              style={{ animationDelay: '0.2s' }}
                            ></div>
                          </div>
                          <span className="text-sm text-gray-500 ml-2">
                            AI is typing...
                          </span>
                        </div>
                      ) : (
                        <div className="whitespace-pre-wrap">
                          {message.content}
                        </div>
                      )}
                    </div>

                    <div
                      className={`flex items-center mt-1 space-x-1 sm:space-x-2 text-xs text-gray-500 ${
                        message.role === 'user' ? 'justify-end' : ''
                      }`}
                    >
                      <Clock className="h-2 w-2 sm:h-3 sm:w-3" />
                      <span className="text-xs">
                        {formatTime(message.timestamp)}
                      </span>
                      {!message.isTyping && (
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-5 w-5 sm:h-6 sm:w-6 p-0"
                          onClick={() => copyMessage(message.content)}
                        >
                          <Copy className="h-2 w-2 sm:h-3 sm:w-3" />
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              ))}
              <div ref={messagesEndRef} />
            </div>
          </ScrollArea>

          {/* Input Area */}
          <div className="border-t border-gray-200 dark:border-gray-700 p-3 sm:p-4">
            <div className="max-w-4xl mx-auto">
              <div className="flex items-end space-x-2">
                <div className="flex-1 relative">
                  <Input
                    ref={inputRef}
                    value={inputMessage}
                    onChange={(e) => setInputMessage(e.target.value)}
                    onKeyPress={handleKeyPress}
                    placeholder="Type your message..."
                    disabled={isLoading}
                    className="pr-4 min-h-[40px] sm:min-h-[44px] resize-none text-sm sm:text-base"
                  />
                </div>
                <Button
                  onClick={handleSendMessage}
                  disabled={!inputMessage.trim() || isLoading}
                  className="h-10 sm:h-11 px-3 sm:px-4"
                >
                  {isLoading ? (
                    <RotateCcw className="h-3 w-3 sm:h-4 sm:w-4 animate-spin" />
                  ) : (
                    <Send className="h-3 w-3 sm:h-4 sm:w-4" />
                  )}
                </Button>
              </div>

              <div className="flex items-center justify-between mt-2 text-xs text-gray-500">
                <span className="hidden sm:block">
                  AI Assistant can make mistakes. Please verify important
                  information.
                </span>
                <span className="sm:hidden text-xs">Verify AI responses</span>
                <span>{inputMessage.length}/2000</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
