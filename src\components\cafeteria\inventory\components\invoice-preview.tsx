'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Download, Eye } from 'lucide-react';

interface InvoicePreviewProps {
  invoicePath: string;
}

export function InvoicePreview({ invoicePath }: InvoicePreviewProps) {
  const [isOpen, setIsOpen] = useState(false);

  const isImage = /\.(jpg|jpeg|png|gif|webp)$/i.test(invoicePath);
  const isPdf = /\.pdf$/i.test(invoicePath);

  const handleDownload = () => {
    const link = document.createElement('a');
    link.href = invoicePath;
    link.download = invoicePath.split('/').pop() || 'invoice';
    link.click();
  };

  return (
    <>
      <Button
        variant="ghost"
        size="sm"
        onClick={() => setIsOpen(true)}
        className="flex items-center gap-2"
      >
        <Eye className="h-4 w-4" />
        View Invoice
      </Button>

      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh]">
          <DialogHeader>
            <DialogTitle className="flex items-center justify-between">
              Invoice Preview
              <Button
                onClick={handleDownload}
                size="sm"
                className="flex items-center gap-2"
              >
                <Download className="h-4 w-4" />
                Download
              </Button>
            </DialogTitle>
          </DialogHeader>

          <div className="flex-1 overflow-auto">
            {isImage && (
              <img
                src={invoicePath}
                alt="Invoice"
                className="w-full h-auto max-h-[70vh] object-contain"
              />
            )}
            {isPdf && (
              <iframe
                src={invoicePath}
                className="w-full h-[70vh]"
                title="Invoice PDF"
              />
            )}
            {!isImage && !isPdf && (
              <div className="text-center py-8">
                <p>Preview not available for this file type</p>
                <Button onClick={handleDownload} className="mt-4">
                  Download File
                </Button>
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
}
