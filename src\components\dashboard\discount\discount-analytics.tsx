'use client';

import { useState } from 'react';
import {
  TrendingUp,
  TrendingDown,
  Percent,
  Tag,
  BadgePercent,
  Loader2,
  XCircle,
} from 'lucide-react';
import {
  Bar,
  BarChart,
  CartesianGrid,
  XAxis,
  YAxis,
  Tooltip,
  Legend,
  Line,
  LineChart,
  ResponsiveContainer,
  Pie,
  <PERSON>Chart,
  Cell,
} from 'recharts';
import YearSelect from '../../common/year-select';
import { GetDiscountAnalytics } from '@/api/analytics';
import { formatValue, numberFormat } from '@/lib/utils';

import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from '@/components/ui/chart';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';

const chartConfig = {
  usage: {
    label: 'Usage',
    theme: {
      light: '#BD9A3D',
      dark: '#BD9A3D',
    },
  },
  savings: {
    label: 'Savings',
    theme: {
      light: '#2196F3',
      dark: '#2196F3',
    },
  },
} satisfies ChartConfig;

const COLORS = ['#BD9A3D', '#2196F3'];

export function DiscountAnalytics() {
  const [year, setYear] = useState<string>(new Date().getFullYear().toString());

  const handleYearChange = (selectedYear: string) => {
    setYear(selectedYear);
  };

  // Use the real API function with the year parameter
  const { discountStats, discountStatsLoading, discountStatsError } =
    GetDiscountAnalytics(`year=${year}`);

  // Handle loading state
  if (discountStatsLoading) {
    return (
      <div className="flex flex-col items-center justify-center h-[400px]">
        <Loader2
          className="w-8 h-8 animate-spin"
          style={{ color: '#BD9A3D' }}
        />
        <p className="text-muted-foreground mt-4">
          Loading discount analytics...
        </p>
      </div>
    );
  }

  // Handle error state
  if (discountStatsError) {
    return (
      <div className="flex flex-col items-center justify-center h-[400px]">
        <XCircle className="w-8 h-8 mb-4" style={{ color: '#2196F3' }} />
        <p>Error loading discount analytics. Please try again later.</p>
      </div>
    );
  }

  // If no data is available, use mock data for development
  const data = discountStats?.data.data;

  return (
    <div className="space-y-4">
      <div className="flex flex-wrap gap-4">
        {/* Summary Cards */}
        <Card className="w-full md:w-[calc(25%-1rem)]">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">
              Total Discounts
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.totalDiscounts}</div>
            <div className="flex items-center mt-1 text-xs">
              <Tag className="w-3 h-3 mr-1" style={{ color: '#BD9A3D' }} />
              <span className="text-muted-foreground">
                {data.activeDiscounts} active
              </span>
            </div>
          </CardContent>
        </Card>

        <Card className="w-full md:w-[calc(25%-1rem)]">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Total Usage</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{data.totalUsage}</div>
            <div className="flex items-center mt-1 text-xs">
              <BadgePercent
                className="w-3 h-3 mr-1"
                style={{ color: '#2196F3' }}
              />
              <span className="text-muted-foreground">
                Across all discounts
              </span>
            </div>
          </CardContent>
        </Card>

        <Card className="w-full md:w-[calc(25%-1rem)]">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">
              Savings Generated
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {numberFormat(data.savingsGenerated)}
            </div>
            <div className="flex items-center mt-1 text-xs">
              <Percent className="w-3 h-3 mr-1" style={{ color: '#BD9A3D' }} />
              <span style={{ color: '#BD9A3D' }}>For customers</span>
            </div>
          </CardContent>
        </Card>

        <Card className="w-full md:w-[calc(25%-1rem)]">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">
              Avg. Savings/Use
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {numberFormat(data.savingsGenerated / data.totalUsage)}
            </div>
            <div className="flex items-center mt-1 text-xs">
              <TrendingUp
                className="w-3 h-3 mr-1"
                style={{ color: '#2196F3' }}
              />
              <span className="text-muted-foreground">Per discount usage</span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Card>
          <CardHeader>
            <div className="flex flex-wrap gap-4 justify-between">
              <div>
                <CardTitle>Monthly Usage</CardTitle>
                <CardDescription>Discount usage by month</CardDescription>
              </div>
              <div>
                <YearSelect onChange={handleYearChange} />
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <ChartContainer
              className="aspect-auto h-[250px]"
              config={chartConfig}
            >
              <BarChart data={data.monthlyData}>
                <CartesianGrid vertical={false} strokeDasharray="3 3" />
                <XAxis
                  dataKey="month"
                  tickLine={false}
                  tickMargin={10}
                  axisLine={false}
                  tickFormatter={(value) => value.slice(0, 3)}
                />
                <YAxis axisLine={false} tickLine={false} tickMargin={10} />
                <Tooltip
                  formatter={(value) => [`${value} uses`, 'Usage']}
                  labelFormatter={(label) => `Month: ${label}`}
                />
                <Bar dataKey="usage" fill="#BD9A3D" radius={4} name="Usage" />
              </BarChart>
            </ChartContainer>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <div className="flex flex-wrap gap-4 justify-between">
              <div>
                <CardTitle>Monthly Savings</CardTitle>
                <CardDescription>Savings generated by month</CardDescription>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <ChartContainer
              className="aspect-auto h-[250px]"
              config={chartConfig}
            >
              <LineChart data={data.monthlyData}>
                <CartesianGrid vertical={false} strokeDasharray="3 3" />
                <XAxis
                  dataKey="month"
                  tickLine={false}
                  tickMargin={10}
                  axisLine={false}
                  tickFormatter={(value) => value.slice(0, 3)}
                />
                <YAxis
                  axisLine={false}
                  tickLine={false}
                  tickMargin={10}
                  tickFormatter={(value) => `₦${value / 1000}k`}
                />
                <Tooltip
                  formatter={(value) => [
                    `${numberFormat(value as number)}`,
                    'Savings',
                  ]}
                  labelFormatter={(label) => `Month: ${label}`}
                />
                <Line
                  type="monotone"
                  dataKey="savings"
                  stroke="#2196F3"
                  activeDot={{ r: 8 }}
                  name="Savings"
                />
              </LineChart>
            </ChartContainer>
          </CardContent>
        </Card>
      </div>

      {/* Top Discounts Table */}
      <Card>
        <CardHeader>
          <CardTitle>Top Performing Discounts</CardTitle>
          <CardDescription>
            Discounts with highest usage and savings
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Discount Code</TableHead>
                <TableHead>Usage Count</TableHead>
                <TableHead>Savings Generated</TableHead>
                <TableHead>Avg. Savings/Use</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {data.topDiscounts.map((discount: any) => (
                <TableRow key={discount.code}>
                  <TableCell className="font-medium">{discount.code}</TableCell>
                  <TableCell>{discount.usage}</TableCell>
                  <TableCell>{numberFormat(discount.savings)}</TableCell>
                  <TableCell>
                    {numberFormat(discount.savings / discount.usage)}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Discount Type Distribution */}
      <Card>
        <CardHeader>
          <CardTitle>Discount Type Distribution</CardTitle>
          <CardDescription>Breakdown by discount type</CardDescription>
        </CardHeader>
        <CardContent className="flex justify-center">
          <div className="w-full max-w-md">
            <ResponsiveContainer width="100%" height={250}>
              <PieChart>
                <Pie
                  data={data.discountTypes}
                  dataKey="count"
                  nameKey="type"
                  cx="50%"
                  cy="50%"
                  outerRadius={80}
                  label={({ type, count }) => `${type}: ${count}`}
                >
                  {data.discountTypes.map((entry: any, index: number) => (
                    <Cell
                      key={`cell-${index}`}
                      fill={COLORS[index % COLORS.length]}
                    />
                  ))}
                </Pie>
                <Tooltip
                  formatter={(value) => [`${value} discounts`, 'Count']}
                />
                <Legend />
              </PieChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
