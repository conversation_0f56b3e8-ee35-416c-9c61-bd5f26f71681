import { ReactNode } from 'react';

interface VMILayoutProps {
  children: ReactNode;
  title: string;
  description?: string;
}

export function VMILayout({ children, title, description }: VMILayoutProps) {
  return (
    <div className="p-6 space-y-6">
      <div className="space-y-2">
        <h1 className="text-3xl font-bold">{title}</h1>
        {description && <p className="text-muted-foreground">{description}</p>}
      </div>
      {children}
    </div>
  );
}
