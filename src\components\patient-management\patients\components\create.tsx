import React, { useState } from 'react';
import { InputField } from '@/components/common/form';
import { Form } from '@/components/ui/form';
import { Modal } from '@/components/common/modal';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { myApi } from '@/api/fetcher';
import { toast } from 'sonner';
import {
  patientFormSchema,
  PatientFormValues,
} from '@/components/validations/crm';
import { GetProfile } from '@/api/staff';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { ModalProps } from '@/components/crm/types';

const Create: React.FC<ModalProps> = ({ setOpen, open, mutate }) => {
  const [isLoading, setIsLoading] = useState(false);
  const { profile } = GetProfile();

  const form = useForm<PatientFormValues>({
    resolver: zod<PERSON>esolver(patientFormSchema),
    defaultValues: {
      firstName: '',
      lastName: '',
      emailAddress: '',
      phoneNumber: '',
    },
  });

  const onSubmit = async (data: PatientFormValues) => {
    try {
      setIsLoading(true);
      const res = await myApi.post('/crm/patients', {
        ...data,
        createdBy: profile.data.fullName,
      });
      setIsLoading(false);
      if (res.status === 200) {
        toast.success('Patient created successfully');
        if (mutate) {
          mutate();
        }
        setOpen(false);
        form.reset();
      }
    } catch (error: any) {
      setIsLoading(false);
      toast.error(error?.response?.data?.message || 'An error occurred');
    }
  };

  return (
    <Modal
      title="Add New Patient"
      open={open}
      setOpen={setOpen}
      isLoading={isLoading}
      description="Create a new patient record with the specified details"
      onSubmit={form.handleSubmit(onSubmit)}
    >
      <Form {...form}>
        <form className="space-y-4">
          <div className="grid grid-cols-3 gap-4">
            <div className="col-span-1">
              <Select
                onValueChange={(value) => form.setValue('title', value)}
                defaultValue={form.getValues('title')}
              >
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Title" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Mr">Mr</SelectItem>
                  <SelectItem value="Mrs">Mrs</SelectItem>
                  <SelectItem value="Ms">Ms</SelectItem>
                  <SelectItem value="Dr">Dr</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="col-span-2">
              <InputField
                control={form.control}
                name="firstName"
                label="First Name"
                placeholder="John"
              />
            </div>
          </div>

          <InputField
            control={form.control}
            name="lastName"
            label="Last Name"
            placeholder="Doe"
          />

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium">Date of Birth</label>
              <input
                type="date"
                className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                {...form.register('dateOfBirth')}
              />
            </div>

            <div>
              <label className="text-sm font-medium">Gender</label>
              <Select
                onValueChange={(value) => form.setValue('gender', value)}
                defaultValue={form.getValues('gender')}
              >
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Select Gender" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="male">Male</SelectItem>
                  <SelectItem value="female">Female</SelectItem>
                  <SelectItem value="other">Other</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <InputField
              control={form.control}
              name="emailAddress"
              label="Email"
              placeholder="<EMAIL>"
              type="email"
            />

            <InputField
              control={form.control}
              name="phoneNumber"
              label="Phone Number"
              placeholder="+234 ************"
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium">Blood Type</label>
              <Select
                onValueChange={(value) => form.setValue('bloodType', value)}
                defaultValue={form.getValues('bloodType')}
              >
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Select Blood Type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="A+">A+</SelectItem>
                  <SelectItem value="A-">A-</SelectItem>
                  <SelectItem value="B+">B+</SelectItem>
                  <SelectItem value="B-">B-</SelectItem>
                  <SelectItem value="AB+">AB+</SelectItem>
                  <SelectItem value="AB-">AB-</SelectItem>
                  <SelectItem value="O+">O+</SelectItem>
                  <SelectItem value="O-">O-</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="text-sm font-medium">Insurance Provider</label>
              <InputField
                control={form.control}
                name="insuranceProvider"
                label="Insurance Provider"
                placeholder="Insurance Provider"
              />
            </div>
          </div>

          <InputField
            control={form.control}
            name="insuranceNumber"
            label="Insurance Number"
            placeholder="Insurance ID/Number"
          />

          <div>
            <label className="text-sm font-medium">Allergies</label>
            <Textarea
              placeholder="List any known allergies"
              className="resize-none"
              {...form.register('allergies')}
            />
          </div>

          <div>
            <label className="text-sm font-medium">Medical History</label>
            <Textarea
              placeholder="Brief medical history"
              className="resize-none"
              {...form.register('medicalHistory')}
            />
          </div>

          <InputField
            control={form.control}
            name="address"
            label="Address"
            placeholder="123 Main St"
          />

          <div className="grid grid-cols-3 gap-4">
            <InputField
              control={form.control}
              name="city"
              label="City"
              placeholder="Lagos"
            />

            <InputField
              control={form.control}
              name="state"
              label="State"
              placeholder="Lagos"
            />

            <InputField
              control={form.control}
              name="zipCode"
              label="Zip Code"
              placeholder="100001"
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <InputField
              control={form.control}
              name="emergencyContact"
              label="Emergency Contact"
              placeholder="Next of Kin Name"
            />

            <InputField
              control={form.control}
              name="emergencyContactPhone"
              label="Emergency Contact Phone"
              placeholder="Next of Kin Phone Number"
            />
          </div>

          <div>
            <label className="text-sm font-medium">Additional Notes</label>
            <Textarea
              placeholder="Additional notes about the patient"
              className="resize-none"
              {...form.register('notes')}
            />
          </div>
        </form>
      </Form>
    </Modal>
  );
};

export default Create;
