export interface Product {
  id: string;
  name: string;
  category: string;
  manufacturer: string;
  batchNumber: string;
  expiryDate: string;
  unitPrice: number;
  description?: string;
}

export interface InventoryItem {
  id: string;
  productId: string;
  product: Product;
  hospitalStore: number;
  vmiStore: number;
  reorderLevel: number;
  maxLevel: number;
  lastUpdated: string;
}

export interface Request {
  id: string;
  productId: string;
  product: Product;
  quantity: number;
  requestedBy: string;
  requestDate: string;
  status: 'pending' | 'approved' | 'rejected' | 'fulfilled';
  approvedBy?: string;
  approvalDate?: string;
  notes?: string;
}

export interface Invoice {
  id: string;
  invoiceNumber: string;
  requestIds: string[];
  totalAmount: number;
  issueDate: string;
  dueDate: string;
  status: 'draft' | 'sent' | 'paid' | 'overdue';
  items: InvoiceItem[];
}

export interface InvoiceItem {
  productId: string;
  product: Product;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
}

export interface VMIStats {
  totalProducts: number;
  lowStockItems: number;
  pendingRequests: number;
  monthlyValue: number;
  outstandingInvoices: number;
  totalInventoryValue: number;
  expiringProducts: number;
  approvedRequests: number;
  fulfilledRequests: number;
  averageOrderValue: number;
  topSellingCategory: string;
  stockTurnoverRate: number;
  hospitalStoreValue: number;
  vmiStoreValue: number;
}

export type StockStatus = 'low' | 'normal' | 'high';
export type RequestStatus = 'pending' | 'approved' | 'rejected' | 'fulfilled';
export type InvoiceStatus = 'draft' | 'sent' | 'paid' | 'overdue';

export interface ChartDataPoint {
  period: string;
  value: number;
  requests?: number;
  invoices?: number;
}

export interface VMIChartData {
  monthlyValue: ChartDataPoint[];
  yearlyValue: ChartDataPoint[];
  monthlyRequests: ChartDataPoint[];
  yearlyRequests: ChartDataPoint[];
}
