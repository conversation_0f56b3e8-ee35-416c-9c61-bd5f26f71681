'use client';

import React, { useEffect } from 'react';
import socketService from '@/services/websocket';
import { forumStore } from '@/store/forumStore';
import { SOCKET_EVENTS } from '@/lib/socket-config';

export const WebSocketProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  useEffect(() => {
    forumStore.setConnectionStatus('connecting');
    socketService.connect();

    const handleConnected = () => {
      forumStore.setConnectionStatus('connected');
      socketService.updatePresence('online');
    };

    const handleDisconnected = () => {
      const status = socketService.getConnectionStatus();
      forumStore.setConnectionStatus(status);
    };

    const handleConnectError = () => {
      // Check if we're in offline mode after connection errors
      setTimeout(() => {
        if (socketService.isOfflineMode()) {
          forumStore.setConnectionStatus('offline');
        }
      }, 1000);
    };

    socketService.on(SOCKET_EVENTS.CONNECT, handleConnected);
    socketService.on(SOCKET_EVENTS.DISCONNECT, handleDisconnected);
    socketService.on(SOCKET_EVENTS.CONNECT_ERROR, handleConnectError);

    const handleVisibilityChange = () => {
      if (socketService.isConnectedToServer()) {
        if (document.hidden) {
          socketService.updatePresence('away');
        } else {
          socketService.updatePresence('online');
        }
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      socketService.off(SOCKET_EVENTS.CONNECT, handleConnected);
      socketService.off(SOCKET_EVENTS.DISCONNECT, handleDisconnected);
      socketService.off(SOCKET_EVENTS.CONNECT_ERROR, handleConnectError);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      if (socketService.isConnectedToServer()) {
        socketService.updatePresence('offline');
      }
      socketService.disconnect();
    };
  }, []);

  return <>{children}</>;
};

export default WebSocketProvider;
