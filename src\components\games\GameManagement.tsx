import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import {
  Plus,
  Search,
  Trophy,
  Clock,
  Users,
  Play,
  Edit,
  Trash2,
  Eye,
  Calendar,
  Target,
  Award,
  TrendingUp,
  Settings,
  Copy,
  Pause,
  PlayCircle,
  StopCircle,
} from 'lucide-react';
import { GetGames, GetGameAnalytics } from '@/api/games/data';
import { CreateGameModal } from './CreateGameModal';
import { GameDetailsModal } from './GameDetailsModal';
import { GameAnalytics } from './GameAnalytics';
import { formatDistanceToNow } from 'date-fns';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';

interface GameManagementProps {
  className?: string;
}

export const GameManagement: React.FC<GameManagementProps> = ({
  className,
}) => {
  const [activeTab, setActiveTab] = useState('games');
  const [searchQuery, setSearchQuery] = useState('');
  const [showCreateGame, setShowCreateGame] = useState(false);
  const [selectedGame, setSelectedGame] = useState<any>(null);
  const [showGameDetails, setShowGameDetails] = useState(false);
  const [gameFilter, setGameFilter] = useState<
    'all' | 'active' | 'scheduled' | 'completed' | 'draft'
  >('all');

  const { games, isLoading: gamesLoading, mutate: mutateGames } = GetGames();
  const { analytics, isLoading: analyticsLoading } = GetGameAnalytics();

  const filteredGames =
    games?.filter((game: any) => {
      const matchesSearch =
        game.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        game.description.toLowerCase().includes(searchQuery.toLowerCase());
      const matchesFilter = gameFilter === 'all' || game.status === gameFilter;
      return matchesSearch && matchesFilter;
    }) || [];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-500 text-white';
      case 'scheduled':
        return 'bg-blue-500 text-white';
      case 'completed':
        return 'bg-gray-500 text-white';
      case 'draft':
        return 'bg-yellow-500 text-white';
      case 'cancelled':
        return 'bg-red-500 text-white';
      default:
        return 'bg-gray-500 text-white';
    }
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy':
        return 'text-green-600 bg-green-50 dark:bg-green-950';
      case 'medium':
        return 'text-yellow-600 bg-yellow-50 dark:bg-yellow-950';
      case 'hard':
        return 'text-red-600 bg-red-50 dark:bg-red-950';
      default:
        return 'text-gray-600 bg-gray-50 dark:bg-gray-950';
    }
  };

  const handleGameAction = async (
    gameId: string,
    action: 'start' | 'pause' | 'stop' | 'duplicate' | 'delete'
  ) => {
    try {
      switch (action) {
        case 'start':
          // API call to start game
          toast.success('Game started successfully');
          break;
        case 'pause':
          // API call to pause game
          toast.success('Game paused');
          break;
        case 'stop':
          // API call to stop game
          toast.success('Game stopped');
          break;
        case 'duplicate':
          // API call to duplicate game
          toast.success('Game duplicated');
          break;
        case 'delete':
          if (confirm('Are you sure you want to delete this game?')) {
            // API call to delete game
            toast.success('Game deleted');
          }
          break;
      }
      mutateGames();
    } catch (error) {
      console.error(`Error ${action}ing game:`, error);
      toast.error(`Failed to ${action} game`);
    }
  };

  const handleViewDetails = (game: any) => {
    setSelectedGame(game);
    setShowGameDetails(true);
  };

  return (
    <div className={cn('space-y-6', className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Game Management</h2>
          <p className="text-muted-foreground">
            Create and manage quiz games, tournaments, and competitions
          </p>
        </div>

        <Button onClick={() => setShowCreateGame(true)}>
          <Plus className="h-4 w-4 mr-2" />
          Create Game
        </Button>
      </div>

      {/* Search and Filters */}
      <div className="flex items-center gap-4">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search games..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="games" className="flex items-center gap-2">
            <Trophy className="h-4 w-4" />
            Games
          </TabsTrigger>
          <TabsTrigger value="analytics" className="flex items-center gap-2">
            <TrendingUp className="h-4 w-4" />
            Analytics
          </TabsTrigger>
        </TabsList>

        <TabsContent value="games" className="space-y-4">
          {/* Game Filters */}
          <div className="flex gap-2">
            <Button
              variant={gameFilter === 'all' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setGameFilter('all')}
            >
              All Games
            </Button>
            <Button
              variant={gameFilter === 'active' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setGameFilter('active')}
            >
              Active
            </Button>
            <Button
              variant={gameFilter === 'scheduled' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setGameFilter('scheduled')}
            >
              Scheduled
            </Button>
            <Button
              variant={gameFilter === 'completed' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setGameFilter('completed')}
            >
              Completed
            </Button>
            <Button
              variant={gameFilter === 'draft' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setGameFilter('draft')}
            >
              Drafts
            </Button>
          </div>

          {/* Games Grid */}
          {gamesLoading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {Array.from({ length: 6 }).map((_, i) => (
                <Card key={i} className="animate-pulse">
                  <CardHeader>
                    <div className="h-4 bg-muted rounded w-3/4"></div>
                    <div className="h-3 bg-muted rounded w-1/2"></div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      <div className="h-3 bg-muted rounded"></div>
                      <div className="h-3 bg-muted rounded w-2/3"></div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {filteredGames.map((game: any) => (
                <Card
                  key={game.id}
                  className="hover:shadow-md transition-shadow"
                >
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <CardTitle className="text-lg">{game.name}</CardTitle>
                        <p className="text-sm text-muted-foreground line-clamp-2">
                          {game.description}
                        </p>
                      </div>
                      <Badge className={getStatusColor(game.status)}>
                        {game.status}
                      </Badge>
                    </div>
                  </CardHeader>

                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div className="flex items-center gap-1">
                        <Users className="h-4 w-4 text-muted-foreground" />
                        <span>
                          {game.currentParticipants}/{game.maxParticipants}
                        </span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Clock className="h-4 w-4 text-muted-foreground" />
                        <span>{game.duration}min</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Target className="h-4 w-4 text-muted-foreground" />
                        <span>{game.totalQuestions}Q</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Award className="h-4 w-4 text-muted-foreground" />
                        <span>₦{game.prizePool.toLocaleString()}</span>
                      </div>
                    </div>

                    <div className="flex items-center justify-between">
                      <Badge
                        variant="outline"
                        className={getDifficultyColor(game.difficulty)}
                      >
                        {game.difficulty}
                      </Badge>

                      <div className="text-xs text-muted-foreground">
                        {formatDistanceToNow(new Date(game.createdAt), {
                          addSuffix: true,
                        })}
                      </div>
                    </div>

                    {/* Action Buttons */}
                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleViewDetails(game)}
                      >
                        <Eye className="h-4 w-4" />
                      </Button>

                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          /* Handle edit */
                        }}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>

                      {game.status === 'draft' && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleGameAction(game.id, 'start')}
                        >
                          <PlayCircle className="h-4 w-4" />
                        </Button>
                      )}

                      {game.status === 'active' && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleGameAction(game.id, 'pause')}
                        >
                          <Pause className="h-4 w-4" />
                        </Button>
                      )}

                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleGameAction(game.id, 'duplicate')}
                      >
                        <Copy className="h-4 w-4" />
                      </Button>

                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleGameAction(game.id, 'delete')}
                        className="text-destructive hover:text-destructive"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}

          {!gamesLoading && filteredGames.length === 0 && (
            <div className="text-center py-12">
              <Trophy className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
              <h3 className="text-lg font-medium mb-2">No games found</h3>
              <p className="text-muted-foreground mb-4">
                {searchQuery
                  ? 'Try adjusting your search terms'
                  : 'Create your first game to get started'}
              </p>
              {!searchQuery && (
                <Button onClick={() => setShowCreateGame(true)}>
                  <Plus className="h-4 w-4 mr-2" />
                  Create Game
                </Button>
              )}
            </div>
          )}
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <GameAnalytics analytics={analytics} isLoading={analyticsLoading} />
        </TabsContent>
      </Tabs>

      {/* Modals */}
      <CreateGameModal
        isOpen={showCreateGame}
        onClose={() => setShowCreateGame(false)}
        onGameCreated={() => {
          setShowCreateGame(false);
          mutateGames();
        }}
      />

      {selectedGame && (
        <GameDetailsModal
          isOpen={showGameDetails}
          onClose={() => {
            setShowGameDetails(false);
            setSelectedGame(null);
          }}
          game={selectedGame}
        />
      )}
    </div>
  );
};
