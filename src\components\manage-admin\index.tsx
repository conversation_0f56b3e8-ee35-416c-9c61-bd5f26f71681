// 'use client';

// import { useState } from 'react';
// import { Users } from 'lucide-react';
// import AdminTable from './components/data-table';
// import { Badge } from '@/components/ui/badge';
// import { hasPermission, permissions } from '@/lib/types/permissions';

// export default function AdminPage() {
//   const [open, setOpen] = useState(false);
//   const permitCreate = hasPermission(permissions.admin_create);

//   return (
//     <>
//       <h2 className="text-lg font-bold text-gray-900 dark:text-white mb-4 text-left flex items-center gap-2">
//         <Users className="w-5 h-5 text-zinc-900 dark:text-zinc-50" />
//         Admin Management
//       </h2>
//       <div className="flex gap-2">
//         {permitCreate && (
//           <Badge className="h-7 cursor-pointer" onClick={() => setOpen(true)}>
//             Add Admin
//           </Badge>
//         )}
//       </div>
//       <div className="mt-4 bg-white dark:bg-[#0F0F12] rounded-xl sm:p-4 flex flex-col items-start justify-start sm:border border-gray-200 dark:border-[#1F1F23]">
//         <AdminTable openCreate={open} setOpenCreate={setOpen} />
//       </div>
//     </>
//   );
// }
