import React, { useState, useEffect } from 'react';
import { InputField } from '@/components/common/form';
import { Form } from '@/components/ui/form';
import { Modal } from '@/components/common/modal';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { myApi } from '@/api/fetcher';
import { toast } from 'sonner';
import {
  departmentFormSchema,
  DepartmentFormValues,
} from '@/components/validations/crm';
import { GetProfile } from '@/api/staff';
import { GetDoctors } from '@/api/crm/data';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { ModalProps } from '@/components/crm/types';
import { Switch } from '@/components/ui/switch';

const Create: React.FC<ModalProps> = ({ setOpen, open, mutate }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [doctors, setDoctors] = useState<any[]>([]);
  const [doctorsLoading, setDoctorsLoading] = useState(false);
  const { profile } = GetProfile();

  const form = useForm<DepartmentFormValues>({
    resolver: zodResolver(departmentFormSchema),
    defaultValues: {
      name: '',
      description: '',
      headOfDepartmentId: '',
      isActive: true,
      location: '',
      contactEmail: '',
      contactPhone: '',
    },
  });

  // Fetch doctors for dropdown
  useEffect(() => {
    const fetchDoctors = async () => {
      if (open) {
        try {
          setDoctorsLoading(true);
          const response = await myApi.get('/crm/doctors');
          if (response.status === 200) {
            setDoctors(response.data.data.doctors);
          }
        } catch (error) {
          console.error('Error fetching doctors:', error);
        } finally {
          setDoctorsLoading(false);
        }
      }
    };

    fetchDoctors();
  }, [open]);

  const onSubmit = async (data: DepartmentFormValues) => {
    try {
      setIsLoading(true);
      const res = await myApi.post('/crm/departments', {
        ...data,
        createdBy: profile.data.fullName,
      });
      setIsLoading(false);
      if (res.status === 200) {
        toast.success('Department added successfully');
        if (mutate) {
          mutate();
        }
        setOpen(false);
        form.reset();
      }
    } catch (error: any) {
      setIsLoading(false);
      toast.error(error?.response?.data?.message || 'An error occurred');
    }
  };

  return (
    <Modal
      title="Add New Department"
      open={open}
      setOpen={setOpen}
      isLoading={isLoading}
      description="Add a new department to the hospital"
      onSubmit={form.handleSubmit(onSubmit)}
    >
      <Form {...form}>
        <form className="space-y-4">
          <InputField
            control={form.control}
            name="name"
            label="Department Name"
            placeholder="Cardiology"
          />

          <div>
            <label className="text-sm font-medium">Description</label>
            <Textarea
              placeholder="Brief description about the department"
              className="resize-none"
              {...form.register('description')}
            />
            {form.formState.errors.description && (
              <p className="text-xs text-red-500 mt-1">
                {form.formState.errors.description.message}
              </p>
            )}
          </div>

          <div>
            <label className="text-sm font-medium">Head of Department</label>
            <Select
              onValueChange={(value) =>
                form.setValue('headOfDepartmentId', value)
              }
              disabled={doctorsLoading}
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Select head of department" />
              </SelectTrigger>
              <SelectContent>
                {doctors.map((doctor) => (
                  <SelectItem key={doctor.id} value={doctor.id.toString()}>
                    Dr. {doctor.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {form.formState.errors.headOfDepartmentId && (
              <p className="text-xs text-red-500 mt-1">
                {form.formState.errors.headOfDepartmentId.message}
              </p>
            )}
          </div>

          <InputField
            control={form.control}
            name="location"
            label="Location"
            placeholder="Building A, Floor 2"
          />

          <div className="grid grid-cols-2 gap-4">
            <InputField
              control={form.control}
              name="contactEmail"
              label="Contact Email"
              placeholder="<EMAIL>"
              type="email"
            />

            <InputField
              control={form.control}
              name="contactPhone"
              label="Contact Phone"
              placeholder="+234 ************"
            />
          </div>

          <div className="flex items-center space-x-2">
            <Switch
              id="active-status"
              checked={form.getValues('isActive')}
              onCheckedChange={(checked) => form.setValue('isActive', checked)}
            />
            <label htmlFor="active-status" className="text-sm font-medium">
              Department is Active
            </label>
          </div>
        </form>
      </Form>
    </Modal>
  );
};

export default Create;
