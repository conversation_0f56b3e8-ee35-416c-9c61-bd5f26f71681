import React, { useState } from 'react';
import { InputField } from '@/components/common/form';
import { Form } from '@/components/ui/form';
import { Modal } from '@/components/common/modal';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import {
  InventoryCatFormSchema,
  Category,
} from '@/components/validations/inventory';
import { myApi } from '@/api/fetcher';
import { toast } from 'sonner';
import { ModalProps } from '../../../types';

const CreateInventoryCat: React.FC<ModalProps> = ({
  setOpen,
  mutate,
  open,
  profile,
}) => {
  const [isLoading, setIsLoading] = useState(false);

  const form = useForm<Category>({
    resolver: zodResolver(InventoryCatFormSchema),
    defaultValues: {
      name: '',
    },
  });

  const onSubmit = async (data: Category) => {
    try {
      setIsLoading(true);
      const res = await myApi.post('/cafeteria/inventory/add-category', {
        name: data.name,
        createdBy: profile?.fullName,
      });
      setIsLoading(false);
      if (res.status === 200) {
        toast.success(res.data.data.message);
        setOpen(false);
        form.reset();
      }
    } catch (error) {
      setIsLoading(false);
    }
  };

  return (
    <Modal
      open={open}
      setOpen={setOpen}
      title="Add Inventory Category"
      description="Enter an inventory category name. E.g Ingredients"
      isLoading={isLoading}
      onSubmit={form.handleSubmit(onSubmit)}
    >
      <Form {...form}>
        <form className="space-y-8">
          <InputField
            control={form.control}
            name="name"
            label="Category Name"
            placeholder="E.g Ingredients"
            type="text"
          />
        </form>
      </Form>
    </Modal>
  );
};

export default CreateInventoryCat;
