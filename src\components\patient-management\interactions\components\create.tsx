import React, { useState, useEffect } from 'react';
import { InputField } from '@/components/common/form';
import { Form } from '@/components/ui/form';
import { Modal } from '@/components/common/modal';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { myApi } from '@/api/fetcher';
import { toast } from 'sonner';
import {
  patientInteractionFormSchema,
  PatientInteractionFormValues,
} from '@/components/validations/crm';
import { GetProfile } from '@/api/staff';
import { GetPatients } from '@/api/crm/data';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { ModalProps } from '@/components/crm/types';
import { useSearchParams } from 'next/navigation';

const Create: React.FC<ModalProps> = ({ setOpen, open, mutate }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [patients, setPatients] = useState<any[]>([]);
  const [patientsLoading, setPatientsLoading] = useState(false);
  const { profile } = GetProfile();
  const searchParams = useSearchParams();
  const patientIdFromUrl = searchParams.get('patientId');

  const form = useForm<PatientInteractionFormValues>({
    resolver: zodResolver(patientInteractionFormSchema),
    defaultValues: {
      patientId: patientIdFromUrl ? parseInt(patientIdFromUrl) : 0,
      channel: 'whatsapp',
      subject: '',
      content: '',
      status: 'pending',
      direction: 'inbound',
      contactInfo: '',
      tags: '',
      followUpRequired: false,
      followUpDate: '',
    },
  });

  // Fetch patients for dropdown
  useEffect(() => {
    const fetchPatients = async () => {
      if (open) {
        try {
          setPatientsLoading(true);
          const response = await myApi.get('/crm/patients?limit=100');
          if (response.status === 200) {
            setPatients(response.data.data.patients);
          }
        } catch (error) {
          console.error('Error fetching patients:', error);
        } finally {
          setPatientsLoading(false);
        }
      }
    };

    fetchPatients();
  }, [open]);

  const onSubmit = async (data: PatientInteractionFormValues) => {
    try {
      setIsLoading(true);
      const res = await myApi.post('/crm/patient-interactions', {
        ...data,
        createdBy: profile.data.fullName,
      });
      setIsLoading(false);
      if (res.status === 200) {
        toast.success('Interaction recorded successfully');
        if (mutate) {
          mutate();
        }
        setOpen(false);
        form.reset();
      }
    } catch (error: any) {
      setIsLoading(false);
      toast.error(error?.response?.data?.message || 'An error occurred');
    }
  };

  return (
    <Modal
      title="Record Patient Interaction"
      open={open}
      setOpen={setOpen}
      isLoading={isLoading}
      description="Record a new interaction with a patient"
      onSubmit={form.handleSubmit(onSubmit)}
    >
      <Form {...form}>
        <form className="space-y-4">
          <div>
            <label className="text-sm font-medium">Patient</label>
            <Select
              onValueChange={(value) =>
                form.setValue('patientId', parseInt(value))
              }
              disabled={patientsLoading || !!patientIdFromUrl}
              defaultValue={patientIdFromUrl || undefined}
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Select a patient" />
              </SelectTrigger>
              <SelectContent>
                {patients.map((patient) => (
                  <SelectItem key={patient.id} value={patient.id.toString()}>
                    {patient.firstName} {patient.lastName}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {form.formState.errors.patientId && (
              <p className="text-xs text-red-500 mt-1">
                {form.formState.errors.patientId.message}
              </p>
            )}
          </div>

          <div>
            <label className="text-sm font-medium">Communication Channel</label>
            <Select
              onValueChange={(value) => form.setValue('channel', value as any)}
              defaultValue={form.getValues('channel')}
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Select channel" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="whatsapp">WhatsApp</SelectItem>
                <SelectItem value="email">Email</SelectItem>
                <SelectItem value="sms">SMS</SelectItem>
                <SelectItem value="phone">Phone Call</SelectItem>
                <SelectItem value="video">Video Call</SelectItem>
                <SelectItem value="instagram">Instagram</SelectItem>
                <SelectItem value="twitter">Twitter</SelectItem>
                <SelectItem value="facebook">Facebook</SelectItem>
                <SelectItem value="other">Other</SelectItem>
              </SelectContent>
            </Select>
            {form.formState.errors.channel && (
              <p className="text-xs text-red-500 mt-1">
                {form.formState.errors.channel.message}
              </p>
            )}
          </div>

          <div>
            <label className="text-sm font-medium">Direction</label>
            <Select
              onValueChange={(value) =>
                form.setValue('direction', value as any)
              }
              defaultValue={form.getValues('direction')}
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Select direction" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="inbound">
                  Inbound (Patient to Hospital)
                </SelectItem>
                <SelectItem value="outbound">
                  Outbound (Hospital to Patient)
                </SelectItem>
              </SelectContent>
            </Select>
            {form.formState.errors.direction && (
              <p className="text-xs text-red-500 mt-1">
                {form.formState.errors.direction.message}
              </p>
            )}
          </div>

          <InputField
            control={form.control}
            name="subject"
            label="Subject"
            placeholder="Brief subject of the interaction"
          />

          <InputField
            control={form.control}
            name="contactInfo"
            label="Contact Information"
            placeholder="Phone number, email, or social media handle used"
          />

          <div>
            <label className="text-sm font-medium">Content</label>
            <Textarea
              placeholder="Details of the interaction"
              className="resize-none"
              {...form.register('content')}
            />
            {form.formState.errors.content && (
              <p className="text-xs text-red-500 mt-1">
                {form.formState.errors.content.message}
              </p>
            )}
          </div>

          <InputField
            control={form.control}
            name="tags"
            label="Tags"
            placeholder="Comma-separated tags (e.g., inquiry, complaint, feedback)"
          />

          <div>
            <label className="text-sm font-medium">Status</label>
            <Select
              onValueChange={(value) => form.setValue('status', value as any)}
              defaultValue={form.getValues('status')}
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Select status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
              </SelectContent>
            </Select>
            {form.formState.errors.status && (
              <p className="text-xs text-red-500 mt-1">
                {form.formState.errors.status.message}
              </p>
            )}
          </div>

          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="followUpRequired"
              className="rounded border-gray-300"
              {...form.register('followUpRequired')}
            />
            <label htmlFor="followUpRequired" className="text-sm font-medium">
              Follow-up Required
            </label>
          </div>

          {form.watch('followUpRequired') && (
            <div>
              <label className="text-sm font-medium">Follow-up Date</label>
              <input
                type="datetime-local"
                className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                {...form.register('followUpDate')}
              />
              {form.formState.errors.followUpDate && (
                <p className="text-xs text-red-500 mt-1">
                  {form.formState.errors.followUpDate.message}
                </p>
              )}
            </div>
          )}
        </form>
      </Form>
    </Modal>
  );
};

export default Create;
