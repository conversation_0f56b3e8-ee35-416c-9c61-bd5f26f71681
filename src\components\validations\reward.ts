import { z } from 'zod';

export const StaffRegFormSchema = z.object({
  email: z.string().email({
    message: 'Please enter a valid email address.',
  }),
  name: z.string().min(6, {
    message: 'Enter staff email.',
  }),
  role: z.string().min(6, {
    message: 'Enter staff role.',
  }),
  type: z.string({
    required_error: 'Please select a type',
  }),
  location: z.string({
    required_error: 'Please select a location',
  }),
  department: z.string({
    required_error: 'Please select a department',
  }),
  staffId: z.string().min(4, {
    message: 'Enter staff ID.',
  }),
  specialty: z.string().optional(),
  phone: z.string().min(11, {
    message: 'Enter a valid phone number.',
  }),
});

export type StaffFormValues = z.infer<typeof StaffRegFormSchema>;

export const RewardFormSchema = z.object({
  description: z.string().min(10, {
    message: 'Description must be at least 10 characters.',
  }),
  value: z.string().refine((val) => !isNaN(Number(val)) && Number(val) > 0, {
    message: 'Value must be a positive number.',
  }),
  type: z.string({
    required_error: 'Please select a reward type',
  }),
  name: z.string({
    required_error: 'Please select a reward name',
  }),
});

export type RewardFormValues = z.infer<typeof RewardFormSchema>;
