import React, { useState } from 'react';
import { Modal } from '@/components/common/modal';
import { ModalProps } from '@/components/types';
import dayjs from 'dayjs';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger, TabsContent } from '@/components/ui/tabs';
import { FileIcon, ExternalLink, CheckCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { myApi } from '@/api/fetcher';
import { toast } from 'sonner';
import { hasPermission, PERMISSIONS } from '@/lib/types/permissions';

const Details: React.FC<ModalProps> = ({ open, setOpen, mutate, data }) => {
  const [activeTab, setActiveTab] = useState('details');
  const [isActivating, setIsActivating] = useState(false);
  const hasEditPermission = hasPermission(PERMISSIONS.REFERRAL_EDIT);

  if (!data) return null;

  const handleActivateAccount = async () => {
    try {
      setIsActivating(true);
      const res = await myApi.post('/referral/confirm-referrer', {
        email: data.email,
      });

      if (res.status === 200) {
        toast.success('Account activated successfully');
        if (mutate) mutate();
      }
    } catch (error) {
      console.error('Error activating account:', error);
      toast.error('Failed to activate account');
    } finally {
      setIsActivating(false);
    }
  };

  return (
    <Modal
      open={open}
      setOpen={setOpen}
      title="Referring Entity Details"
      description={`Details for ${data.name}`}
      size="lg"
    >
      <Tabs
        defaultValue="details"
        value={activeTab}
        onValueChange={setActiveTab}
        className="w-full"
      >
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="details">Details</TabsTrigger>
          <TabsTrigger value="credentials">
            {data.entityType === 'ORGANIZATION'
              ? 'Admin Contact'
              : 'Credentials'}
          </TabsTrigger>
        </TabsList>

        <TabsContent value="details" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h3 className="text-sm font-medium">Entity Type</h3>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {data.entityType === 'ORGANIZATION'
                  ? 'Organization'
                  : 'Individual'}
              </p>
            </div>
            <div>
              <h3 className="text-sm font-medium">Name</h3>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {data.name || 'Not provided'}
              </p>
            </div>
            <div>
              <h3 className="text-sm font-medium">Email</h3>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {data.email || 'Not provided'}
              </p>
            </div>
            <div>
              <h3 className="text-sm font-medium">Phone Number</h3>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {data.phoneNumber || 'Not provided'}
              </p>
            </div>
            <div>
              <h3 className="text-sm font-medium">Email Validation</h3>
              <p
                className={`text-sm ${data.isEmailValidated ? 'text-green-600' : 'text-gray-500'}`}
              >
                {data.isEmailValidated ? 'Validated' : 'Not Validated'}
              </p>
            </div>
            <div>
              <h3 className="text-sm font-medium">Account Status</h3>
              <div className="flex items-center gap-2">
                <p
                  className={`text-sm ${data.activated ? 'text-green-600' : 'text-gray-500'}`}
                >
                  {data.activated ? 'Activated' : 'Not Activated'}
                </p>
                {!data.activated && hasEditPermission && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleActivateAccount}
                    disabled={isActivating}
                    className="ml-2 text-xs h-7 px-2"
                  >
                    {isActivating ? (
                      'Activating...'
                    ) : (
                      <>
                        <CheckCircle className="mr-1 h-3 w-3" />
                        Activate
                      </>
                    )}
                  </Button>
                )}
              </div>
            </div>
            <div>
              <h3 className="text-sm font-medium">Active Status</h3>
              <p
                className={`text-sm ${data.isActive ? 'text-green-600' : 'text-gray-500'}`}
              >
                {data.isActive ? 'Active' : 'Inactive'}
              </p>
            </div>
            <div>
              <h3 className="text-sm font-medium">Last Login</h3>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {data.lastLogin
                  ? dayjs(data.lastLogin).format('MMMM D, YYYY h:mm A')
                  : 'Never logged in'}
              </p>
            </div>
            <div>
              <h3 className="text-sm font-medium">Created At</h3>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {data.createdAt
                  ? dayjs(data.createdAt).format('MMMM D, YYYY h:mm A')
                  : 'Not available'}
              </p>
            </div>
            <div>
              <h3 className="text-sm font-medium">Updated At</h3>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {data.updatedAt
                  ? dayjs(data.updatedAt).format('MMMM D, YYYY h:mm A')
                  : 'Not available'}
              </p>
            </div>
          </div>

          {data.address && (
            <div>
              <h3 className="text-sm font-medium mb-2">Address</h3>
              <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-md">
                <p className="text-sm whitespace-pre-wrap">{data.address}</p>
              </div>
            </div>
          )}

          {data.notes && (
            <div>
              <h3 className="text-sm font-medium mb-2">Notes</h3>
              <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-md">
                <p className="text-sm whitespace-pre-wrap">{data.notes}</p>
              </div>
            </div>
          )}
        </TabsContent>

        <TabsContent value="credentials" className="space-y-4">
          {data.entityType === 'ORGANIZATION' ? (
            <div className="space-y-6">
              <h3 className="text-sm font-medium">
                Organization Admin Contact
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h3 className="text-sm font-medium">Primary Contact Name</h3>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    {data.primaryContactName || 'Not provided'}
                  </p>
                </div>
                <div>
                  <h3 className="text-sm font-medium">Primary Contact Email</h3>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    {data.primaryContactEmail || 'Not provided'}
                  </p>
                </div>
                <div>
                  <h3 className="text-sm font-medium">Primary Contact Phone</h3>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    {data.primaryContactPhone || 'Not provided'}
                  </p>
                </div>
                <div>
                  <h3 className="text-sm font-medium">Position/Role</h3>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    {data.primaryContactRole || 'Not provided'}
                  </p>
                </div>
                {data.organizationWebsite && (
                  <div>
                    <h3 className="text-sm font-medium">
                      Organization Website
                    </h3>
                    <p className="text-sm text-primary hover:underline">
                      <a
                        href={data.organizationWebsite}
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        {data.organizationWebsite}
                      </a>
                    </p>
                  </div>
                )}
              </div>
            </div>
          ) : data.submittedCredentials &&
            data.submittedCredentials.length > 0 ? (
            <div className="space-y-4">
              <h3 className="text-sm font-medium">Submitted Credentials</h3>
              <div className="grid grid-cols-1 gap-3">
                {data.submittedCredentials.map(
                  (credential: any, index: number) => (
                    <div
                      key={index}
                      className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-md"
                    >
                      <div className="flex items-center gap-3">
                        <FileIcon className="w-5 h-5 text-primary" />
                        <div>
                          <p className="text-sm font-medium">
                            {credential.fileName || `File ${index + 1}`}
                          </p>
                          <p className="text-xs text-gray-500">
                            {credential.fileType || 'Unknown type'} •
                            {credential.uploadedAt
                              ? dayjs(credential.uploadedAt).format(
                                  'MMM D, YYYY'
                                )
                              : 'Unknown date'}
                          </p>
                        </div>
                      </div>
                      {credential.fileUrl && (
                        <a
                          href={credential.fileUrl}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="flex items-center text-xs text-primary hover:underline"
                        >
                          View <ExternalLink className="ml-1 w-3 h-3" />
                        </a>
                      )}
                    </div>
                  )
                )}
              </div>
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center py-8 text-center">
              <FileIcon className="w-12 h-12 text-gray-300 mb-2" />
              <h3 className="text-lg font-medium text-gray-700 dark:text-gray-300">
                No Credentials
              </h3>
              <p className="text-sm text-gray-500 max-w-sm">
                This referring entity has not submitted any credential documents
                yet.
              </p>
            </div>
          )}
        </TabsContent>
      </Tabs>
    </Modal>
  );
};

export default Details;
