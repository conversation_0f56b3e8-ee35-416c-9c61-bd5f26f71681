import React from 'react';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Plus } from 'lucide-react';
import { LocationPriceForm } from '@/lib/types/types';

// Define types for the component props
interface Location {
  id: string;
  name: string;
}

interface PriceProps {
  locations: Location[];
  newLocation: {
    location: {
      id: string;
      name: string;
    };
    amount: string;
    currency: string;
    endDate: Date;
  };

  setNewLocation: (location: LocationPriceForm) => void;
  onAddLocationPrice: () => void;
  isEditing?: boolean;
  isEditMode?: boolean;
  onCancelEdit?: () => void;
}

const Price: React.FC<PriceProps> = ({
  locations,
  newLocation,
  setNewLocation,
  onAddLocationPrice,
  isEditing = false,
  onCancelEdit,
}) => {
  return (
    <div className="space-y-4">
      <h3 className="text-lg font-medium">Package Location Pricing</h3>
      <div className="space-y-4">
        {/* Select Location */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="location">Select Location</Label>
            <div className="h-10">
              <Select
                value={newLocation.location.name} // Set the value to location name
                onValueChange={(value) => {
                  // Find the location by the name value
                  const selectedLocation = locations.find(
                    (location) => location.name === value
                  );

                  if (selectedLocation) {
                    setNewLocation({
                      ...newLocation,
                      location: {
                        name: selectedLocation.name, // Set the name from the selected location
                        id: selectedLocation.id, // Set the id from the selected location
                      },
                    });
                  }
                }}
              >
                <SelectTrigger id="location" className="w-full h-10">
                  <SelectValue placeholder="Select a location" />
                </SelectTrigger>
                <SelectContent>
                  {locations.map((location) => (
                    <SelectItem key={location.id} value={location.name}>
                      {location.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
          <div className="space-y-2">
            <Label>End Date</Label>
            <div>
              <Input
                type="date"
                value={newLocation.endDate.toISOString().split('T')[0]}
                onChange={(e) => {
                  const selectedDate = new Date(e.target.value);
                  const today = new Date();
                  today.setHours(0, 0, 0, 0);

                  if (isEditing) {
                    setNewLocation({
                      ...newLocation,
                      endDate: selectedDate,
                    });
                  } else {
                    if (selectedDate <= today) {
                      const tomorrow = new Date(today);
                      tomorrow.setDate(tomorrow.getDate() + 2);
                      setNewLocation({
                        ...newLocation,
                        endDate: tomorrow,
                      });
                    } else {
                      setNewLocation({
                        ...newLocation,
                        endDate: selectedDate,
                      });
                    }
                  }
                }}
                min={
                  isEditing
                    ? undefined
                    : new Date(new Date().getTime() + 86400000)
                        .toISOString()
                        .split('T')[0]
                } // Only set min to tomorrow if not editing
              />
              <p className="text-xs text-muted-foreground mt-1">
                {isEditing
                  ? 'Past dates are allowed when editing existing location prices'
                  : 'End date must be a future date for new location prices'}
              </p>
            </div>
          </div>
        </div>

        {/* Price Input */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label>Price</Label>
            <Input
              type="text"
              placeholder="Enter amount"
              value={
                newLocation.amount
                  ? new Intl.NumberFormat('en-US').format(
                      parseInt(newLocation.amount)
                    )
                  : ''
              }
              onChange={(e) => {
                // Remove all non-numeric characters
                const value = e.target.value.replace(/[^\d]/g, '');

                setNewLocation({
                  ...newLocation,
                  amount: value,
                });
              }}
            />
          </div>

          {/* Currency Input */}
          <div className="space-y-2">
            <Label>Currency</Label>
            <Input
              placeholder="NGN"
              value={newLocation.currency}
              onChange={(e) =>
                setNewLocation({
                  ...newLocation,
                  currency: e.target.value,
                })
              }
            />
          </div>
        </div>

        {/* Add/Update Location Price Button */}
        <div className="flex gap-2">
          <Button
            type="button"
            onClick={onAddLocationPrice}
            disabled={
              !newLocation.location.name ||
              !/^\d*\.?\d+$/.test(newLocation.amount) ||
              !newLocation.currency
            }
          >
            {isEditing ? (
              <>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="mr-2"
                >
                  <path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"></path>
                  <polyline points="17 21 17 13 7 13 7 21"></polyline>
                  <polyline points="7 3 7 8 15 8"></polyline>
                </svg>
                Update Price
              </>
            ) : (
              <>
                <Plus className="mr-2 h-4 w-4" /> Add Price
              </>
            )}
          </Button>

          {isEditing && onCancelEdit && (
            <Button type="button" variant="outline" onClick={onCancelEdit}>
              Cancel
            </Button>
          )}
        </div>
      </div>
    </div>
  );
};

export default Price;
