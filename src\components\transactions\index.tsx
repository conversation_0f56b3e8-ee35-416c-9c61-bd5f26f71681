'use client';

import React, { useState } from 'react';
import { Wallet } from 'lucide-react';
import { hasPermission, PERMISSIONS } from '@/lib/types/permissions';
import TransactionData from './components/data-table';

export default function Transaction() {
  const [open, setOpen] = useState(false);
  const permitEdit = hasPermission(PERMISSIONS.PACKAGE_EDIT);

  return (
    <div className="space-y-4">
      <h2 className="text-lg font-bold text-gray-900 dark:text-white mb-4 text-left flex items-center gap-2">
        <Wallet className="w-5 h-5 text-zinc-900 dark:text-zinc-50" />
        Manage Transactions
      </h2>
      <div className="mt-4 dark:bg-[#0F0F12] rounded-xl p-4 flex flex-col items-start justify-start border border-gray-200 dark:border-[#1F1F23]">
        <TransactionData />
      </div>
    </div>
  );
}
