import useSWR from 'swr';

// Booking Analytics
export const GetEnhancedBookingAnalytics = (params?: string) => {
  const qs = params ? new URLSearchParams(params) : new URLSearchParams();

  const { data, error, isLoading } = useSWR(
    `/analytic/dashboard?${qs.toString()}`
  );

  return {
    bookingStats: data,
    bookingStatsLoading: isLoading,
    bookingStatsError: error,
  };
};

// Feedback Analytics
export const GetFeedbackAnalytics = (params?: string) => {
  const qs = params ? new URLSearchParams(params) : new URLSearchParams();

  const { data, error, isLoading } = useSWR(
    `/analytic/feedback/dashboard?${qs.toString()}`
  );

  return {
    feedbackStats: data,
    feedbackStatsLoading: isLoading,
    feedbackStatsError: error,
  };
};

// Transaction Analytics
export const GetTransactionAnalytics = (params?: string) => {
  const qs = params ? new URLSearchParams(params) : new URLSearchParams();

  const { data, error, isLoading } = useSWR(
    `/analytic/transactions?${qs.toString()}`
  );

  return {
    transactionStats: data,
    transactionStatsLoading: isLoading,
    transactionStatsError: error,
  };
};

// Discount Analytics
export const GetDiscountAnalytics = (params?: string) => {
  const qs = params ? new URLSearchParams(params) : new URLSearchParams();

  const { data, error, isLoading } = useSWR(
    `/analytic/discounts?${qs.toString()}`
  );

  return {
    discountStats: data,
    discountStatsLoading: isLoading,
    discountStatsError: error,
  };
};

// Staff Referral Analytics
export const GetStaffReferralAnalytics = (params?: string) => {
  const qs = params ? new URLSearchParams(params) : new URLSearchParams();

  const { data, error, isLoading } = useSWR(
    `/analytic/staff-referral?${qs.toString()}`
  );

  return {
    referralStats: data,
    referralStatsLoading: isLoading,
    referralStatsError: error,
  };
};
