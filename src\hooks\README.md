# Search and Pagination Hooks

This directory contains reusable hooks for handling search and pagination functionality across the application.

## useSearchAndPagination

A simple hook that provides debounced search and pagination functionality.

### Usage

```tsx
import { useSearchAndPagination } from '@/hooks/useSearchAndPagination';

function MyComponent() {
  const {
    searchTerm,
    handleSearchChange,
    currentPage,
    pageSize,
    handlePageChange,
    queryParam,
  } = useSearchAndPagination({
    initialPageSize: 15,
    debounceDelay: 500,
  });

  // Use in your API call
  const { data, loading } = useAPI(
    `?page=${currentPage}&limit=${pageSize}${queryParam ? `&${queryParam}` : ''}`
  );

  return (
    <div>
      <Input
        value={searchTerm}
        onChange={handleSearchChange}
        placeholder="Search..."
      />
      {/* Your table/list component */}
      <Pagination
        currentPage={currentPage}
        onPageChange={handlePageChange}
        totalPages={data?.totalPages}
      />
    </div>
  );
}
```

## useAdvancedSearchAndPagination

An advanced hook that provides debounced search, pagination, and support for additional filters.

### Usage

```tsx
import { useAdvancedSearchAndPagination } from '@/hooks/useAdvancedSearchAndPagination';

function MyAdvancedComponent() {
  const {
    searchTerm,
    handleSearchChange,
    currentPage,
    pageSize,
    handlePageChange,
    queryParam,
    buildQueryParam,
  } = useAdvancedSearchAndPagination({ initialPageSize: 10 });

  const [statusFilter, setStatusFilter] = useState('');
  const [dateRange, setDateRange] = useState({ start: '', end: '' });

  // Update query when filters change
  useEffect(() => {
    buildQueryParam({
      status: statusFilter,
      startDate: dateRange.start,
      endDate: dateRange.end,
    });
  }, [statusFilter, dateRange, buildQueryParam]);

  return (
    <div>
      <Input
        value={searchTerm}
        onChange={handleSearchChange}
        placeholder="Search..."
      />
      <Select value={statusFilter} onValueChange={setStatusFilter}>
        <SelectItem value="all">All Status</SelectItem>
        <SelectItem value="active">Active</SelectItem>
        <SelectItem value="inactive">Inactive</SelectItem>
      </Select>
      {/* Your table/list component */}
    </div>
  );
}
```

## Features

- **Debounced Search**: Prevents excessive API calls while typing
- **Automatic Pagination Reset**: Resets to page 1 when search term changes
- **Flexible Query Building**: Easy to extend with additional filters
- **TypeScript Support**: Full type safety
- **Minimal Code**: Reduces boilerplate in components

## Migration

To migrate existing components:

1. Import the hook: `import { useSearchAndPagination } from '@/hooks/useSearchAndPagination';`
2. Replace existing state variables with the hook
3. Remove manual debounce logic and search handlers
4. Update your component to use the returned values

### Before

```tsx
const [searchTerm, setSearchTerm] = useState('');
const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('');
const [currentPage, setCurrentPage] = useState(1);
const [pageSize] = useState(15);

useEffect(() => {
  const timer = setTimeout(() => {
    setDebouncedSearchTerm(searchTerm);
  }, 500);
  return () => clearTimeout(timer);
}, [searchTerm]);

const handleSearchChange = (e) => setSearchTerm(e.target.value);
const handlePageChange = (page) => setCurrentPage(page);
```

### After

```tsx
const {
  searchTerm,
  handleSearchChange,
  currentPage,
  pageSize,
  handlePageChange,
  queryParam,
} = useSearchAndPagination({ initialPageSize: 15 });
```
