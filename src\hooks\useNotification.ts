'use client';

import { useCallback } from 'react';
import { sendNotification, isNotificationSupported } from '@/lib/notification';

export function useNotification() {
  const isSupported = isNotificationSupported();
  const permissionStatus = isSupported ? Notification.permission : 'denied';

  const notify = useCallback(
    (title: string, options: NotificationOptions = {}) => {
      return sendNotification(title, options);
    },
    []
  );

  return {
    isSupported,
    permissionStatus,
    notify,
  };
}
