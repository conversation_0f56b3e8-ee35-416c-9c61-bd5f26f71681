# VMI (Vendor Managed Inventory) Feature

## Overview

The VMI feature provides comprehensive vendor managed inventory functionality for pharmaceutical store-to-store operations.

## Features

### 1. Overview Dashboard (`/vmi/overview`)

- Real-time statistics and KPIs
- Total products, low stock alerts, pending requests
- Monthly value tracking and outstanding invoices

### 2. Product Catalogue (`/vmi/products`)

- Complete product management system
- Add, edit, and search pharmaceutical products
- Track manufacturer, batch numbers, and expiry dates
- Category-based organization

### 3. Inventory Tracking (`/vmi/inventory`)

- Dual-store inventory management (Hospital Store + VMI Store)
- Real-time stock level monitoring
- Automated reorder level alerts
- Stock status indicators (Low/Normal/High)

### 4. Request Management (`/vmi/requests`)

- Create and manage inventory requests
- Approval workflow system
- Request status tracking (Pending/Approved/Rejected/Fulfilled)
- Request history and audit trail

### 5. Invoices & Receipts (`/vmi/invoices`)

- Generate and manage invoices
- PDF download functionality for invoices and receipts
- Payment status tracking
- Outstanding invoice monitoring

## Technical Implementation

### File Structure

```
src/
├── app/(authenticated)/vmi/
│   ├── overview/page.tsx
│   ├── products/page.tsx
│   ├── inventory/page.tsx
│   ├── requests/page.tsx
│   ├── invoices/page.tsx
│   └── page.tsx
├── components/vmi/
│   ├── layout.tsx
│   └── README.md
├── api/vmi/
│   └── index.ts
└── lib/
    ├── types/vmi.ts
    └── vmi-utils.ts
```

### Key Dependencies

- jsPDF & jspdf-autotable for PDF generation
- Lucide React for icons
- Radix UI components for UI elements
- SWR for data fetching (if integrated)

### Data Models

- **Product**: Core pharmaceutical product information
- **InventoryItem**: Stock levels across both stores
- **Request**: Inventory requests with approval workflow
- **Invoice**: Billing and payment tracking
- **VMIStats**: Dashboard statistics

## Usage

### Navigation

The VMI feature is accessible through the main sidebar under "VMI (VENDOR MANAGED INVENTORY)" section.

### Permissions

Currently implemented without role-based restrictions. Can be extended to include:

- Pharmacy staff: View inventory, create requests
- Pharmacy managers: Approve requests, manage products
- Finance team: Handle invoices and payments

### PDF Downloads

- Invoice PDFs include complete itemization and totals
- Receipt PDFs generated for paid invoices
- Automatic filename generation with invoice numbers

## Future Enhancements

- Real-time notifications for low stock
- Automated reordering based on consumption patterns
- Integration with external pharmacy systems
- Advanced reporting and analytics
- Mobile-responsive design improvements
