'use client';

import { useState, useEffect } from 'react';
import { GetProfile } from '@/api/staff';

/**
 * Hook to verify if the user profile is loaded with proper roles and permissions
 * @param timeoutMs Optional timeout in milliseconds (default: 15000ms)
 * @returns An object with loading status, success status, and error information
 */
export function useProfileVerification(timeoutMs: number = 15000) {
  const { profile, isLoading, error } = GetProfile();
  const [hasTimedOut, setHasTimedOut] = useState(false);
  const [verificationState, setVerificationState] = useState({
    isLoading: true,
    isSuccess: false,
    isError: false,
    isTimeout: false,
    error: null as Error | null | unknown,
  });

  const permissions =
    profile?.data?.roles?.flatMap(
      (role: any) => role.permissions?.map((p: any) => p.action) || []
    ) || [];

  useEffect(() => {
    let timer: NodeJS.Timeout;

    if (isLoading) {
      timer = setTimeout(() => {
        setHasTimedOut(true);
      }, timeoutMs);
    }

    return () => {
      if (timer) clearTimeout(timer);
    };
  }, [isLoading, timeoutMs]);

  // useEffect(() => {
  //   console.log('useProfileVerification running', {
  //     profile,
  //     isLoading,
  //     error,
  //     hasTimedOut,
  //   });
  // }, [profile, isLoading, error, hasTimedOut]);

  useEffect(() => {
    if (hasTimedOut && isLoading) {
      setVerificationState({
        isLoading: false,
        isSuccess: false,
        isError: true,
        isTimeout: true,
        error: new Error(`Profile verification timed out after ${timeoutMs}ms`),
      });
      return;
    }

    if (isLoading && !hasTimedOut) {
      setVerificationState({
        isLoading: true,
        isSuccess: false,
        isError: false,
        isTimeout: false,
        error: null,
      });
      return;
    }

    if (error) {
      setVerificationState({
        isLoading: false,
        isSuccess: false,
        isError: true,
        isTimeout: false,
        error,
      });
      return;
    }

    // Profile is null (definitive negative result)
    if (profile === null) {
      setVerificationState({
        isLoading: false,
        isSuccess: false,
        isError: true,
        isTimeout: false,
        error: new Error('Profile is null'),
      });
      return;
    }

    // Profile exists but missing roles
    const roles =
      profile?.data?.roles || (profile?.data?.role ? [profile.data.role] : []);

    //  const permissions = profile?.data?.permissions || [];

    if (profile && (!profile.data || !roles.length)) {
      setVerificationState({
        isLoading: false,
        isSuccess: false,
        isError: true,
        isTimeout: false,
        error: new Error('Profile exists but roles are missing'),
      });
      return;
    }

    // Success case - profile exists with roles
    setVerificationState({
      isLoading: false,
      isSuccess: true,
      isError: false,
      isTimeout: false,
      error: null,
    });
  }, [profile, isLoading, error, hasTimedOut, timeoutMs]);

  return { ...verificationState, profile, permissions };
}
