import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Checkbox } from '@/components/ui/checkbox';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Calendar } from '@/components/ui/calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  ChevronLeft,
  ChevronRight,
  CalendarIcon,
  CheckCircle,
  Star,
  Save,
  Send,
} from 'lucide-react';
import { format } from 'date-fns';
import {
  Survey,
  SurveyQuestion,
  SurveyAnswer,
  submitSurveyResponse,
  saveDraftResponse,
  getDraftResponse,
} from '@/api/surveys/data';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';

interface SurveyParticipationProps {
  survey: Survey;
  onComplete?: () => void;
  onExit?: () => void;
}

export const SurveyParticipation: React.FC<SurveyParticipationProps> = ({
  survey,
  onComplete,
  onExit,
}) => {
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [answers, setAnswers] = useState<Record<string, any>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSavingDraft, setIsSavingDraft] = useState(false);
  const [startTime] = useState(Date.now());
  const [questionStartTime, setQuestionStartTime] = useState(Date.now());

  const { draftResponse } = getDraftResponse(survey.id);

  // Load draft responses on mount
  useEffect(() => {
    if (draftResponse?.answers) {
      const draftAnswers: Record<string, any> = {};
      draftResponse.answers.forEach((answer: SurveyAnswer) => {
        draftAnswers[answer.questionId] = answer.answer;
      });
      setAnswers(draftAnswers);
    }
  }, [draftResponse]);

  // Update question start time when question changes
  useEffect(() => {
    setQuestionStartTime(Date.now());
  }, [currentQuestionIndex]);

  const currentQuestion = survey.questions[currentQuestionIndex];
  const isLastQuestion = currentQuestionIndex === survey.questions.length - 1;
  const isFirstQuestion = currentQuestionIndex === 0;

  const getProgressPercentage = () => {
    return ((currentQuestionIndex + 1) / survey.questions.length) * 100;
  };

  const handleAnswerChange = (questionId: string, value: any) => {
    setAnswers((prev) => ({
      ...prev,
      [questionId]: value,
    }));
  };

  const validateCurrentQuestion = () => {
    if (!currentQuestion.isRequired) return true;

    const answer = answers[currentQuestion.id];

    if (!answer) return false;

    if (Array.isArray(answer)) {
      return answer.length > 0;
    }

    if (typeof answer === 'string') {
      return answer.trim().length > 0;
    }

    return true;
  };

  const handleNext = () => {
    if (!validateCurrentQuestion()) {
      toast.error('Please answer this required question before continuing');
      return;
    }

    if (currentQuestionIndex < survey.questions.length - 1) {
      setCurrentQuestionIndex((prev) => prev + 1);
    }
  };

  const handlePrevious = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex((prev) => prev - 1);
    }
  };

  const handleSaveDraft = async () => {
    setIsSavingDraft(true);

    try {
      const surveyAnswers: Partial<SurveyAnswer>[] = Object.entries(
        answers
      ).map(([questionId, answer]) => ({
        questionId,
        questionType:
          survey.questions.find((q) => q.id === questionId)?.type || 'text',
        answer,
      }));

      await saveDraftResponse(survey.id, surveyAnswers);
      toast.success('Draft saved successfully');
    } catch (error) {
      console.error('Error saving draft:', error);
      toast.error('Failed to save draft');
    } finally {
      setIsSavingDraft(false);
    }
  };

  const handleSubmit = async () => {
    // Validate all required questions
    const unansweredRequired = survey.questions.filter(
      (q) => q.isRequired && !answers[q.id]
    );

    if (unansweredRequired.length > 0) {
      toast.error(
        `Please answer all required questions (${unansweredRequired.length} remaining)`
      );
      return;
    }

    setIsSubmitting(true);

    try {
      const completionTime = Math.floor((Date.now() - startTime) / 1000);

      const surveyAnswers: Partial<SurveyAnswer>[] = Object.entries(
        answers
      ).map(([questionId, answer]) => {
        const question = survey.questions.find((q) => q.id === questionId);
        return {
          questionId,
          questionType: question?.type || 'text',
          answer,
          textAnswer: typeof answer === 'string' ? answer : undefined,
          selectedOptions: Array.isArray(answer) ? answer : undefined,
          numericAnswer: typeof answer === 'number' ? answer : undefined,
          dateAnswer: answer instanceof Date ? answer.toISOString() : undefined,
        };
      });

      await submitSurveyResponse(survey.id, surveyAnswers);

      if (onComplete) {
        onComplete();
      }
    } catch (error) {
      console.error('Error submitting survey:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderQuestionInput = (question: SurveyQuestion) => {
    const value = answers[question.id];

    switch (question.type) {
      case 'single_choice':
        return (
          <RadioGroup
            value={value || ''}
            onValueChange={(newValue) =>
              handleAnswerChange(question.id, newValue)
            }
          >
            {question.options?.map((option) => (
              <div key={option.id} className="flex items-center space-x-2">
                <RadioGroupItem value={option.value} id={option.id} />
                <Label htmlFor={option.id} className="flex-1 cursor-pointer">
                  {option.text}
                </Label>
              </div>
            ))}
          </RadioGroup>
        );

      case 'multiple_choice':
        return (
          <div className="space-y-3">
            {question.options?.map((option) => (
              <div key={option.id} className="flex items-center space-x-2">
                <Checkbox
                  id={option.id}
                  checked={Array.isArray(value) && value.includes(option.value)}
                  onCheckedChange={(checked) => {
                    const currentValues = Array.isArray(value) ? value : [];
                    if (checked) {
                      handleAnswerChange(question.id, [
                        ...currentValues,
                        option.value,
                      ]);
                    } else {
                      handleAnswerChange(
                        question.id,
                        currentValues.filter((v) => v !== option.value)
                      );
                    }
                  }}
                />
                <Label htmlFor={option.id} className="flex-1 cursor-pointer">
                  {option.text}
                </Label>
              </div>
            ))}
          </div>
        );

      case 'text':
        return (
          <Input
            value={value || ''}
            onChange={(e) => handleAnswerChange(question.id, e.target.value)}
            placeholder="Enter your answer"
            maxLength={question.validation?.maxLength}
          />
        );

      case 'textarea':
        return (
          <Textarea
            value={value || ''}
            onChange={(e) => handleAnswerChange(question.id, e.target.value)}
            placeholder="Enter your answer"
            rows={4}
            maxLength={question.validation?.maxLength}
          />
        );

      case 'number':
        return (
          <Input
            type="number"
            value={value || ''}
            onChange={(e) =>
              handleAnswerChange(question.id, parseFloat(e.target.value) || '')
            }
            placeholder="Enter a number"
            min={question.validation?.minValue}
            max={question.validation?.maxValue}
          />
        );

      case 'email':
        return (
          <Input
            type="email"
            value={value || ''}
            onChange={(e) => handleAnswerChange(question.id, e.target.value)}
            placeholder="Enter your email address"
          />
        );

      case 'date':
        return (
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className={cn(
                  'justify-start text-left font-normal',
                  !value && 'text-muted-foreground'
                )}
              >
                <CalendarIcon className="mr-2 h-4 w-4" />
                {value ? format(new Date(value), 'PPP') : 'Pick a date'}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0">
              <Calendar
                mode="single"
                selected={value ? new Date(value) : undefined}
                onSelect={(date) =>
                  handleAnswerChange(question.id, date?.toISOString())
                }
                initialFocus
              />
            </PopoverContent>
          </Popover>
        );

      case 'rating':
        const maxRating = 5;
        return (
          <div className="flex items-center gap-2">
            {Array.from({ length: maxRating }, (_, i) => i + 1).map(
              (rating) => (
                <Button
                  key={rating}
                  variant="ghost"
                  size="sm"
                  className={cn(
                    'p-1',
                    value >= rating
                      ? 'text-yellow-500'
                      : 'text-muted-foreground'
                  )}
                  onClick={() => handleAnswerChange(question.id, rating)}
                >
                  <Star
                    className={cn('h-6 w-6', value >= rating && 'fill-current')}
                  />
                </Button>
              )
            )}
            <span className="ml-2 text-sm text-muted-foreground">
              {value ? `${value}/5` : 'Not rated'}
            </span>
          </div>
        );

      case 'yes_no':
        return (
          <RadioGroup
            value={value || ''}
            onValueChange={(newValue) =>
              handleAnswerChange(question.id, newValue)
            }
          >
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="yes" id="yes" />
              <Label htmlFor="yes" className="cursor-pointer">
                Yes
              </Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="no" id="no" />
              <Label htmlFor="no" className="cursor-pointer">
                No
              </Label>
            </div>
          </RadioGroup>
        );

      default:
        return (
          <Input
            value={value || ''}
            onChange={(e) => handleAnswerChange(question.id, e.target.value)}
            placeholder="Enter your answer"
          />
        );
    }
  };

  return (
    <div className="max-w-2xl mx-auto space-y-6">
      {/* Survey Header */}
      <div className="text-center space-y-2">
        <h1 className="text-2xl font-bold">{survey.title}</h1>
        {survey.description && (
          <p className="text-muted-foreground">{survey.description}</p>
        )}
      </div>

      {/* Progress Bar */}
      {survey.settings.showProgressBar && (
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>Progress</span>
            <span>{Math.round(getProgressPercentage())}%</span>
          </div>
          <Progress value={getProgressPercentage()} className="h-2" />
        </div>
      )}

      {/* Question Card */}
      <Card>
        <CardHeader>
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <CardTitle className="text-lg leading-relaxed">
                {currentQuestion.question}
                {currentQuestion.isRequired && (
                  <span className="text-destructive ml-1">*</span>
                )}
              </CardTitle>
              {currentQuestion.description && (
                <p className="text-sm text-muted-foreground mt-2">
                  {currentQuestion.description}
                </p>
              )}
            </div>

            <Badge variant="outline" className="ml-4">
              {currentQuestionIndex + 1} of {survey.questions.length}
            </Badge>
          </div>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* Question Input */}
          <div className="space-y-4">
            {renderQuestionInput(currentQuestion)}
          </div>

          {/* Navigation Buttons */}
          <div className="flex items-center justify-between pt-4">
            <div className="flex gap-2">
              {survey.settings.allowBackNavigation && !isFirstQuestion && (
                <Button
                  variant="outline"
                  onClick={handlePrevious}
                  disabled={isSubmitting}
                >
                  <ChevronLeft className="h-4 w-4 mr-2" />
                  Previous
                </Button>
              )}

              <Button
                variant="ghost"
                onClick={handleSaveDraft}
                disabled={isSavingDraft || isSubmitting}
              >
                <Save className="h-4 w-4 mr-2" />
                {isSavingDraft ? 'Saving...' : 'Save Draft'}
              </Button>
            </div>

            <div className="flex gap-2">
              {!isLastQuestion ? (
                <Button onClick={handleNext} disabled={isSubmitting}>
                  Next
                  <ChevronRight className="h-4 w-4 ml-2" />
                </Button>
              ) : (
                <Button
                  onClick={handleSubmit}
                  disabled={isSubmitting}
                  className="min-w-[120px]"
                >
                  {isSubmitting ? (
                    'Submitting...'
                  ) : (
                    <>
                      <Send className="h-4 w-4 mr-2" />
                      Submit Survey
                    </>
                  )}
                </Button>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Survey Info */}
      <div className="text-center text-sm text-muted-foreground">
        {survey.isAnonymous ? (
          <p>
            This survey is anonymous. Your responses will not be linked to your
            identity.
          </p>
        ) : (
          <p>Your responses will be recorded with your identity.</p>
        )}
      </div>
    </div>
  );
};
