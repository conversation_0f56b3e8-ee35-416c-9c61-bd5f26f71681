export type OrderStatus = "completed" | "processing" | "pending" | "cancelled"

export interface Order {
  id: number
  reference: string
  customer: string
  date: Date
  amount: number
  status: OrderStatus
}




export interface ApiResponse {
  data: Order[]
  total: number
  page?: number
  limit?: number
}

export interface FetchOrdersParams {
  page?: number
  limit?: number
  status?: string
  search?: string
  fromDate?: string
  toDate?: string
}

