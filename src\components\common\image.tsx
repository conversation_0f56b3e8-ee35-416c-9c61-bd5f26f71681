// ImageUpload.tsx
import React, { useRef, useState } from 'react';
import { Button } from '@/components/ui/button';
import { X, Upload, ImageIcon } from 'lucide-react';

interface ImageUploadProps {
  onImageChange: (file: File | null) => void; // Callback to handle the uploaded image
  onPreviewChange: (preview: string | null) => void; // Callback to handle the image preview URL
  initialPreview?: string | null;
}

const ImageUpload: React.FC<ImageUploadProps> = ({
  onImageChange,
  onPreviewChange,
  initialPreview = null,
}) => {
  const [imagePreview, setImagePreview] = useState<string | null>(
    initialPreview
  );
  const fileInputRef = useRef<HTMLInputElement | null>(null);

  // Update preview when initialPreview changes
  React.useEffect(() => {
    if (initialPreview) {
      setImagePreview(initialPreview);
      onPreviewChange(initialPreview);
    }
  }, [initialPreview, onPreviewChange]);

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      onImageChange(file);
      const reader = new FileReader();
      reader.onload = () => {
        setImagePreview(reader.result as string);
        onPreviewChange(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleRemoveImage = () => {
    setImagePreview(null);
    onImageChange(null);
    onPreviewChange(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  return (
    <div className="space-y-2">
      <div className="flex flex-col items-center justify-center gap-4">
        {imagePreview ? (
          <div className="relative w-full max-w-[200px] aspect-square">
            <img
              src={imagePreview || '/placeholder.svg'}
              alt="Product preview"
              className="w-full h-full object-contain rounded-md border"
            />
            <Button
              type="button"
              variant="destructive"
              size="icon"
              className="absolute top-2 right-2 h-8 w-8 rounded-full"
              onClick={handleRemoveImage}
            >
              <X className="h-4 w-4" />
              <span className="sr-only">Remove image</span>
            </Button>
          </div>
        ) : (
          <div
            className="border-2 border-dashed rounded-md p-8 w-full flex flex-col items-center justify-center gap-2 cursor-pointer hover:bg-muted/50 transition-colors"
            onClick={() => fileInputRef.current?.click()}
          >
            <ImageIcon className="h-10 w-10 text-muted-foreground" />
            <p className="text-sm text-muted-foreground">
              Click to upload product image
            </p>
            <p className="text-xs text-muted-foreground">
              PNG, JPG or WEBP (max. 5MB)
            </p>
          </div>
        )}
        <input
          type="file"
          ref={fileInputRef}
          accept="image/png, image/jpeg, image/webp"
          className="hidden"
          onChange={handleImageUpload}
        />
        {!imagePreview && (
          <Button
            type="button"
            variant="outline"
            onClick={() => fileInputRef.current?.click()}
          >
            <Upload className="mr-2 h-4 w-4" />
            Upload Image
          </Button>
        )}
      </div>
    </div>
  );
};

export default ImageUpload;
