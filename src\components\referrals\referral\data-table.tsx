'use client';

import React, { useState, useEffect } from 'react';
import { useSearchAndPagination } from '@/hooks/useSearchAndPagination';
import Pagination from '@/components/common/pagination';
import dayjs from 'dayjs';
import { Ellipsis, Search } from 'lucide-react';
import { GetAllReferrals } from '@/api/referral/data';
import { EmptyState, LoadingState } from '@/components/common/dataState';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
// import Details from './details';
import DateRangeFilter from '@/components/common/date-range-filter';
import { getStatusColor, getUrgencyColor } from '@/lib/utils';
import ReferralDetails from './details';

const Referral = () => {
  const [open, setOpen] = useState(false);
  const {
    searchTerm,
    debouncedSearchTerm,
    handleSearchChange,
    currentPage,
    pageSize,
    handlePageChange,
    queryParam,
    setQueryParam,
  } = useSearchAndPagination({ initialPageSize: 10 });

  const [detail, setDetail] = useState<any | null>(null);
  const [statusFilter, setStatusFilter] = useState('');
  const [startDate, setStartDate] = useState<Date | undefined>(undefined);
  const [endDate, setEndDate] = useState<Date | undefined>(undefined);
  const [showDetails, setShowDetails] = useState(false);
  const [selectedReferralId, setSelectedReferralId] = useState<string | null>(
    null
  );

  // Update query parameters when search term, rating filter, or date range changes
  useEffect(() => {
    let params = [];

    if (statusFilter && statusFilter !== 'all') {
      params.push(`status=${statusFilter}`);
    }

    if (debouncedSearchTerm) {
      params.push(`search=${debouncedSearchTerm}`);
    }

    if (startDate) {
      const formattedStartDate = dayjs(startDate).format('YYYY-MM-DD');
      params.push(`startDate=${formattedStartDate}`);
    }

    if (endDate) {
      const formattedEndDate = dayjs(endDate).format('YYYY-MM-DD');
      params.push(`endDate=${formattedEndDate}`);
    }

    setQueryParam(params.join('&'));
  }, [debouncedSearchTerm, statusFilter, startDate, endDate, setQueryParam]);

  const { referrals, referralLoading, mutate } = GetAllReferrals(
    `?page=${currentPage}&limit=${pageSize}${queryParam ? `&${queryParam}` : ''}`
  );
  const refData = referrals?.data?.referrals;
  const totalPages = referrals?.data?.totalPages ?? 0;

  const handleEventFromModal = (ref: any) => {
    setDetail(ref);
    setOpen(true);
  };

  const handleStatusChange = (value: string) => {
    setStatusFilter(value);
  };

  const handleDateRangeChange = (
    start: Date | undefined,
    end: Date | undefined
  ) => {
    setStartDate(start);
    setEndDate(end);
  };

  // Rating options for the dropdown
  const filterOptions = [
    { value: 'all', label: 'All' },
    { value: 'PENDING_REVIEW', label: 'Pending Review' },
    { value: 'ACCEPTED', label: 'Accepted' },
    { value: 'IN_PROGRESS', label: 'In Progress' },
    { value: 'COMPLETED', label: 'Completed' },
    { value: 'REJECTED', label: 'Rejected' },
    { value: 'CANCELLED', label: 'Cancelled' },
  ];

  const setBack = () => {
    setShowDetails(false);
    mutate();
  };
  if (showDetails && selectedReferralId) {
    return (
      <ReferralDetails
        referralId={selectedReferralId}
        onBack={() => setBack()}
      />
    );
  }

  return (
    <>
      <div className="w-full overflow-x-auto rounded-t-lg shadow-md">
        <div className="flex flex-wrap justify-between items-center mb-4 p-4">
          <div className="flex flex-wrap gap-3 items-center w-full md:w-auto my-4">
            <Select value={statusFilter} onValueChange={handleStatusChange}>
              <SelectTrigger className="w-[220px]">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                {filterOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <DateRangeFilter
              onDateRangeChange={handleDateRangeChange}
              className="w-[250px]"
            />
          </div>
          <div className="relative w-full md:w-64">
            <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
              <Search className="w-4 h-4 text-gray-500" />
            </div>
            <Input
              type="text"
              placeholder="Search referrals..."
              className="pl-10 pr-4 py-2 w-full"
              value={searchTerm}
              onChange={handleSearchChange}
            />
          </div>
        </div>
        <table className="w-full table-auto text-left text-xs">
          <thead className="bg-primary text-gray-100 dark:text-black">
            <tr>
              <th className="table-style">S/N</th>
              <th className="table-style">Date</th>
              <th className="table-style">Referral ID</th>
              <th className="table-style">Patient Email</th>
              <th className="table-style">Referrer</th>
              <th className="table-style">Receiver</th>
              <th className="table-style">Urgency</th>
              <th className="table-style">Status</th>
              <th className="table-style">Details</th>
            </tr>
          </thead>
          <tbody className="relative overflow-x-auto whitespace-nowrap text-sm shadow-md">
            {refData?.map((ref: any, index: any) => (
              <tr
                className="text-xs text-[#062A55] dark:text-white"
                key={ref.id}
              >
                <td className="table-style">
                  {currentPage === 1
                    ? index + 1
                    : (currentPage - 1) * pageSize + (index + 1)}
                </td>
                <td className="table-style">
                  {dayjs(ref.createdAt).format('MMMM D, YYYY')}
                </td>
                <td className="table-style">{ref.referralID}</td>
                <td className="table-style">{ref.patient.emailAddress}</td>
                <td className="table-style">{ref.referringEntity.name}</td>
                <td className="table-style">{ref.receivingEntity.name}</td>
                <td className="table-style">
                  <span
                    className={`inline-flex items-center rounded-full px-3 py-1 font-medium ${getUrgencyColor(ref.urgency)}`}
                  >
                    {ref.urgency.charAt(0).toUpperCase() +
                      ref.urgency.slice(1).toLowerCase()}
                  </span>
                </td>
                <td className="table-style">
                  <span
                    className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${getStatusColor(ref.status)}`}
                  >
                    {ref.status
                      .replace(/_/g, ' ')
                      .toLowerCase()
                      .replace(/\b\w/g, (l: string) => l.toUpperCase())}
                  </span>
                </td>
                <td className="table-style">
                  <Ellipsis
                    onClick={() => {
                      setSelectedReferralId(ref.id);
                      setShowDetails(true);
                    }}
                    className="w-4 h-4 cursor-pointer"
                  />
                </td>
              </tr>
            ))}
          </tbody>
        </table>
        {referralLoading ? (
          <LoadingState />
        ) : totalPages === 0 ? (
          <EmptyState />
        ) : null}
      </div>
      {totalPages > 1 ? (
        <Pagination
          title="referals"
          totalPages={totalPages}
          currentPage={currentPage}
          onPageChange={handlePageChange}
          totalCount={referrals?.data?.totalCount}
        />
      ) : (
        ''
      )}
      {/* {open && (
        <Details open={open} setOpen={setOpen} mutate={mutate} data={detail} />
      )} */}
    </>
  );
};

export default Referral;
