'use client';

import { useFormContext } from 'react-hook-form';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

export default function StepReferees() {
  const { register } = useFormContext();

  return (
    <div className="space-y-6">
      <p className="text-sm text-muted-foreground">
        Please provide details for at least 2 professional referees who can
        attest to your clinical competence.
      </p>

      {[1, 2, 3, 4].map((num) => (
        <Card key={num}>
          <CardHeader>
            <CardTitle className="text-base">Referee {num}</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Input
                placeholder="Full Name"
                {...register(`referees.${num}.name`)}
              />
              <Input
                placeholder="Position/Title"
                {...register(`referees.${num}.position`)}
              />
              <Input
                placeholder="Telephone"
                {...register(`referees.${num}.telephone`)}
              />
              <Input
                type="email"
                placeholder="Email"
                {...register(`referees.${num}.email`)}
              />
              <Input
                placeholder="Address"
                {...register(`referees.${num}.address`)}
              />
              <Input
                placeholder="Postcode"
                {...register(`referees.${num}.postcode`)}
              />
            </div>
            <Input
              placeholder="Relationship to Applicant"
              {...register(`referees.${num}.relationship`)}
            />
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
