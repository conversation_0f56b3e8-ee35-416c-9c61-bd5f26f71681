# WebSocket Notification Service

This service handles real-time notifications from the backend server using WebSockets.

## Configuration

To configure the WebSocket connection, update the WebSocket URL in `websocket.ts`:

```typescript
// Replace with your actual WebSocket server URL
const websocketService = new WebSocketService('wss://your-backend-url/ws');
```

## Message Format

The WebSocket service expects messages in the following format:

```json
{
  "type": "notification",
  "data": {
    "title": "Notification Title",
    "message": "Notification message content",
    "link": "/optional/link/to/navigate"
  }
}
```

### Supported Message Types

1. `notification` - General notifications

   ```json
   {
     "type": "notification",
     "data": {
       "title": "New Alert",
       "message": "Something important happened",
       "link": "/dashboard"
     }
   }
   ```

2. `forum_message` - Forum-specific messages
   ```json
   {
     "type": "forum_message",
     "data": {
       "sender": "<PERSON>e",
       "preview": "Hello, how are you?",
       "groupId": "group-123"
     }
   }
   ```

## Backend Implementation

Your backend should implement a WebSocket server that sends messages in the format described above. Here's a simple example using Node.js and ws:

```javascript
const WebSocket = require('ws');

const wss = new WebSocket.Server({ port: 8080 });

wss.on('connection', (ws) => {
  console.log('Client connected');

  // Send a welcome notification
  ws.send(
    JSON.stringify({
      type: 'notification',
      data: {
        title: 'Connected',
        message: 'You are now connected to the notification service',
        link: null,
      },
    })
  );

  // Handle messages from client
  ws.on('message', (message) => {
    const data = JSON.parse(message);
    console.log('Received:', data);

    // Handle join_channel messages
    if (data.type === 'join_channel') {
      // Add client to channel
      console.log(`Client joined channel: ${data.data.channel}`);
    }
  });

  ws.on('close', () => {
    console.log('Client disconnected');
  });
});

// Example: Send a notification to all clients
function broadcastNotification(title, message, link) {
  wss.clients.forEach((client) => {
    if (client.readyState === WebSocket.OPEN) {
      client.send(
        JSON.stringify({
          type: 'notification',
          data: { title, message, link },
        })
      );
    }
  });
}

// Example usage
// broadcastNotification('System Update', 'The system will be down for maintenance', '/maintenance');
```
