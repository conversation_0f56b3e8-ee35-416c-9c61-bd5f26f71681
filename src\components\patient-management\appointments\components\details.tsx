import React, { useState } from 'react';
import { Modal } from '@/components/common/modal';
import { ModalProps } from '@/components/crm/types';
import dayjs from 'dayjs';
import { myApi } from '@/api/fetcher';
import { toast } from 'sonner';
import { GetProfile } from '@/api/staff';
import { Button } from '@/components/ui/button';
import { hasPermission, PERMISSIONS } from '@/lib/types/permissions';
import { useRouter } from 'next/navigation';
import { Paths } from '@/components/navigations/data';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { User, CalendarDays, ClipboardList, FileText } from 'lucide-react';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

interface TabProps {
  label: string;
  icon: React.ReactNode;
  content: React.ReactNode;
}

const Details: React.FC<ModalProps> = ({ open, setOpen, mutate, data }) => {
  const [isLoading, setIsLoading] = useState(false);
  // Using activeTab state to track the current tab
  const [activeTab, setActiveTab] = useState(0);
  const [diagnosis, setDiagnosis] = useState('');
  const [treatment, setTreatment] = useState('');
  const [notes, setNotes] = useState('');
  const [followUpRequired, setFollowUpRequired] = useState(false);
  const [followUpDate, setFollowUpDate] = useState('');
  const { profile } = GetProfile();
  const router = useRouter();
  const permitUpdate = hasPermission('appointment:update');

  if (!data) return null;

  const handleViewPatient = () => {
    setOpen(false);
    router.push(`${Paths.Patients}?patientId=${data.patientId}`);
  };

  const handleUpdateStatus = async (newStatus: string) => {
    try {
      setIsLoading(true);
      const res = await myApi.patch(`/crm/appointments/${data.id}/status`, {
        status: newStatus,
        updatedBy: profile.data.fullName,
      });
      setIsLoading(false);
      if (res.status === 200) {
        toast.success(`Appointment marked as ${newStatus}`);
        if (mutate) {
          mutate();
        }
        setOpen(false);
      }
    } catch (error: any) {
      setIsLoading(false);
      toast.error(error?.response?.data?.message || 'An error occurred');
    }
  };

  const handleCompleteAppointment = async () => {
    try {
      setIsLoading(true);
      const res = await myApi.patch(`/crm/appointments/${data.id}/complete`, {
        diagnosis,
        treatment,
        notes,
        followUpRequired,
        followUpDate: followUpRequired ? followUpDate : undefined,
        completedBy: profile.data.fullName,
      });
      setIsLoading(false);
      if (res.status === 200) {
        toast.success('Appointment completed successfully');
        if (mutate) {
          mutate();
        }
        setOpen(false);
      }
    } catch (error: any) {
      setIsLoading(false);
      toast.error(error?.response?.data?.message || 'An error occurred');
    }
  };

  // Tab content components
  const AppointmentInfo = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-xs font-medium mb-2 text-gray-500 uppercase">
          Appointment Details
        </h3>
        <div className="grid sm:grid-cols-2 gap-4 text-sm">
          <div>
            <p className="text-gray-500 dark:text-gray-400">Patient</p>
            <p className="font-medium">{data.patientName}</p>
          </div>
          <div>
            <p className="text-gray-500 dark:text-gray-400">Appointment ID</p>
            <p className="font-medium">A-{String(data.id).padStart(6, '0')}</p>
          </div>
          <div>
            <p className="text-gray-500 dark:text-gray-400">Date & Time</p>
            <p className="font-medium">
              {dayjs(data.appointmentDate).format('MMMM D, YYYY h:mm A')}
            </p>
          </div>
          <div>
            <p className="text-gray-500 dark:text-gray-400">Type</p>
            <p className="font-medium">
              {data.type.replace('_', ' ').charAt(0).toUpperCase() +
                data.type.replace('_', ' ').slice(1)}
            </p>
          </div>
          <div>
            <p className="text-gray-500 dark:text-gray-400">Department</p>
            <p className="font-medium">{data.department}</p>
          </div>
          <div>
            <p className="text-gray-500 dark:text-gray-400">Doctor</p>
            <p className="font-medium">{data.doctorName}</p>
          </div>
          <div>
            <p className="text-gray-500 dark:text-gray-400">Priority</p>
            <p
              className={`font-medium ${
                data.priority === 'emergency'
                  ? 'text-red-600'
                  : data.priority === 'urgent'
                    ? 'text-orange-600'
                    : 'text-green-600'
              }`}
            >
              {data.priority.charAt(0).toUpperCase() + data.priority.slice(1)}
            </p>
          </div>
          <div>
            <p className="text-gray-500 dark:text-gray-400">Status</p>
            <p
              className={`font-medium ${
                data.status === 'completed'
                  ? 'text-green-600'
                  : data.status === 'scheduled'
                    ? 'text-blue-600'
                    : data.status === 'cancelled'
                      ? 'text-red-600'
                      : 'text-yellow-600'
              }`}
            >
              {data.status.charAt(0).toUpperCase() + data.status.slice(1)}
            </p>
          </div>
        </div>
      </div>

      {data.symptoms && (
        <div>
          <h3 className="text-xs font-medium mb-2 text-gray-500 uppercase">
            Symptoms
          </h3>
          <p className="text-sm whitespace-pre-wrap">{data.symptoms}</p>
        </div>
      )}

      {data.description && (
        <div>
          <h3 className="text-xs font-medium mb-2 text-gray-500 uppercase">
            Additional Notes
          </h3>
          <p className="text-sm whitespace-pre-wrap">{data.description}</p>
        </div>
      )}

      {permitUpdate && data.status === 'scheduled' && (
        <div className="flex justify-end gap-2 mt-4">
          <Button
            variant="outline"
            onClick={() => handleUpdateStatus('cancelled')}
            className="text-red-600"
          >
            Cancel Appointment
          </Button>
          <Button onClick={() => setActiveTab(1)}>Complete Appointment</Button>
        </div>
      )}

      {data.status !== 'scheduled' && (
        <div className="flex justify-end gap-2 mt-4">
          <Button variant="outline" onClick={handleViewPatient}>
            View Patient
          </Button>
        </div>
      )}
    </div>
  );

  const CompleteAppointment = () => (
    <div className="space-y-4">
      <h3 className="text-sm font-medium">Complete Appointment</h3>

      <div>
        <label className="text-sm font-medium">Diagnosis</label>
        <Textarea
          placeholder="Enter diagnosis"
          className="resize-none"
          value={diagnosis}
          onChange={(e) => setDiagnosis(e.target.value)}
        />
      </div>

      <div>
        <label className="text-sm font-medium">Treatment</label>
        <Textarea
          placeholder="Enter treatment plan"
          className="resize-none"
          value={treatment}
          onChange={(e) => setTreatment(e.target.value)}
        />
      </div>

      <div>
        <label className="text-sm font-medium">Notes</label>
        <Textarea
          placeholder="Additional notes"
          className="resize-none"
          value={notes}
          onChange={(e) => setNotes(e.target.value)}
        />
      </div>

      <div className="flex items-center space-x-2">
        <input
          type="checkbox"
          id="followUpRequired"
          checked={followUpRequired}
          onChange={(e) => setFollowUpRequired(e.target.checked)}
          className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
        />
        <label htmlFor="followUpRequired" className="text-sm font-medium">
          Follow-up Required
        </label>
      </div>

      {followUpRequired && (
        <div>
          <label className="text-sm font-medium">Follow-up Date</label>
          <input
            type="date"
            className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
            value={followUpDate}
            onChange={(e) => setFollowUpDate(e.target.value)}
          />
        </div>
      )}

      <div className="flex justify-end gap-2 mt-4">
        <Button variant="outline" onClick={() => setActiveTab(0)}>
          Back
        </Button>
        <Button
          onClick={handleCompleteAppointment}
          disabled={isLoading || !diagnosis}
        >
          {isLoading ? 'Processing...' : 'Complete Appointment'}
        </Button>
      </div>
    </div>
  );

  // Define tabs
  const tabs: TabProps[] = [
    {
      label: 'Appointment Info',
      icon: <CalendarDays className="w-4 h-4" />,
      content: <AppointmentInfo />,
    },
    {
      label: 'Complete',
      icon: <ClipboardList className="w-4 h-4" />,
      content: <CompleteAppointment />,
    },
  ];

  return (
    <Modal
      open={open}
      setOpen={setOpen}
      title="Appointment Details"
      description={`${data.subject || 'Appointment'}`}
      size="lg"
    >
      <Tabs
        defaultValue="0"
        className="w-full"
        onValueChange={(value) => setActiveTab(parseInt(value))}
      >
        <TabsList className="grid grid-cols-2 mb-4">
          {tabs.map((tab, index) => (
            <TabsTrigger
              key={index}
              value={index.toString()}
              className="flex items-center gap-2"
              disabled={index === 1 && data.status !== 'scheduled'}
            >
              {tab.icon}
              <span className="hidden sm:inline">{tab.label}</span>
            </TabsTrigger>
          ))}
        </TabsList>

        {tabs.map((tab, index) => (
          <TabsContent key={index} value={index.toString()} className="mt-0">
            {tab.content}
          </TabsContent>
        ))}
      </Tabs>
    </Modal>
  );
};

export default Details;
