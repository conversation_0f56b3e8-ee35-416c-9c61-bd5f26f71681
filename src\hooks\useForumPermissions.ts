import { useMemo } from 'react';
import { hasPermission, PERMISSIONS } from '@/lib/types/permissions';

export const useForumPermissions = () => {
  const permissions = useMemo(() => {
    return {
      canCreateGroup: hasPermission(PERMISSIONS.FORUM_CREATE_GROUP),
      canViewGroup: hasPermission(PERMISSIONS.FORUM_VIEW_GROUP),
      canEditGroup: hasPermission(PERMISSIONS.FORUM_EDIT_GROUP),
      canDeleteGroup: hasPermission(PERMISSIONS.FORUM_DELETE_GROUP),
      canAddMembers: hasPermission(PERMISSIONS.FORUM_ADD_MEMBERS),
      canRemoveMembers: hasPermission(PERMISSIONS.FORUM_REMOVE_MEMBERS),
      canJoinGroup: hasPermission(PERMISSIONS.FORUM_JOIN_GROUP),
      canSendMessage: hasPermission(PERMISSIONS.FORUM_SEND_MESSAGE),
      canViewMessages: hasPermission(PERMISSIONS.FORUM_VIEW_MESSAGES),
      canEditMessage: hasPermission(PERMISSIONS.FORUM_EDIT_MESSAGE),
      canDeleteMessage: hasPermission(PERMISSIONS.FORUM_DELETE_MESSAGE),
      canModerateMessages: hasPermission(PERMISSIONS.FORUM_MODERATE_MESSAGES),
      canPinMessage: hasPermission(PERMISSIONS.FORUM_PIN_MESSAGE),
    };
  }, []);

  return permissions;
};
