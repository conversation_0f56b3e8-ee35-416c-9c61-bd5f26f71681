import React, { useState, useEffect } from 'react';
import { Modal } from '@/components/common/modal';
import { ModalProps } from '@/components/crm/types';
import dayjs from 'dayjs';
import { myApi } from '@/api/fetcher';
import { toast } from 'sonner';
import { GetProfile } from '@/api/staff';
import { GetDoctors } from '@/api/crm/data';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Building, Users, Calendar, ClipboardList } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { hasPermission, PERMISSIONS } from '@/lib/types/permissions';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  departmentFormSchema,
  DepartmentFormValues,
} from '@/components/validations/crm';
import { InputField } from '@/components/common/form';
import { Form } from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';

interface TabProps {
  label: string;
  icon: React.ReactNode;
  content: React.ReactNode;
}

const Details: React.FC<ModalProps> = ({ open, setOpen, mutate, data }) => {
  const [isLoading, setIsLoading] = useState(false);
  // Using activeTab state to track the current tab
  const [, setActiveTab] = useState(0);
  const [editMode, setEditMode] = useState(false);
  const [doctors, setDoctors] = useState<any[]>([]);
  const [doctorsLoading, setDoctorsLoading] = useState(false);
  const { profile } = GetProfile();
  const permitUpdate = hasPermission(PERMISSIONS.STAFF_EDIT);

  const form = useForm<DepartmentFormValues>({
    resolver: zodResolver(departmentFormSchema),
    defaultValues: {
      name: data?.name || '',
      description: data?.description || '',
      headOfDepartmentId: data?.headOfDepartmentId?.toString() || '',
      isActive: data?.isActive || true,
      location: data?.location || '',
      contactEmail: data?.contactEmail || '',
      contactPhone: data?.contactPhone || '',
    },
  });

  // Fetch doctors for dropdown when in edit mode
  useEffect(() => {
    const fetchDoctors = async () => {
      if (editMode) {
        try {
          setDoctorsLoading(true);
          const response = await myApi.get('/crm/doctors');
          if (response.status === 200) {
            setDoctors(response.data.data.doctors);
          }
        } catch (error) {
          console.error('Error fetching doctors:', error);
        } finally {
          setDoctorsLoading(false);
        }
      }
    };

    fetchDoctors();
  }, [editMode]);

  const handleUpdateDepartment = async (formData: DepartmentFormValues) => {
    try {
      setIsLoading(true);
      const res = await myApi.patch(`/crm/departments/${data.id}`, {
        ...formData,
        updatedBy: profile.data.fullName,
      });
      setIsLoading(false);
      if (res.status === 200) {
        toast.success('Department updated successfully');
        setEditMode(false);
        if (mutate) {
          mutate();
        }
      }
    } catch (error: any) {
      setIsLoading(false);
      toast.error(error?.response?.data?.message || 'An error occurred');
    }
  };

  const toggleStatus = async () => {
    try {
      setIsLoading(true);
      const res = await myApi.patch(`/crm/departments/${data.id}/status`, {
        isActive: !data.isActive,
        updatedBy: profile.data.fullName,
      });
      setIsLoading(false);
      if (res.status === 200) {
        toast.success(
          `Department marked as ${!data.isActive ? 'active' : 'inactive'}`
        );
        if (mutate) {
          mutate();
        }
      }
    } catch (error: any) {
      setIsLoading(false);
      toast.error(error?.response?.data?.message || 'An error occurred');
    }
  };

  if (!data) return null;

  // Tab content components
  const DepartmentInfo = () => (
    <div className="space-y-6">
      {editMode ? (
        <Form {...form}>
          <form
            className="space-y-4"
            onSubmit={form.handleSubmit(handleUpdateDepartment)}
          >
            <InputField
              control={form.control}
              name="name"
              label="Department Name"
              placeholder="Cardiology"
            />

            <div>
              <label className="text-sm font-medium">Description</label>
              <Textarea
                placeholder="Brief description about the department"
                className="resize-none"
                {...form.register('description')}
              />
            </div>

            <div>
              <label className="text-sm font-medium">Head of Department</label>
              <Select
                onValueChange={(value) =>
                  form.setValue('headOfDepartmentId', value)
                }
                disabled={doctorsLoading}
                defaultValue={form.getValues('headOfDepartmentId')}
              >
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Select head of department" />
                </SelectTrigger>
                <SelectContent>
                  {doctors.map((doctor) => (
                    <SelectItem key={doctor.id} value={doctor.id.toString()}>
                      Dr. {doctor.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <InputField
              control={form.control}
              name="location"
              label="Location"
              placeholder="Building A, Floor 2"
            />

            <div className="grid grid-cols-2 gap-4">
              <InputField
                control={form.control}
                name="contactEmail"
                label="Contact Email"
                placeholder="<EMAIL>"
                type="email"
              />

              <InputField
                control={form.control}
                name="contactPhone"
                label="Contact Phone"
                placeholder="+234 ************"
              />
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="active-status-edit"
                checked={form.getValues('isActive')}
                onCheckedChange={(checked) =>
                  form.setValue('isActive', checked)
                }
              />
              <label
                htmlFor="active-status-edit"
                className="text-sm font-medium"
              >
                Department is Active
              </label>
            </div>

            <div className="flex justify-end gap-2">
              <Button
                type="button"
                variant="outline"
                onClick={() => setEditMode(false)}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isLoading}>
                Save Changes
              </Button>
            </div>
          </form>
        </Form>
      ) : (
        <>
          <div className="flex justify-between items-start">
            <div>
              <h3 className="text-lg font-semibold">{data.name}</h3>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {data.location}
              </p>
            </div>
            {permitUpdate && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => setEditMode(true)}
              >
                Edit
              </Button>
            )}
          </div>

          <div>
            <h4 className="text-sm font-medium">Description</h4>
            <p className="text-sm">{data.description}</p>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <h4 className="text-sm font-medium">Head of Department</h4>
              <p className="text-sm">
                {data.headOfDepartment || 'Not assigned'}
              </p>
            </div>
            <div>
              <h4 className="text-sm font-medium">Number of Doctors</h4>
              <p className="text-sm">{data.doctorsCount || 0}</p>
            </div>
            <div>
              <h4 className="text-sm font-medium">Contact Email</h4>
              <p className="text-sm">{data.contactEmail || 'Not provided'}</p>
            </div>
            <div>
              <h4 className="text-sm font-medium">Contact Phone</h4>
              <p className="text-sm">{data.contactPhone || 'Not provided'}</p>
            </div>
            <div>
              <h4 className="text-sm font-medium">Status</h4>
              <div className="flex items-center gap-2">
                <span
                  className={`px-2 py-1 text-xs rounded-full ${
                    data.isActive
                      ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'
                      : 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400'
                  }`}
                >
                  {data.isActive ? 'Active' : 'Inactive'}
                </span>
                {permitUpdate && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={toggleStatus}
                    disabled={isLoading}
                  >
                    Toggle
                  </Button>
                )}
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  );

  const DoctorsList = () => (
    <div className="space-y-4">
      <h3 className="text-md font-semibold">Doctors in Department</h3>
      <div className="border rounded-md p-4">
        {data.doctors && data.doctors.length > 0 ? (
          <ul className="space-y-2">
            {data.doctors.map((doctor: any) => (
              <li key={doctor.id} className="flex justify-between items-center">
                <div>
                  <p className="font-medium">Dr. {doctor.name}</p>
                  <p className="text-xs text-gray-500">
                    {doctor.specialization}
                  </p>
                </div>
                <span
                  className={`px-2 py-1 text-xs rounded-full ${
                    doctor.isAvailable
                      ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'
                      : 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400'
                  }`}
                >
                  {doctor.isAvailable ? 'Available' : 'Unavailable'}
                </span>
              </li>
            ))}
          </ul>
        ) : (
          <p className="text-sm text-gray-500 dark:text-gray-400">
            No doctors assigned to this department yet.
          </p>
        )}
      </div>
    </div>
  );

  const tabs: TabProps[] = [
    {
      label: 'Details',
      icon: <Building className="h-4 w-4" />,
      content: <DepartmentInfo />,
    },
    {
      label: 'Doctors',
      icon: <Users className="h-4 w-4" />,
      content: <DoctorsList />,
    },
  ];

  return (
    <Modal
      open={open}
      setOpen={setOpen}
      title="Department Details"
      description={data.name}
      size="lg"
    >
      <Tabs
        defaultValue="0"
        className="w-full"
        onValueChange={(value) => setActiveTab(parseInt(value))}
      >
        <TabsList className="grid grid-cols-2 mb-4">
          {tabs.map((tab, index) => (
            <TabsTrigger
              key={index}
              value={index.toString()}
              className="flex items-center gap-2"
            >
              {tab.icon}
              {tab.label}
            </TabsTrigger>
          ))}
        </TabsList>
        {tabs.map((tab, index) => (
          <TabsContent key={index} value={index.toString()}>
            {tab.content}
          </TabsContent>
        ))}
      </Tabs>
    </Modal>
  );
};

export default Details;
