'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import useIsLoggedIn from '@/hooks/isLoggedIn';
import { WelcomeScreen } from '@/components/auth/welcome-screen';

export default function Home() {
  const isLoggedIn = useIsLoggedIn();
  const router = useRouter();
  const [showWelcome, setShowWelcome] = useState(true);

  useEffect(() => {
    if (isLoggedIn === true && !showWelcome) {
      router.replace('/dashboard');
    } else if (isLoggedIn === false && !showWelcome) {
      router.replace('/login');
    }
  }, [isLoggedIn, showWelcome, router]);

  if (isLoggedIn === null || showWelcome) {
    return (
      <WelcomeScreen
        onComplete={() => setShowWelcome(false)}
      />
    );
  }

  return null;
}
