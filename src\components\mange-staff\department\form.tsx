import React, { useState, useEffect } from 'react';
import { InputField, FormRow } from '@/components/common/form';
import { Form } from '@/components/ui/form';
import { Button } from '@/components/ui/button';
import { Modal } from '@/components/common/modal';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm, useFieldArray } from 'react-hook-form';
import { myApi } from '@/api/fetcher';
import { toast } from 'sonner';
import { MultiSelect } from '@/components/common/multi-select';
import { Label } from '@/components/ui/label';
import { Check } from 'lucide-react';
import { z } from 'zod';
import { GetStaffList } from '@/api/staff'; // You'll need to create/import this

interface Staff {
  id: string;
  name: string;
}

export const DepartmentFormSchema = z.object({
  name: z.string().min(1, 'Department name is required'),
  // managerId is handled via selectedManagerIds state
});

export type DepartmentFormValues = z.infer<typeof DepartmentFormSchema>;

export interface DepartmentInitialData {
  id: string;
  name: string;
  manager?: Staff;
}

interface DepartmentModalProps {
  open: boolean;
  setOpen: (open: boolean) => void;
  mutate?: () => void;
  isEditMode?: boolean;
  initialData?: DepartmentInitialData;
}

const DepartmentForm: React.FC<DepartmentModalProps> = ({
  setOpen,
  mutate,
  open,
  isEditMode = false,
  initialData,
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [selectedManagerIds, setSelectedManagerIds] = useState<string[]>([]);

  // Fetch staff data for manager selection
  // const { staffs } = GetStaffs(`active=true`); // Example query
  // const staffData: Staff[] = staffs?.data || [];

  const form = useForm<DepartmentFormValues>({
    resolver: zodResolver(DepartmentFormSchema),
    defaultValues: {
      name: '',
    },
  });

  useEffect(() => {
    if (isEditMode && initialData) {
      form.reset({
        name: initialData.name,
      });
      setSelectedManagerIds(
        initialData.manager ? [initialData.manager.id] : []
      );
    } else {
      form.reset({
        name: '',
      });
      setSelectedManagerIds([]);
    }
  }, [isEditMode, initialData, form]);

  const onSubmit = async (data: DepartmentFormValues) => {
    const managerId =
      selectedManagerIds.length > 0 ? selectedManagerIds[0] : undefined;
    const payload: any = {
      name: data.name,
    };

    if (isEditMode) {
      payload.managerId = managerId;
    }

    try {
      setIsLoading(true);
      let res;
      if (isEditMode && initialData) {
        res = await myApi.patch(`/department/${initialData.id}`, payload);
      } else {
        res = await myApi.post('/staff/create-department', payload);
      }
      setIsLoading(false);
      if (res.status === 200) {
        toast.success(
          `Department ${isEditMode ? 'updated' : 'created'} successfully`
        );
        if (mutate) {
          mutate();
        }
        setOpen(false);
        form.reset();
        setSelectedManagerIds([]);
      }
    } catch (error) {
      setIsLoading(false);
      toast.error(`Failed to ${isEditMode ? 'update' : 'create'} department`);
      console.error('Error submitting department form:', error);
    }
  };

  return (
    <Modal
      open={open}
      setOpen={setOpen}
      title={isEditMode ? 'Edit Department' : 'Add New Department'}
      description={
        isEditMode
          ? 'Update the details of the existing department.'
          : 'Create a new department in the system.'
      }
      isLoading={isLoading}
      onSubmit={form.handleSubmit(onSubmit)}
    >
      <Form {...form}>
        <form className="space-y-4">
          <InputField
            control={form.control}
            name="name"
            label="Department Name"
            placeholder="e.g., Human Resources"
            type="text"
          />

          {/* {isEditMode && (
            <>
              <div className="space-y-2">
                <Label htmlFor="manager">Select Department Manager</Label>
                <MultiSelect
                  className="overflow-auto"
                  options={staffData}
                  selected={selectedManagerIds}
                  onChange={(ids) => setSelectedManagerIds(ids.slice(0, 1))} // Enforce single selection
                  placeholder="Select a manager"
                  valueField="id"
                  labelField="name"
                  badgeClassName="bg-primary text-primary-foreground hover:bg-primary/90"
                  renderOption={(staffMember, isSelected) => (
                    <div className="flex flex-col">
                      <div className="flex items-center">
                        <div className="mr-2 flex h-4 w-4 items-center justify-center">
                          {isSelected ? <Check className="h-4 w-4" /> : null}
                        </div>
                        <span>{(staffMember as Staff).name}</span>
                      </div>
                    </div>
                  )}
                />
              </div>
            </>
          )} */}
        </form>
      </Form>
    </Modal>
  );
};

export default DepartmentForm;
