import React from 'react';
import Link from 'next/link';
import {
  Too<PERSON><PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';

// Define the type for the props
interface NavItemProps {
  url: string;
  icon: React.ElementType;
  children: React.ReactNode;
  isActive: boolean;
  collapsed?: boolean;
  onClick?: () => void;
}

// The NavItem component
const NavItem: React.FC<NavItemProps> = ({
  url,
  icon: Icon,
  children,
  isActive,
  collapsed = false,
  onClick,
}) => {
  const content = (
    <button
      onClick={onClick}
      type="button"
      className={`cursor-pointer w-full flex items-center px-3 py-2 text-sm rounded-md transition-colors 
      ${isActive ? 'bg-primary text-white dark:text-[#1F1F23]' : 'hover:bg-gray-100 dark:hover:bg-gray-800'}
      ${collapsed ? 'justify-center' : ''}`}
    >
      <Icon className={`h-4 w-4 flex-shrink-0 ${collapsed ? '' : 'mr-3'}`} />
      {!collapsed && children}
    </button>
  );

  return collapsed ? (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Link className="space-y-2" key={url} href={url}>
            {content}
          </Link>
        </TooltipTrigger>
        <TooltipContent side="right">
          <p>{children}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  ) : (
    <Link className="space-y-2" key={url} href={url}>
      {content}
    </Link>
  );
};

export default NavItem;
