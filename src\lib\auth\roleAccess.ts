// 'use client';

// import { GetProfile } from '@/api/staff';
// import { useRouter } from 'next/navigation';
// import { useEffect, useState } from 'react';

// /**
//  * Hook to check if the current user has the required permissions
//  * @param requiredPermissions Array of permissions that are required to access the component
//  * @returns Object containing loading state and whether the user has access
//  */
// export function usePermissionAccess(requiredPermissions: string[]) {
//   const { profile, isLoading } = GetProfile();
//   const [hasAccess, setHasAccess] = useState(false);
//   const router = useRouter();
//   const [redirectAttempted, setRedirectAttempted] = useState(false);

//   useEffect(() => {
//     // If still loading, don't do anything yet but don't block access
//     if (isLoading) {
//       setHasAccess(true); // Be permissive during loading
//       return;
//     }

//     // Check if profile exists and has roles
//     const roles =
//       profile?.data?.roles || (profile?.data?.role ? [profile.data.role] : []);

//     if (!profile || !profile.data || !roles.length) {
//       console.error('User profile is invalid or missing role information');

//       // Only attempt redirect once to prevent loops
//       if (!redirectAttempted) {
//         setRedirectAttempted(true);

//         // Add a delay to avoid immediate redirect
//         const timer = setTimeout(() => {
//           // Only redirect if we're not already on the forbidden page
//           if (window.location.pathname !== '/forbidden') {
//             router.push('/forbidden');
//           }
//         }, 1000);

//         return () => clearTimeout(timer);
//       }
//       return;
//     }

//     // Check if user has any of the required permissions
//     const hasRequiredPermission = requiredPermissions.some((permission) =>
//       roles.some((role: { permissions: { action: string }[] }) =>
//         role?.permissions?.some(
//           (item: { action: string }) => item.action === permission
//         )
//       )
//     );

//     if (!hasRequiredPermission) {
//       console.warn(
//         `User attempted to access a restricted area requiring one of these permissions: ${requiredPermissions.join(', ')}`
//       );

//       // Only attempt redirect once to prevent loops
//       if (!redirectAttempted) {
//         setRedirectAttempted(true);

//         // Add a delay to avoid immediate redirect
//         const timer = setTimeout(() => {
//           // Only redirect if we're not already on the forbidden page
//           if (window.location.pathname !== '/forbidden') {
//             router.push('/forbidden');
//           }
//         }, 1000);

//         return () => clearTimeout(timer);
//       }
//       return;
//     }

//     setHasAccess(true);
//   }, [profile, isLoading, requiredPermissions, router, redirectAttempted]);

//   return { isLoading, hasAccess };
// }

// /**
//  * Legacy hook to check if the current user has the required role
//  * @param requiredRoles Array of roles that are allowed to access the component
//  * @returns Object containing loading state and whether the user has access
//  */
// export function useRoleAccess(requiredRoles: string[]) {
//   const { profile, isLoading } = GetProfile();
//   const [hasAccess, setHasAccess] = useState(false);
//   const router = useRouter();
//   const [redirectAttempted, setRedirectAttempted] = useState(false);

//   useEffect(() => {
//     // If still loading, don't do anything yet but don't block access
//     if (isLoading) {
//       setHasAccess(true); // Be permissive during loading
//       return;
//     }

//     // Check if profile exists and has roles
//     const roles =
//       profile?.data?.roles || (profile?.data?.role ? [profile.data.role] : []);

//     if (!profile || !profile.data || !roles.length) {
//       console.error('User profile is invalid or missing role information');

//       // Only attempt redirect once to prevent loops
//       if (!redirectAttempted) {
//         setRedirectAttempted(true);

//         // Add a delay to avoid immediate redirect
//         const timer = setTimeout(() => {
//           // Only redirect if we're not already on the forbidden page
//           if (window.location.pathname !== '/forbidden') {
//             router.push('/forbidden');
//           }
//         }, 1000);

//         return () => clearTimeout(timer);
//       }
//       return;
//     }

//     // Check if any of user's roles match the required roles
//     const hasRequiredRole = requiredRoles.some((requiredRole) =>
//       roles.some(
//         (role: { name: string }) =>
//           role.name.toLowerCase() === requiredRole.toLowerCase()
//       )
//     );

//     if (!hasRequiredRole) {
//       console.warn(
//         `User attempted to access a restricted area requiring one of these roles: ${requiredRoles.join(', ')}`
//       );

//       // Only attempt redirect once to prevent loops
//       if (!redirectAttempted) {
//         setRedirectAttempted(true);

//         // Add a delay to avoid immediate redirect
//         const timer = setTimeout(() => {
//           // Only redirect if we're not already on the forbidden page
//           if (window.location.pathname !== '/forbidden') {
//             router.push('/forbidden');
//           }
//         }, 1000);

//         return () => clearTimeout(timer);
//       }
//       return;
//     }

//     setHasAccess(true);
//   }, [profile, isLoading, requiredRoles, router, redirectAttempted]);

//   return { isLoading, hasAccess };
// }

// /**
//  * Utility function to check if a user has a specific role
//  * @param role The role to check for
//  * @returns Boolean indicating if the user has the specified role
//  */
// export function hasRole(role: string): boolean {
//   const { profile } = GetProfile();

//   // Check if user has roles (new structure) or role (old structure)
//   const roles =
//     profile?.data?.roles || (profile?.data?.role ? [profile.data.role] : []);

//   if (!roles.length) {
//     return false;
//   }

//   return roles.some(
//     (userRole: { name: string }) =>
//       userRole.name.toLowerCase() === role.toLowerCase()
//   );
// }

// /**
//  * Utility function to check if a user has a specific permission
//  * @param permission The permission to check for
//  * @returns Boolean indicating if the user has the specified permission
//  */
// export function hasPermission(permission: string): boolean {
//   const { profile, isLoading } = GetProfile();

//   // During initial loading, be permissive to avoid flickering UI
//   if (isLoading) {
//     return true;
//   }

//   // Check if user has roles (new structure) or role (old structure)
//   const roles =
//     profile?.data?.roles || (profile?.data?.role ? [profile.data.role] : []);

//   if (!roles.length) {
//     return false;
//   }

//   // Check if any of the user's roles has the required permission
//   return roles.some((role: { permissions: { action: string }[] }) =>
//     role?.permissions?.some(
//       (item: { action: string }) => item.action === permission
//     )
//   );
// }

// /**
//  * Get the current user's roles
//  * @returns Array of user's roles or empty array if not available
//  */
// export function getUserRoles(): Array<{
//   name: string;
//   permissions: Array<{ name: string }>;
// }> {
//   const { profile } = GetProfile();

//   // Check if user has roles (new structure) or role (old structure)
//   return (
//     profile?.data?.roles || (profile?.data?.role ? [profile.data.role] : [])
//   );
// }
