'use client';

import React, { useState } from 'react';
import { ParsedData } from '@/lib/data-analysis/parser';
import { Input } from '@/components/ui/input';
import { Search } from 'lucide-react';
import Pagination from '@/components/common/pagination';
import { useDebounce } from '@/hooks/useDebounce';

interface DataTableProps {
  data: ParsedData;
}

const DataTable: React.FC<DataTableProps> = ({ data }) => {
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(10);
  const [searchTerm, setSearchTerm] = useState('');
  const debouncedSearchTerm = useDebounce(searchTerm, 300);

  if (!data || !data.rows || !data.columns) {
    return <div className="text-center py-8">No data available</div>;
  }

  // Filter rows based on search term
  const filteredRows = data.rows.filter((row) => {
    if (!debouncedSearchTerm) return true;

    // Search across all columns
    return Object.values(row).some((value) =>
      String(value).toLowerCase().includes(debouncedSearchTerm.toLowerCase())
    );
  });

  // Paginate the filtered rows
  const totalPages = Math.ceil(filteredRows.length / pageSize);
  const paginatedRows = filteredRows.slice(
    (currentPage - 1) * pageSize,
    currentPage * pageSize
  );

  const handlePageChange = (pageNumber: number) => {
    setCurrentPage(pageNumber);
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-bold">Data Preview</h2>
        <div className="relative w-64">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search data..."
            className="pl-8"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
      </div>

      <div className="overflow-x-auto rounded-lg border border-gray-200 dark:border-gray-700">
        <table className="w-full text-sm text-left">
          <thead className="text-xs text-gray-700 uppercase bg-gray-100 dark:bg-gray-700 dark:text-gray-400">
            <tr>
              {data.columns.map((column, index) => (
                <th key={index} className="px-4 py-3">
                  {column.name}
                  <span className="ml-1 text-xs font-normal text-gray-500">
                    ({column.type})
                  </span>
                </th>
              ))}
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
            {paginatedRows.length > 0 ? (
              paginatedRows.map((row, rowIndex) => (
                <tr
                  key={rowIndex}
                  className="bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700"
                >
                  {data.columns.map((column, colIndex) => (
                    <td key={colIndex} className="px-4 py-3 whitespace-nowrap">
                      {row[column.name] !== undefined &&
                      row[column.name] !== null ? (
                        String(row[column.name])
                      ) : (
                        <span className="text-gray-400">—</span>
                      )}
                    </td>
                  ))}
                </tr>
              ))
            ) : (
              <tr>
                <td
                  colSpan={data.columns.length}
                  className="px-4 py-8 text-center text-gray-500"
                >
                  No matching data found
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      {filteredRows.length > 0 && (
        <div className="flex justify-between items-center text-sm text-gray-500">
          <div>
            Showing {paginatedRows.length} of {filteredRows.length} rows
          </div>
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={handlePageChange}
          />
        </div>
      )}
    </div>
  );
};

export default DataTable;
