'use client';

import {
  createContext,
  useContext,
  useState,
  ReactNode,
  useEffect,
} from 'react';

interface PermissionsContextType {
  storedPermissions: string[];
  setStoredPermissions: (permissions: string[]) => void;
  hasPermissionsChanged: (currentPermissions: string[]) => boolean;
}

const PermissionsContext = createContext<PermissionsContextType | undefined>(
  undefined
);

export function PermissionsProvider({ children }: { children: ReactNode }) {
  const [storedPermissions, setStoredPermissions] = useState<string[]>([]);

  // Initialize from sessionStorage if available (for page navigation within the app)
  useEffect(() => {
    try {
      const stored = sessionStorage.getItem('user_permissions');
      if (stored && !storedPermissions.length) {
        setStoredPermissions(JSON.parse(stored));
      }
    } catch (e) {
      console.error('Error reading permissions from sessionStorage', e);
    }
  }, [storedPermissions.length]);

  const hasPermissionsChanged = (currentPermissions: string[]): boolean => {
    // If we don't have stored permissions yet, no change has occurred
    if (!storedPermissions.length && currentPermissions.length) return false;

    const sortedCurrent = [...currentPermissions].sort();
    const sortedStored = [...storedPermissions].sort();

    return (
      sortedStored.length !== sortedCurrent.length ||
      sortedStored.some((v, i) => v !== sortedCurrent[i])
    );
  };

  return (
    <PermissionsContext.Provider
      value={{
        storedPermissions,
        setStoredPermissions,
        hasPermissionsChanged,
      }}
    >
      {children}
    </PermissionsContext.Provider>
  );
}

export function usePermissions() {
  const context = useContext(PermissionsContext);
  if (context === undefined) {
    throw new Error('usePermissions must be used within a PermissionsProvider');
  }
  return context;
}
