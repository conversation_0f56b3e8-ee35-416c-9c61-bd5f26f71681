'use client';

import React, { useState, useEffect } from 'react';
import { useSearchAndPagination } from '@/hooks/useSearchAndPagination';
import Pagination from '@/components/common/pagination';
import dayjs from 'dayjs';
import { Loader2, Ellipsis, Search } from 'lucide-react';
import { StatusBadge } from '@/components/common/status-badge';
import { EmptyState, LoadingState } from '@/components/common/dataState';
import { Input } from '@/components/ui/input';
import DateRangeFilter from '@/components/common/date-range-filter';
import { GetPatients } from '@/api/crm/data';
import { Patient } from '@/components/crm/types';
import Details from './details';
import Create from './create';
import { useDebounce } from '@/hooks/useDebounce';

interface PatientTableProps {
  openCreate: boolean;
  setOpenCreate: React.Dispatch<React.SetStateAction<boolean>>;
}

export default function PatientTable({
  openCreate,
  setOpenCreate,
}: PatientTableProps) {
  const {
    searchTerm,
    debouncedSearchTerm,
    handleSearchChange,
    currentPage,
    pageSize,
    handlePageChange,
    queryParam,
    setQueryParam,
  } = useSearchAndPagination({ initialPageSize: 10 });

  const [statusFilter, setStatusFilter] = useState('');
  const [startDate, setStartDate] = useState<Date | null>(null);
  const [endDate, setEndDate] = useState<Date | null>(null);
  const [openDetails, setOpenDetails] = useState(false);
  const [selectedPatient, setSelectedPatient] = useState<Patient | null>(null);

  const handleEventFromModal = (patient: Patient) => {
    setSelectedPatient(patient);
    setOpenDetails(true);
  };

  useEffect(() => {
    let params = [];

    if (debouncedSearchTerm) {
      params.push(`search=${debouncedSearchTerm}`);
    }

    if (statusFilter) {
      params.push(`status=${statusFilter}`);
    }

    if (startDate) {
      const formattedStartDate = dayjs(startDate).format('YYYY-MM-DD');
      params.push(`startDate=${formattedStartDate}`);
    }

    if (endDate) {
      const formattedEndDate = dayjs(endDate).format('YYYY-MM-DD');
      params.push(`endDate=${formattedEndDate}`);
    }

    setQueryParam(params.join('&'));
  }, [debouncedSearchTerm, statusFilter, startDate, endDate, setQueryParam]);

  const { patients, patientsLoading, mutate } = GetPatients(
    `?page=${currentPage}&limit=${pageSize}${queryParam ? `&${queryParam}` : ''}`
  );
  const patientData = patients?.data?.patients;
  const totalPages = patients?.data?.totalPages ?? 0;

  return (
    <div className="w-full">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4 gap-4">
        <div className="relative w-full sm:w-64">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search patients..."
            className="pl-8"
            value={searchTerm}
            onChange={handleSearchChange}
          />
        </div>
        <div className="flex flex-col sm:flex-row gap-2 w-full sm:w-auto">
          <div className="flex gap-2">
            <button
              onClick={() => setStatusFilter('active')}
              className={`px-3 py-1 text-xs rounded-full ${
                statusFilter === 'active'
                  ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'
                  : 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300'
              }`}
            >
              Active
            </button>
            <button
              onClick={() => setStatusFilter('inactive')}
              className={`px-3 py-1 text-xs rounded-full ${
                statusFilter === 'inactive'
                  ? 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400'
                  : 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300'
              }`}
            >
              Inactive
            </button>
            <button
              onClick={() => setStatusFilter('')}
              className={`px-3 py-1 text-xs rounded-full ${
                statusFilter === ''
                  ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400'
                  : 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300'
              }`}
            >
              All
            </button>
          </div>
          <DateRangeFilter
            startDate={startDate}
            endDate={endDate}
            onStartDateChange={setStartDate}
            onEndDateChange={setEndDate}
            onClear={() => {
              setStartDate(null);
              setEndDate(null);
            }}
          />
        </div>
      </div>

      {patientsLoading ? (
        <LoadingState />
      ) : patientData && patientData.length > 0 ? (
        <div className="overflow-x-auto">
          <table className="w-full border-collapse">
            <thead>
              <tr className="bg-gray-100 dark:bg-[#1F1F23] text-xs text-gray-500 dark:text-gray-400">
                <th className="table-style">#</th>
                <th className="table-style">Patient ID</th>
                <th className="table-style">Name</th>
                <th className="table-style">Gender</th>
                <th className="table-style">Age</th>
                <th className="table-style">Phone</th>
                <th className="table-style">Blood Type</th>
                <th className="table-style">Last Visit</th>
                <th className="table-style">Status</th>
                <th className="table-style">Actions</th>
              </tr>
            </thead>
            <tbody className="relative overflow-x-auto whitespace-nowrap text-sm shadow-md">
              {patientData?.map((patient: Patient, index: number) => {
                // Calculate age from date of birth
                const age = patient.dateOfBirth
                  ? dayjs().diff(dayjs(patient.dateOfBirth), 'year')
                  : 'N/A';

                return (
                  <tr
                    className="text-xs text-[#062A55] dark:text-white"
                    key={patient.id}
                  >
                    <td className="table-style">
                      {currentPage === 1
                        ? index + 1
                        : (currentPage - 1) * pageSize + (index + 1)}
                    </td>
                    <td className="table-style">
                      P-{String(patient.id).padStart(6, '0')}
                    </td>
                    <td className="table-style">
                      {patient.title ? `${patient.title} ` : ''}
                      {patient.firstName} {patient.lastName}
                    </td>
                    <td className="table-style">
                      {patient.gender
                        ? patient.gender.charAt(0).toUpperCase() +
                          patient.gender.slice(1)
                        : 'N/A'}
                    </td>
                    <td className="table-style">{age}</td>
                    <td className="table-style">{patient.phoneNumber}</td>
                    <td className="table-style">
                      {patient.bloodType || 'N/A'}
                    </td>
                    <td className="table-style">
                      {patient.lastVisitDate
                        ? dayjs(patient.lastVisitDate).format('MMM D, YYYY')
                        : 'Never'}
                    </td>
                    <td className="table-style">
                      <StatusBadge status={patient.status} />
                    </td>
                    <td className="table-style">
                      <Ellipsis
                        onClick={() => handleEventFromModal(patient)}
                        className="w-4 h-4 cursor-pointer"
                      />
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
          <div className="mt-4">
            <Pagination
              currentPage={currentPage}
              totalPages={totalPages}
              onPageChange={handlePageChange}
            />
          </div>
        </div>
      ) : (
        <EmptyState message="No patients found" />
      )}

      {selectedPatient && (
        <Details
          open={openDetails}
          setOpen={setOpenDetails}
          data={selectedPatient}
          mutate={mutate}
        />
      )}

      <Create open={openCreate} setOpen={setOpenCreate} mutate={mutate} />
    </div>
  );
}
