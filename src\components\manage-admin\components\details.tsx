// 'use client';

// import React, { useState } from 'react';
// import { Modal } from '@/components/common/modal';
// import { ModalProps } from '@/components/manage-admin/types';
// import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
// import {
//   User,
//   Shield,
//   Clock,
//   Mail,
//   Calendar,
//   CheckCircle,
//   XCircle,
// } from 'lucide-react';
// import { Badge } from '@/components/ui/badge';
// import { Button } from '@/components/ui/button';
// import dayjs from 'dayjs';
// import { GetAdmin } from '@/api/data';
// import { hasPermission, permissions } from '@/lib/types/permissions';
// import ChangeAdminPassword from './change-password';

// interface TabProps {
//   label: string;
//   icon: React.ReactNode;
//   content: React.ReactNode;
// }

// const Details: React.FC<ModalProps> = ({ open, setOpen, mutate, data }) => {
//   const [activeTab, setActiveTab] = useState(0);
//   const [showPasswordModal, setShowPasswordModal] = useState(false);
//   const { profile } = GetAdmin();

//   // Check permissions
//   const permitUpdate = hasPermission(permissions.admin_update);
//   const isSuperAdmin =
//     profile?.data?.role?.name?.toLowerCase() === 'superadmin';

//   if (!data) return null;

//   const formatDate = (date: string | Date) => {
//     if (!date) return 'N/A';
//     return dayjs(date).format('MMMM D, YYYY h:mm A');
//   };

//   // Personal Info Tab Content
//   const PersonalInfo = () => (
//     <div className="space-y-4">
//       <div className="flex flex-col space-y-1">
//         <span className="text-sm font-medium text-muted-foreground">
//           Full Name
//         </span>
//         <span className="font-semibold">{data.fullName}</span>
//       </div>

//       <div className="flex flex-col space-y-1">
//         <span className="text-sm font-medium text-muted-foreground">
//           Designation
//         </span>
//         <div className="flex items-center gap-2">
//           <span>{data.designation}</span>
//         </div>
//       </div>

//       <div className="flex flex-col space-y-1">
//         <span className="text-sm font-medium text-muted-foreground">
//           Email Address
//         </span>
//         <div className="flex items-center gap-2">
//           <Mail className="h-4 w-4 text-muted-foreground" />
//           <span>{data.emailAddress}</span>
//         </div>
//       </div>

//       <div className="flex flex-col space-y-1">
//         <span className="text-sm font-medium text-muted-foreground">Role</span>
//         <Badge variant="outline" className="w-fit">
//           {data.role?.name}
//         </Badge>
//       </div>
//     </div>
//   );

//   // Account Info Tab Content
//   const AccountInfo = () => (
//     <div className="space-y-4">
//       <div className="flex flex-col space-y-1">
//         <span className="text-sm font-medium text-muted-foreground">
//           Account Status
//         </span>
//         <Badge
//           variant={data.isActive ? 'success' : 'destructive'}
//           className="w-fit"
//         >
//           {data.isActive ? 'Active' : 'Inactive'}
//         </Badge>
//       </div>

//       <div className="flex flex-col space-y-1">
//         <span className="text-sm font-medium text-muted-foreground">
//           Account Created
//         </span>
//         <div className="flex items-center gap-2">
//           <Calendar className="h-4 w-4 text-muted-foreground" />
//           <span>{formatDate(data.createdAt)}</span>
//         </div>
//       </div>

//       <div className="flex flex-col space-y-1">
//         <span className="text-sm font-medium text-muted-foreground">
//           Last Updated
//         </span>
//         <div className="flex items-center gap-2">
//           <Clock className="h-4 w-4 text-muted-foreground" />
//           <span>{formatDate(data.updatedAt)}</span>
//         </div>
//       </div>

//       <div className="flex flex-col space-y-1">
//         <span className="text-sm font-medium text-muted-foreground">
//           Last Login
//         </span>
//         <div className="flex items-center gap-2">
//           <Clock className="h-4 w-4 text-muted-foreground" />
//           <span>{formatDate(data.lastLogin)}</span>
//         </div>
//       </div>

//       <div className="flex flex-col space-y-1">
//         <span className="text-sm font-medium text-muted-foreground">
//           Date Reset Password
//         </span>
//         <div className="flex items-center gap-2">
//           <Clock className="h-4 w-4 text-muted-foreground" />
//           <span>{formatDate(data.dateResetPassword)}</span>
//         </div>
//       </div>

//       {permitUpdate && isSuperAdmin && (
//         <div className="pt-4">
//           <Button
//             onClick={() => setShowPasswordModal(true)}
//             variant="outline"
//             className="w-full"
//           >
//             Change Password
//           </Button>
//         </div>
//       )}
//     </div>
//   );

//   // Define tabs
//   const tabs: TabProps[] = [
//     {
//       label: 'Personal Info',
//       icon: <User className="h-4 w-4" />,
//       content: <PersonalInfo />,
//     },
//     {
//       label: 'Account Info',
//       icon: <Shield className="h-4 w-4" />,
//       content: <AccountInfo />,
//     },
//   ];

//   return (
//     <>
//       <Modal
//         open={open}
//         setOpen={setOpen}
//         title="Admin Details"
//         description={`Admin information for ${data.fullName || 'Unknown'}`}
//         size="md"
//       >
//         <Tabs
//           defaultValue="0"
//           className="w-full"
//           onValueChange={(value) => setActiveTab(parseInt(value))}
//         >
//           <TabsList className="grid grid-cols-2 mb-4">
//             {tabs.map((tab, index) => (
//               <TabsTrigger
//                 key={index}
//                 value={index.toString()}
//                 className="flex items-center gap-2"
//               >
//                 {tab.icon}
//                 {tab.label}
//               </TabsTrigger>
//             ))}
//           </TabsList>

//           {tabs.map((tab, index) => (
//             <TabsContent key={index} value={index.toString()} className="mt-0">
//               {tab.content}
//             </TabsContent>
//           ))}
//         </Tabs>
//       </Modal>

//       {/* Change Password Modal */}
//       {data && (
//         <ChangeAdminPassword
//           open={showPasswordModal}
//           setOpen={setShowPasswordModal}
//           adminId={data.id}
//           adminName={data.fullName}
//         />
//       )}
//     </>
//   );
// };

// export default Details;
