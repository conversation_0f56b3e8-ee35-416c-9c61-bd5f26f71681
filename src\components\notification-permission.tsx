'use client';

import { useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';
import { isNotificationSupported } from '@/lib/notification';
import { toast } from 'sonner';
import { X } from 'lucide-react';

export function NotificationPermission() {
  const [permission, setPermission] = useState<NotificationPermission | null>(
    null
  );
  const [supported, setSupported] = useState(false);
  const [isDismissed, setIsDismissed] = useState(false);

  useEffect(() => {
    // Check if notifications are supported
    const notificationsSupported = isNotificationSupported();
    setSupported(notificationsSupported);

    if (notificationsSupported) {
      setPermission(Notification.permission);
    }

    // Check if user has dismissed the notification for this session
    const dismissed = sessionStorage.getItem('notificationPermissionDismissed');
    if (dismissed === 'true') {
      setIsDismissed(true);
    }
  }, []);

  const handleRequestPermission = async () => {
    try {
      // Request permission directly using the Notification API
      const result = await Notification.requestPermission();
      setPermission(result);

      if (result === 'granted') {
        toast.success('Notification permission granted!');
        setIsDismissed(true);
        sessionStorage.setItem('notificationPermissionDismissed', 'true');
      } else if (result === 'denied') {
        // Show instructions for enabling in browser settings
        toast.error(
          'Permission denied. Please enable notifications in your browser settings.',
          { duration: 5000 }
        );
      } else {
        toast.info('Notification permission was dismissed');
      }
    } catch (error) {
      console.error('Error requesting notification permission:', error);
      toast.error('Failed to request notification permission');
    }
  };

  const handleDismiss = () => {
    setIsDismissed(true);
    sessionStorage.setItem('notificationPermissionDismissed', 'true');
  };

  if (!supported) {
    return null;
  }

  if (permission === 'granted' || isDismissed) {
    return null;
  }

  return (
    <div className="fixed bottom-4 right-4 p-4 bg-background border rounded-lg shadow-lg z-50 max-w-sm">
      <div className="flex justify-between items-start mb-2">
        <h3 className="font-medium">Enable Notifications</h3>
        <Button
          variant="ghost"
          size="sm"
          onClick={handleDismiss}
          className="h-6 w-6 p-0 hover:bg-muted"
        >
          <X className="h-4 w-4" />
        </Button>
      </div>
      <p className="text-sm text-muted-foreground mb-4">
        Get real-time updates and important alerts directly to your device.
      </p>
      <div className="flex gap-2">
        <Button onClick={handleRequestPermission} size="sm">
          Enable
        </Button>
        <Button onClick={handleDismiss} variant="outline" size="sm">
          Maybe Later
        </Button>
      </div>
      {permission === 'denied' && (
        <div className="mt-2 text-xs text-amber-500">
          Notifications are blocked. Please enable them in your browser
          settings.
        </div>
      )}
    </div>
  );
}
