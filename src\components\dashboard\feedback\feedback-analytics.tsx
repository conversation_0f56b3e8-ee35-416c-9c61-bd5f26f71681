'use client';

import { useState } from 'react';
import {
  TrendingUp,
  TrendingDown,
  Star,
  MessageCircle,
  Loader2,
  XCircle,
} from 'lucide-react';
import {
  Bar,
  Bar<PERSON>hart,
  CartesianGrid,
  XAxis,
  YAxis,
  Tooltip,
  Cell,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
} from 'recharts';
import YearSelect from '../../common/year-select';
import { GetFeedbackAnalytics } from '@/api/analytics';
import { formatValue } from '@/lib/utils';

import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from '@/components/ui/chart';

const chartConfig = {
  feedbacks: {
    label: 'Feedbacks',
    theme: {
      light: '#BD9A3D',
      dark: '#BD9A3D',
    },
  },
  inpatient: {
    label: 'Inpatient',
    theme: {
      light: '#2196F3',
      dark: '#2196F3',
    },
  },
  outpatient: {
    label: 'Outpatient',
    theme: {
      light: '#1A1A1A',
      dark: '#1A1A1A',
    },
  },
  ratings: {
    label: 'Ratings',
    theme: {
      light: '#BD9A3D',
      dark: '#BD9A3D',
    },
  },
} satisfies ChartConfig;

const COLORS = ['#BD9A3D', '#2196F3', '#1A1A1A'];

// Helper function to get rating text
const getRatingText = (rating: number): string => {
  const ratingText = {
    1: 'Very Dissatisfied',
    2: 'Dissatisfied',
    3: 'Neutral',
    4: 'Satisfied',
    5: 'Very Satisfied',
  };
  return ratingText[rating as keyof typeof ratingText] || '';
};

export function FeedbackAnalytics() {
  const [year, setYear] = useState<string>(new Date().getFullYear().toString());

  const handleYearChange = (selectedYear: string) => {
    setYear(selectedYear);
  };

  // Use the real API function with the year parameter
  const { feedbackStats, feedbackStatsLoading, feedbackStatsError } =
    GetFeedbackAnalytics(`year=${year}`);

  // Handle loading state
  if (feedbackStatsLoading) {
    return (
      <div className="flex flex-col items-center justify-center h-[400px]">
        <Loader2
          className="w-8 h-8 animate-spin"
          style={{ color: '#BD9A3D' }}
        />
        <p className="text-muted-foreground mt-4">
          Loading feedback analytics...
        </p>
      </div>
    );
  }

  // Handle error state
  if (feedbackStatsError) {
    return (
      <div className="flex flex-col items-center justify-center h-[400px]">
        <XCircle className="w-8 h-8 mb-4" style={{ color: '#2196F3' }} />
        <p>Error loading feedback analytics. Please try again later.</p>
      </div>
    );
  }

  // If no data is available
  if (!feedbackStats || !feedbackStats.data) {
    return (
      <div className="flex flex-col items-center justify-center h-[400px]">
        <MessageCircle className="w-8 h-8 mb-4" style={{ color: '#1A1A1A' }} />
        <p>No feedback data available for the selected period.</p>
      </div>
    );
  }

  // Use the data from the API response
  const data = feedbackStats.data.data;

  const renderStars = (rating: number) => {
    const stars = [];
    for (let i = 0; i < 5; i++) {
      stars.push(
        <Star
          key={i}
          className={`w-4 h-4 ${i < rating ? '' : 'text-gray-300'}`}
          style={i < rating ? { color: '#BD9A3D', fill: '#BD9A3D' } : {}}
        />
      );
    }
    return <div className="flex">{stars}</div>;
  };

  return (
    <div className="space-y-4">
      <div className="flex flex-wrap gap-4">
        {/* Summary Cards */}
        <Card className="w-full md:w-[calc(33.33%-1rem)]">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">
              Total Feedbacks
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {data?.totalFeedbacks || 0}
            </div>
            <div className="flex items-center mt-1 text-xs">
              {data.recentTrend?.direction === 'up' ? (
                <TrendingUp
                  className="w-3 h-3 mr-1"
                  style={{ color: '#BD9A3D' }}
                />
              ) : (
                <TrendingDown
                  className="w-3 h-3 mr-1"
                  style={{ color: '#2196F3' }}
                />
              )}
              <span
                style={{
                  color:
                    data.recentTrend?.direction === 'up'
                      ? '#BD9A3D'
                      : '#2196F3',
                }}
              >
                {data.recentTrend?.percent || 0}% from last month
              </span>
            </div>
          </CardContent>
        </Card>

        <Card className="w-full md:w-[calc(33.33%-1rem)]">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">
              Average Rating
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {data.averageRating ? data.averageRating.toFixed(1) : '0.0'}
            </div>
            <div className="flex items-center mt-1">
              {renderStars(Math.round(data.averageRating || 0))}
            </div>
          </CardContent>
        </Card>

        <Card className="w-full md:w-[calc(33.33%-1rem)]">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">
              Most Common Type
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {data.feedbackTypes && data.feedbackTypes.length > 0
                ? data.feedbackTypes.sort(
                    (a: any, b: any) => b.count - a.count
                  )[0].type
                : 'N/A'}
            </div>
            <div className="flex items-center mt-1 text-xs">
              <MessageCircle
                className="w-3 h-3 mr-1"
                style={{ color: '#2196F3' }}
              />
              <span className="text-muted-foreground">
                {data.feedbackTypes && data.feedbackTypes.length > 0
                  ? `${
                      data.feedbackTypes.sort(
                        (a: any, b: any) => b.count - a.count
                      )[0].count
                    } feedbacks`
                  : 'No data'}
              </span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Card>
          <CardHeader>
            <div className="flex flex-wrap gap-4 justify-between">
              <div>
                <CardTitle>Monthly Feedback</CardTitle>
                <CardDescription>Feedback submissions by month</CardDescription>
              </div>
              <div>
                <YearSelect onChange={handleYearChange} />
              </div>
            </div>
          </CardHeader>
          <CardContent>
            {data.monthlyData && data.monthlyData.length > 0 ? (
              <ChartContainer
                className="aspect-auto h-[250px]"
                config={chartConfig}
              >
                <BarChart data={data.monthlyData}>
                  <CartesianGrid vertical={false} strokeDasharray="3 3" />
                  <XAxis
                    dataKey="month"
                    tickLine={false}
                    tickMargin={10}
                    axisLine={false}
                    tickFormatter={(value) => value.slice(0, 3)}
                  />
                  <YAxis axisLine={false} tickLine={false} tickMargin={10} />
                  <Tooltip
                    formatter={(value) => [`${value} feedbacks`, 'Count']}
                    labelFormatter={(label) => `Month: ${label}`}
                  />
                  <Bar
                    dataKey="count"
                    fill="var(--color-feedbacks)"
                    radius={4}
                    name="Feedbacks"
                  />
                </BarChart>
              </ChartContainer>
            ) : (
              <div className="flex items-center justify-center h-[250px] text-muted-foreground">
                <MessageCircle className="w-8 h-8 mr-2" />
                <span>No monthly data available</span>
              </div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Feedback Types</CardTitle>
            <CardDescription>Distribution by feedback type</CardDescription>
          </CardHeader>
          <CardContent>
            {data.feedbackTypes && data.feedbackTypes.length > 0 ? (
              <ChartContainer
                className="aspect-auto h-[250px]"
                config={chartConfig}
              >
                <PieChart>
                  <Pie
                    data={data.feedbackTypes}
                    dataKey="count"
                    nameKey="type"
                    cx="50%"
                    cy="50%"
                    outerRadius={80}
                    label={({ type, count }) => `${type}: ${count}`}
                    labelLine={false}
                  >
                    {data.feedbackTypes.map((_: any, index: number) => (
                      <Cell
                        key={`cell-${index}`}
                        fill={COLORS[index % COLORS.length]}
                      />
                    ))}
                  </Pie>
                  <Tooltip
                    formatter={(value) => [`${value} feedbacks`, 'Count']}
                    labelFormatter={(label) => `Type: ${label}`}
                  />
                  <Legend />
                </PieChart>
              </ChartContainer>
            ) : (
              <div className="flex items-center justify-center h-[250px] text-muted-foreground">
                <MessageCircle className="w-8 h-8 mr-2" />
                <span>No feedback type data available</span>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Rating Distribution Chart */}
      <Card>
        <CardHeader>
          <CardTitle>Rating Distribution</CardTitle>
          <CardDescription>
            Distribution of feedback by star rating
          </CardDescription>
        </CardHeader>
        <CardContent>
          {data.ratingDistribution && data.ratingDistribution.length > 0 ? (
            <ChartContainer
              className="aspect-auto h-[250px]"
              config={chartConfig}
            >
              <BarChart data={data.ratingDistribution}>
                <CartesianGrid vertical={false} strokeDasharray="3 3" />
                <XAxis
                  dataKey="rating"
                  tickLine={false}
                  tickMargin={10}
                  axisLine={false}
                  tickFormatter={(value) =>
                    `${value} ★ (${getRatingText(Number(value))})`
                  }
                />
                <YAxis axisLine={false} tickLine={false} tickMargin={10} />
                <Tooltip
                  formatter={(value) => [`${value} feedbacks`, 'Count']}
                  labelFormatter={(label) =>
                    `${label} Star Rating (${getRatingText(Number(label))})`
                  }
                />
                <Bar
                  dataKey="count"
                  fill="var(--color-ratings)"
                  radius={4}
                  name="Ratings"
                >
                  {data.ratingDistribution.map((entry: any, index: number) => {
                    // Use different colors based on rating
                    let color;
                    if (entry.rating <= 2) {
                      color = '#1A1A1A'; // Low ratings
                    } else if (entry.rating === 3) {
                      color = '#2196F3'; // Medium ratings
                    } else {
                      color = '#BD9A3D'; // High ratings
                    }
                    return <Cell key={`cell-${index}`} fill={color} />;
                  })}
                </Bar>
              </BarChart>
            </ChartContainer>
          ) : (
            <div className="flex items-center justify-center h-[250px] text-muted-foreground">
              <Star className="w-8 h-8 mr-2" />
              <span>No rating distribution data available</span>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
