export interface ModalProps {
  setOpen: (open: boolean) => void;
  open: boolean;
  mutate?: () => void;
  data?: any;
}

export interface PatientInteraction {
  id: number;
  patientId: number;
  patientName: string;
  channel: string;
  subject: string;
  content: string;
  status: 'pending' | 'completed';
  direction: 'inbound' | 'outbound';
  contactInfo?: string;
  tags?: string;
  followUpRequired?: boolean;
  followUpDate?: string;
  parentInteractionId?: number;
  createdAt: string;
  createdBy?: string;
}

export interface Appointment {
  id: number;
  patientId: number;
  patientName: string;
  type:
    | 'appointment'
    | 'call'
    | 'email'
    | 'lab_result'
    | 'prescription'
    | 'other';
  subject: string;
  description: string;
  status: 'scheduled' | 'completed' | 'cancelled' | 'no_show';
  priority: 'routine' | 'urgent' | 'emergency';
  appointmentDate?: string;
  department?: string;
  doctorId?: number;
  doctorName?: string;
  symptoms?: string;
  diagnosis?: string;
  treatment?: string;
  followUpRequired?: boolean;
  followUpDate?: string;
  createdAt: string;
  createdBy?: string;
}

export interface Patient {
  id: number;
  title?: string;
  firstName: string;
  lastName: string;
  emailAddress: string;
  phoneNumber: string;
  address?: string;
  city?: string;
  state?: string;
  zipCode?: string;
  dateOfBirth?: string;
  gender?: string;
  bloodType?: string;
  allergies?: string;
  medicalHistory?: string;
  emergencyContact?: string;
  emergencyContactPhone?: string;
  insuranceProvider?: string;
  insuranceNumber?: string;
  notes?: string;
  status: 'active' | 'inactive';
  lastVisitDate?: string;
  createdAt: string;
  createdBy?: string;
}
