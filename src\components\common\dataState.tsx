import { Loader2 } from 'lucide-react';

export function LoadingState() {
  return (
    <div className="dark:text-white py-12 px-4 flex items-center justify-center gap-2">
      <Loader2 className="animate-spin w-6 h-6 " />
      <span>Loading...</span>
    </div>
  );
}

export function EmptyState({
  message = 'No data available',
}: {
  message?: string;
}) {
  return (
    <div className="border text-sm py-12 text-center dark:text-white">
      {message}
    </div>
  );
}
