'use client';

import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import {
  FileSpreadsheet,
  BarChart,
  Table,
  Download,
  Loader2,
  Settings,
} from 'lucide-react';
import FileUpload from './file-upload';
import DataTable from './data-table';
import DataVisualizations from './visualizations';
import {
  parseFile,
  analyzeData,
  ParsedData,
  AnalysisResult,
} from '@/lib/data-analysis/parser';
import {
  generatePdfReport,
  downloadPdf,
  PdfExportOptions,
} from '@/lib/data-analysis/pdf-export';
import { toast } from 'sonner';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';

export default function DataAnalysisModule() {
  const [file, setFile] = useState<File | null>(null);
  const [parsedData, setParsedData] = useState<ParsedData | null>(null);
  const [analysisResult, setAnalysisResult] = useState<AnalysisResult | null>(
    null
  );
  const [activeTab, setActiveTab] = useState('upload');
  const [isLoading, setIsLoading] = useState(false);
  const [isExporting, setIsExporting] = useState(false);

  const handleFileChange = (selectedFile: File | null) => {
    setFile(selectedFile);

    // Reset data when file changes
    if (selectedFile === null) {
      setParsedData(null);
      setAnalysisResult(null);
      setActiveTab('upload');
    }
  };

  const handleAnalyzeData = async () => {
    if (!file) {
      toast.error('Please upload a file first');
      return;
    }

    try {
      setIsLoading(true);

      // Parse the file
      const data = await parseFile(file);
      setParsedData(data);

      // Analyze the data
      const analysis = analyzeData(data);
      setAnalysisResult(analysis);

      // Switch to the visualization tab
      setActiveTab('visualize');

      toast.success('Data analysis completed successfully');
    } catch (error) {
      console.error('Error analyzing data:', error);
      toast.error(
        'Failed to analyze data. Please check the file format and try again.'
      );
    } finally {
      setIsLoading(false);
    }
  };

  const handleExportPdf = async () => {
    if (!parsedData || !analysisResult) {
      toast.error('No data to export');
      return;
    }

    try {
      setIsExporting(true);

      const options: PdfExportOptions = {
        title: `Analysis Report - ${file?.name || 'Data'}`,
        includeDataTable: true,
        includeCharts: true,
        includeStatistics: true,
        maxRows: 100,
      };

      const pdfBlob = await generatePdfReport(
        parsedData,
        analysisResult,
        options
      );
      downloadPdf(
        pdfBlob,
        `${file?.name.split('.')[0] || 'data'}-analysis-report.pdf`
      );

      toast.success('PDF report generated successfully');
    } catch (error) {
      console.error('Error generating PDF:', error);
      toast.error('Failed to generate PDF report');
    } finally {
      setIsExporting(false);
    }
  };

  return (
    <>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Data Analysis Tool</h1>

        <div className="flex gap-2">
          {parsedData && analysisResult && (
            <Button
              variant="outline"
              onClick={handleExportPdf}
              disabled={isExporting}
              className="flex items-center gap-2"
            >
              {isExporting ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Download className="h-4 w-4" />
              )}
              Export PDF
            </Button>
          )}
        </div>
      </div>

      <div className="bg-white dark:bg-[#0F0F12] rounded-xl p-6 shadow-sm border border-gray-200 dark:border-[#1F1F23]">
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="mb-6">
            <TabsTrigger value="upload" className="flex items-center gap-2">
              <FileSpreadsheet className="h-4 w-4" />
              Upload Data
            </TabsTrigger>
            <TabsTrigger
              value="data"
              className="flex items-center gap-2"
              disabled={!parsedData}
            >
              <Table className="h-4 w-4" />
              Data Preview
            </TabsTrigger>
            <TabsTrigger
              value="visualize"
              className="flex items-center gap-2"
              disabled={!analysisResult}
            >
              <BarChart className="h-4 w-4" />
              Visualizations
            </TabsTrigger>
            <TabsTrigger value="options" className="flex items-center gap-2">
              <Settings className="h-4 w-4" />
              Options
            </TabsTrigger>
          </TabsList>

          <TabsContent value="upload" className="space-y-6">
            <div className="max-w-2xl mx-auto">
              <FileUpload onFileChange={handleFileChange} />

              {file && (
                <div className="mt-6 flex justify-center">
                  <Button
                    onClick={handleAnalyzeData}
                    disabled={isLoading}
                    className="flex items-center gap-2"
                  >
                    {isLoading ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <BarChart className="h-4 w-4" />
                    )}
                    {isLoading ? 'Analyzing...' : 'Analyze Data'}
                  </Button>
                </div>
              )}
            </div>
          </TabsContent>

          <TabsContent value="data">
            {parsedData && <DataTable data={parsedData} />}
          </TabsContent>

          <TabsContent value="visualize">
            {analysisResult && (
              <DataVisualizations analysisResult={analysisResult} />
            )}
          </TabsContent>

          <TabsContent value="options">
            <div className="max-w-4xl mx-auto">
              <Card>
                <CardHeader>
                  <CardTitle>Visualization Options</CardTitle>
                  <CardDescription>
                    Configure how your data is visualized
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div>
                    <h3 className="text-lg font-medium mb-4">Chart Types</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <h4 className="text-md font-medium mb-2">
                          Categorical Data
                        </h4>
                        <p className="text-sm text-gray-500 mb-4">
                          The following chart types are available for
                          categorical data:
                        </p>
                        <ul className="list-disc pl-5 space-y-2">
                          <li>
                            Pie Chart - Shows distribution as parts of a whole
                          </li>
                          <li>Bar Chart - Compares values across categories</li>
                          <li>
                            Donut Chart - Similar to pie chart with center hole
                          </li>
                        </ul>
                      </div>
                      <div>
                        <h4 className="text-md font-medium mb-2">
                          Numerical Data
                        </h4>
                        <p className="text-sm text-gray-500 mb-4">
                          The following chart types are available for numerical
                          data:
                        </p>
                        <ul className="list-disc pl-5 space-y-2">
                          <li>Bar Chart - Shows comparison between values</li>
                          <li>Line Chart - Shows trends over a sequence</li>
                          <li>Area Chart - Emphasizes magnitude of change</li>
                          <li>
                            Scatter Plot - Shows relationship between values
                          </li>
                        </ul>
                      </div>
                      <div>
                        <h4 className="text-md font-medium mb-2">
                          Date/Time Data
                        </h4>
                        <p className="text-sm text-gray-500 mb-4">
                          The following chart types are available for date/time
                          data:
                        </p>
                        <ul className="list-disc pl-5 space-y-2">
                          <li>Line Chart - Shows trends over time</li>
                          <li>
                            Bar Chart - Compares values across time periods
                          </li>
                          <li>
                            Area Chart - Shows cumulative values over time
                          </li>
                          <li>
                            Composite Chart - Combines multiple chart types (bar
                            and line)
                          </li>
                        </ul>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h3 className="text-lg font-medium mb-4">Usage Tips</h3>
                    <ul className="list-disc pl-5 space-y-2">
                      <li>
                        Click on the chart type icons above each visualization
                        to change the chart type
                      </li>
                      <li>
                        Hover over data points to see detailed information
                      </li>
                      <li>Use pie charts for showing proportions of a whole</li>
                      <li>
                        Use bar charts for comparing values across categories
                      </li>
                      <li>
                        Use line charts for showing trends over time or sequence
                      </li>
                      <li>
                        Use scatter plots for showing correlations between
                        variables
                      </li>
                    </ul>
                  </div>

                  <div>
                    <h3 className="text-lg font-medium mb-4">Color Scheme</h3>
                    <p className="text-sm text-gray-500 mb-4">
                      The visualizations use the following color scheme:
                    </p>
                    <div className="flex flex-wrap gap-3">
                      {[
                        '#BD9A3D',
                        '#2196F3',
                        '#1A1A1A',
                        '#8884d8',
                        '#82ca9d',
                        '#ffc658',
                      ].map((color, index) => (
                        <div key={index} className="flex flex-col items-center">
                          <div
                            className="w-10 h-10 rounded-md border border-gray-200 dark:border-gray-700"
                            style={{ backgroundColor: color }}
                          />
                          <span className="text-xs mt-1">{color}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </>
  );
}
