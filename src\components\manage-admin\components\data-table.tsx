// import React, { useState, useEffect } from 'react';
// import Pagination from '@/components/common/pagination';
// import dayjs from 'dayjs';
// import { Loader2, Ellipsis } from 'lucide-react';
// import { StatusBadge } from '@/components/common/status-badge';
// import { GetAdminList } from '@/api/admin/data';
// import Create from './create';
// import Details from './details';
// import { hasPermission, permissions } from '@/lib/types/permissions';
// import { LoadingState, EmptyState } from '@/components/common/dataState';
// import { Input } from '@/components/ui/input';
// import { Search } from 'lucide-react';

// interface AdminProps {
//   openCreate: boolean;
//   setOpenCreate: React.Dispatch<React.SetStateAction<boolean>>;
// }

// const AdminTable: React.FC<AdminProps> = ({ openCreate, setOpenCreate }) => {
//   const [open, setOpen] = useState(false);
//   const [pageSize, setPageSize] = useState(10);
//   const [currentPage, setCurrentPage] = useState(1);
//   const [searchTerm, setSearchTerm] = useState('');
//   const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('');
//   const [queryParam, setQueryParam] = useState('');
//   const [detail, setDetail] = useState<any | null>(null);

//   const { admins, adminsLoading, mutate } = GetAdminList(
//     `?page=${currentPage}&limit=${pageSize}${queryParam ? `&${queryParam}` : ''}`
//   );
//   const adminData = admins?.data?.admins;
//   const totalPages = admins?.data?.totalPages ?? 0;

//   console.log(admins);

//   const permitUpdate = hasPermission(permissions.admin_update);

//   const handlePageChange = (pageNumber: number) => {
//     setCurrentPage(pageNumber);
//   };

//   // Handle opening the details modal
//   const handleEventFromModal = (admin: any) => {
//     setDetail(admin);
//     setOpen(true);
//   };

//   // Debounce search term to avoid too many API calls
//   useEffect(() => {
//     const timer = setTimeout(() => {
//       setDebouncedSearchTerm(searchTerm);
//     }, 500); // 500ms delay

//     return () => clearTimeout(timer);
//   }, [searchTerm]);

//   // Update query parameters when search term or date range changes
//   useEffect(() => {
//     let newQueryParam = '';

//     if (debouncedSearchTerm) {
//       newQueryParam = `search=${debouncedSearchTerm}`;
//     }

//     setCurrentPage(1); // Reset to first page on new search
//     setQueryParam(newQueryParam);
//   }, [debouncedSearchTerm]);

//   const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
//     setSearchTerm(e.target.value);
//   };

//   return (
//     <>
//       <div className="w-full overflow-x-auto rounded-t-lg shadow-md">
//         <div className="mb-4">
//           <div className="relative w-64">
//             <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
//               <Search className="w-4 h-4 text-gray-500" />
//             </div>
//             <Input
//               type="text"
//               placeholder="Search admin..."
//               className="pl-10 pr-4 py-2 w-full"
//               value={searchTerm}
//               onChange={handleSearchChange}
//             />
//           </div>
//         </div>
//         <table className="w-full table-auto text-left text-xs">
//           <thead className="bg-primary text-gray-100 dark:text-black">
//             <tr>
//               <th className="table-style">S/N</th>
//               <th className="table-style">Date Created</th>
//               <th className="table-style">Full Name</th>
//               <th className="table-style">Role</th>
//               <th className="table-style">Status</th>
//               <th className="table-style">Last Login</th>
//               <th className="table-style">Action</th>
//             </tr>
//           </thead>
//           <tbody className="relative overflow-x-auto whitespace-nowrap text-sm shadow-md">
//             {adminData?.map((admin: any, index: any) => (
//               <tr
//                 className="text-xs text-[#062A55] dark:text-white"
//                 key={admin.id}
//               >
//                 <td className="table-style">
//                   {currentPage === 1
//                     ? index + 1
//                     : (currentPage - 1) * pageSize + (index + 1)}
//                 </td>
//                 <td className="table-style">
//                   {dayjs(admin.createdAt).format('MMMM D, YYYY')}
//                 </td>
//                 <td className="table-style">{admin.fullName}</td>
//                 <td className="table-style">{admin.role.name}</td>
//                 <td className="table-style">
//                   {admin.isActive
//                     ? StatusBadge({ status: 'active' })
//                     : StatusBadge({ status: 'inactive' })}
//                 </td>
//                 <td className="table-style">
//                   {admin.lastLogin
//                     ? dayjs(admin.lastLogin).format('MMMM D, YYYY')
//                     : '--'}
//                 </td>
//                 <td className="table-style">
//                   <Ellipsis
//                     onClick={() => handleEventFromModal(admin)}
//                     className="w-4 h-4 cursor-pointer"
//                   />
//                 </td>
//               </tr>
//             ))}
//           </tbody>
//         </table>
//         {adminsLoading ? (
//           <LoadingState />
//         ) : totalPages === 0 ? (
//           <EmptyState />
//         ) : null}
//       </div>
//       {totalPages > 1 ? (
//         <Pagination
//           title="Admins"
//           totalPages={totalPages}
//           currentPage={currentPage}
//           onPageChange={handlePageChange}
//           totalCount={admins?.data?.totalCount}
//         />
//       ) : (
//         ''
//       )}

//       {/* Create Modal */}
//       <Create open={openCreate} setOpen={setOpenCreate} mutate={mutate} />

//       {/* Details Modal */}
//       {detail && (
//         <Details open={open} setOpen={setOpen} data={detail} mutate={mutate} />
//       )}
//     </>
//   );
// };

// export default AdminTable;
