// 'use client';

// import React, { useState } from 'react';
// import { Modal } from '@/components/common/modal';
// import { Form } from '@/components/ui/form';
// import { PasswordField } from '@/components/common/form';
// import { useForm } from 'react-hook-form';
// import { zodResolver } from '@hookform/resolvers/zod';
// import { z } from 'zod';
// import { myApi } from '@/api/fetcher';
// import { toast } from 'sonner';
// import { Alert, AlertDescription } from '@/components/ui/alert';
// import { AlertCircle } from 'lucide-react';

// // Schema for admin password change (no current password required)
// const adminPasswordSchema = z
//   .object({
//     newPassword: z
//       .string()
//       .min(8, { message: 'New password must be at least 8 characters' })
//       .max(50, { message: 'New password must be less than 50 characters' })
//       .refine((password) => /[A-Z]/.test(password), {
//         message: 'Password must contain at least one uppercase letter',
//       })
//       .refine((password) => /[a-z]/.test(password), {
//         message: 'Password must contain at least one lowercase letter',
//       })
//       .refine((password) => /[0-9]/.test(password), {
//         message: 'Password must contain at least one number',
//       })
//       .refine((password) => /[^A-Za-z0-9]/.test(password), {
//         message: 'Password must contain at least one special character',
//       }),
//     confirmPassword: z
//       .string()
//       .min(8, { message: 'Confirm password must be at least 8 characters' }),
//   })
//   .refine((data) => data.newPassword === data.confirmPassword, {
//     message: 'Passwords do not match',
//     path: ['confirmPassword'],
//   });

// type AdminPasswordFormValues = z.infer<typeof adminPasswordSchema>;

// interface ChangeAdminPasswordProps {
//   open: boolean;
//   setOpen: (open: boolean) => void;
//   adminId: number;
//   adminName: string;
// }

// const ChangeAdminPassword: React.FC<ChangeAdminPasswordProps> = ({
//   open,
//   setOpen,
//   adminId,
//   adminName,
// }) => {
//   const [isLoading, setIsLoading] = useState(false);
//   const [error, setError] = useState<string | null>(null);

//   const form = useForm<AdminPasswordFormValues>({
//     resolver: zodResolver(adminPasswordSchema),
//     defaultValues: {
//       newPassword: '',
//       confirmPassword: '',
//     },
//     mode: 'onChange',
//   });

//   const onSubmit = async (data: AdminPasswordFormValues) => {
//     try {
//       setIsLoading(true);
//       setError(null);

//       // Call the API to change the admin's password
//       const response = await myApi.post('/admin/reset-password', {
//         adminId: adminId,
//         newPassword: data.newPassword,
//       });

//       if (response.status === 200) {
//         toast.success('Password changed successfully');
//         setOpen(false);
//         form.reset();
//       }
//     } catch (error: any) {
//       setError(
//         error?.response?.data?.message ||
//           'Failed to change password. Please try again.'
//       );
//       toast.error('Failed to change password');
//     } finally {
//       setIsLoading(false);
//     }
//   };

//   return (
//     <Modal
//       open={open}
//       setOpen={setOpen}
//       title="Change Admin Password"
//       description={`Update password for ${adminName}`}
//       isLoading={isLoading}
//       onSubmit={form.handleSubmit(onSubmit)}
//     >
//       <Form {...form}>
//         <form className="space-y-4">
//           {error && (
//             <Alert variant="destructive">
//               <AlertCircle className="h-4 w-4" />
//               <AlertDescription>{error}</AlertDescription>
//             </Alert>
//           )}

//           <PasswordField
//             control={form.control}
//             name="newPassword"
//             label="New Password"
//             placeholder="Enter new password"
//           />

//           <PasswordField
//             control={form.control}
//             name="confirmPassword"
//             label="Confirm New Password"
//             placeholder="Confirm new password"
//           />
//         </form>
//       </Form>
//     </Modal>
//   );
// };

// export default ChangeAdminPassword;
