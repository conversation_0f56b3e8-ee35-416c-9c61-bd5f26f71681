import useSWR, { mutate } from 'swr';

export const GetStaffList = (params: string) => {
  const qs = new URLSearchParams(params);

  const { data, error, isLoading, mutate } = useSWR(
    `/staff/list?${qs.toString()}`
  );

  return {
    staff: data,
    staffLoading: isLoading,
    mutate: mutate,
  };
};

export const GetRewards = (params?: string) => {
  const qs = new URLSearchParams(params);
  const { data, isLoading, mutate } = useSWR(`/reward/list?${qs.toString()}`);

  return {
    rewards: data,
    isLoading: isLoading,
    mutate: mutate,
  };
};

export const GetDepartmentList = (params: string) => {
  const qs = new URLSearchParams(params);

  const { data, error, isLoading, mutate } = useSWR(
    `/staff/list-deparment?${qs.toString()}`
  );

  return {
    department: data,
    departmentLoading: isLoading,
    mutate: mutate,
  };
};
