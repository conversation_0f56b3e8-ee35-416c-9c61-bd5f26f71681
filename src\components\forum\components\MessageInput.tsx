import React, { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import {
  Paperclip,
  Send,
  Smile,
  AtSign,
  X,
  Image as ImageIcon,
  FileText,
  Mic,
  Video,
} from 'lucide-react';
import { ForumMessage } from '@/api/forum/data';
import { useForum } from '@/hooks/useForum';
import { cn } from '@/lib/utils';

interface MessageInputProps {
  onSendMessage: (
    content: string,
    attachments?: File[],
    mentions?: string[],
    replyTo?: string
  ) => void;
  placeholder?: string;
  replyingTo?: ForumMessage | null;
  onCancelReply?: () => void;
  disabled?: boolean;
  className?: string;
}

export const MessageInput: React.FC<MessageInputProps> = ({
  onSendMessage,
  placeholder = 'Type a message...',
  replyingTo,
  onCancelReply,
  disabled = false,
  className,
}) => {
  const [message, setMessage] = useState('');
  const [attachments, setAttachments] = useState<File[]>([]);
  const [mentions, setMentions] = useState<string[]>([]);
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const [showMentionSuggestions, setShowMentionSuggestions] = useState(false);
  const [mentionQuery, setMentionQuery] = useState('');
  const [isTyping, setIsTyping] = useState(false);

  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { sendTypingIndicator, activeGroupId, activeRecipientId } = useForum();

  // Auto-resize textarea
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;
    }
  }, [message]);

  // Handle typing indicators
  useEffect(() => {
    if (message.trim() && !isTyping) {
      setIsTyping(true);
      sendTypingIndicator(
        activeGroupId || undefined,
        activeRecipientId || undefined,
        true
      );
    } else if (!message.trim() && isTyping) {
      setIsTyping(false);
      sendTypingIndicator(
        activeGroupId || undefined,
        activeRecipientId || undefined,
        false
      );
    }

    const timeout = setTimeout(() => {
      if (isTyping) {
        setIsTyping(false);
        sendTypingIndicator(
          activeGroupId || undefined,
          activeRecipientId || undefined,
          false
        );
      }
    }, 3000);

    return () => clearTimeout(timeout);
  }, [
    message,
    isTyping,
    sendTypingIndicator,
    activeGroupId,
    activeRecipientId,
  ]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!message.trim() && attachments.length === 0) return;

    onSendMessage(
      message.trim(),
      attachments.length > 0 ? attachments : undefined,
      mentions.length > 0 ? mentions : undefined,
      replyingTo?.id
    );

    // Reset form
    setMessage('');
    setAttachments([]);
    setMentions([]);
    setIsTyping(false);
    sendTypingIndicator(
      activeGroupId || undefined,
      activeRecipientId || undefined,
      false
    );
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e);
    }
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    setAttachments((prev) => [...prev, ...files]);
  };

  const removeAttachment = (index: number) => {
    setAttachments((prev) => prev.filter((_, i) => i !== index));
  };

  const handleMessageChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value;
    setMessage(value);

    // Handle @ mentions
    const lastAtIndex = value.lastIndexOf('@');
    if (lastAtIndex !== -1) {
      const textAfterAt = value.slice(lastAtIndex + 1);
      const spaceIndex = textAfterAt.indexOf(' ');
      const query =
        spaceIndex === -1 ? textAfterAt : textAfterAt.slice(0, spaceIndex);

      if (query.length > 0 && spaceIndex === -1) {
        setMentionQuery(query);
        setShowMentionSuggestions(true);
      } else {
        setShowMentionSuggestions(false);
      }
    } else {
      setShowMentionSuggestions(false);
    }
  };

  const insertEmoji = (emoji: string) => {
    const textarea = textareaRef.current;
    if (textarea) {
      const start = textarea.selectionStart;
      const end = textarea.selectionEnd;
      const newMessage = message.slice(0, start) + emoji + message.slice(end);
      setMessage(newMessage);

      // Set cursor position after emoji
      setTimeout(() => {
        textarea.setSelectionRange(start + emoji.length, start + emoji.length);
        textarea.focus();
      }, 0);
    }
    setShowEmojiPicker(false);
  };

  const getFileIcon = (file: File) => {
    if (file.type.startsWith('image/'))
      return <ImageIcon className="h-4 w-4" />;
    return <FileText className="h-4 w-4" />;
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const COMMON_EMOJIS = [
    '😀',
    '😂',
    '❤️',
    '👍',
    '👎',
    '😮',
    '😢',
    '😡',
    '🎉',
    '🔥',
    '💯',
    '✨',
  ];

  return (
    <div className={cn('border-t bg-background', className)}>
      {/* Reply Preview */}
      {replyingTo && (
        <div className="p-3 border-b bg-muted/50">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2 text-sm">
              <span className="text-muted-foreground">Replying to</span>
              <span className="font-medium">{replyingTo.senderName}</span>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={onCancelReply}
              className="h-6 w-6 p-0"
            >
              <X className="h-3 w-3" />
            </Button>
          </div>
          <div className="text-xs text-muted-foreground mt-1 truncate">
            {replyingTo.content}
          </div>
        </div>
      )}

      {/* Attachments Preview */}
      {attachments.length > 0 && (
        <div className="p-3 border-b">
          <div className="flex flex-wrap gap-2">
            {attachments.map((file, index) => (
              <div
                key={index}
                className="flex items-center gap-2 bg-muted rounded p-2 text-sm"
              >
                {getFileIcon(file)}
                <div className="flex-1 min-w-0">
                  <div className="truncate font-medium">{file.name}</div>
                  <div className="text-xs text-muted-foreground">
                    {formatFileSize(file.size)}
                  </div>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => removeAttachment(index)}
                  className="h-6 w-6 p-0"
                >
                  <X className="h-3 w-3" />
                </Button>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Mentions */}
      {mentions.length > 0 && (
        <div className="p-3 border-b">
          <div className="flex flex-wrap gap-1">
            <span className="text-sm text-muted-foreground mr-2">
              Mentioning:
            </span>
            {mentions.map((mention, index) => (
              <Badge key={index} variant="secondary" className="text-xs">
                @{mention}
                <X
                  className="h-3 w-3 ml-1 cursor-pointer"
                  onClick={() =>
                    setMentions((prev) => prev.filter((_, i) => i !== index))
                  }
                />
              </Badge>
            ))}
          </div>
        </div>
      )}

      <form onSubmit={handleSubmit} className="p-3">
        <div className="relative">
          <Textarea
            ref={textareaRef}
            value={message}
            onChange={handleMessageChange}
            onKeyDown={handleKeyDown}
            placeholder={placeholder}
            disabled={disabled}
            className="min-h-[40px] max-h-32 resize-none pr-24"
            rows={1}
          />

          {/* Action Buttons */}
          <div className="absolute right-2 bottom-2 flex items-center gap-1">
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={() => setShowEmojiPicker(!showEmojiPicker)}
              className="h-8 w-8 p-0"
            >
              <Smile className="h-4 w-4" />
            </Button>

            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={() => fileInputRef.current?.click()}
              className="h-8 w-8 p-0"
            >
              <Paperclip className="h-4 w-4" />
            </Button>

            <Button
              type="submit"
              size="sm"
              disabled={
                disabled || (!message.trim() && attachments.length === 0)
              }
              className="h-8 w-8 p-0"
            >
              <Send className="h-4 w-4" />
            </Button>
          </div>

          {/* Emoji Picker */}
          {showEmojiPicker && (
            <div className="absolute bottom-full right-0 mb-2 bg-background border rounded-lg shadow-lg p-3 z-10">
              <div className="grid grid-cols-6 gap-1">
                {COMMON_EMOJIS.map((emoji) => (
                  <Button
                    key={emoji}
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => insertEmoji(emoji)}
                    className="h-8 w-8 p-0 hover:bg-muted"
                  >
                    {emoji}
                  </Button>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Hidden file input */}
        <input
          ref={fileInputRef}
          type="file"
          multiple
          onChange={handleFileSelect}
          className="hidden"
          accept="image/*,video/*,.pdf,.doc,.docx,.txt"
        />
      </form>
    </div>
  );
};
