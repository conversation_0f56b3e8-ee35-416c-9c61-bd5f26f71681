import React, { useState, useEffect } from 'react';
import { InputField } from '@/components/common/form';
import { Form } from '@/components/ui/form';
import { Modal } from '@/components/common/modal';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { myApi } from '@/api/fetcher';
import { toast } from 'sonner';
import {
  interactionFormSchema,
  InteractionFormValues,
} from '@/components/validations/crm';
import { GetProfile } from '@/api/staff';
// import { GetPatients, GetDepartments, GetDoctors } from '@/api/crm/data';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { ModalProps } from '@/components/types';
import { useSearchParams } from 'next/navigation';

const Create: React.FC<ModalProps> = ({ setOpen, open, mutate }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [patients, setPatients] = useState<any[]>([]);
  const [departments, setDepartments] = useState<any[]>([]);
  const [doctors, setDoctors] = useState<any[]>([]);
  const [selectedDepartment, setSelectedDepartment] = useState<string>('');
  const [patientsLoading, setPatientsLoading] = useState(false);
  const [departmentsLoading, setDepartmentsLoading] = useState(false);
  const [doctorsLoading, setDoctorsLoading] = useState(false);
  const { profile } = GetProfile();
  const searchParams = useSearchParams();
  const patientIdFromUrl = searchParams.get('patientId');

  const form = useForm<InteractionFormValues>({
    resolver: zodResolver(interactionFormSchema),
    defaultValues: {
      patientId: patientIdFromUrl ? parseInt(patientIdFromUrl) : 0,
      type: 'appointment',
      subject: '',
      description: '',
      status: 'scheduled',
      priority: 'routine',
      appointmentDate: '',
      department: '',
      doctorId: undefined,
      symptoms: '',
      diagnosis: '',
      treatment: '',
      followUpRequired: false,
      followUpDate: '',
    },
  });

  // Fetch patients for dropdown
  useEffect(() => {
    const fetchPatients = async () => {
      if (open) {
        try {
          setPatientsLoading(true);
          const response = await myApi.get('/crm/patients?limit=100');
          if (response.status === 200) {
            setPatients(response.data.data.patients);
          }
        } catch (error) {
          console.error('Error fetching patients:', error);
        } finally {
          setPatientsLoading(false);
        }
      }
    };

    const fetchDepartments = async () => {
      if (open) {
        try {
          setDepartmentsLoading(true);
          const response = await myApi.get('/crm/departments');
          if (response.status === 200) {
            setDepartments(response.data.data.departments);
          }
        } catch (error) {
          console.error('Error fetching departments:', error);
        } finally {
          setDepartmentsLoading(false);
        }
      }
    };

    fetchPatients();
    fetchDepartments();
  }, [open]);

  // Fetch doctors when department changes
  useEffect(() => {
    const fetchDoctors = async () => {
      if (selectedDepartment) {
        try {
          setDoctorsLoading(true);
          const response = await myApi.get(
            `/crm/doctors?departmentId=${selectedDepartment}`
          );
          if (response.status === 200) {
            setDoctors(response.data.data.doctors);
          }
        } catch (error) {
          console.error('Error fetching doctors:', error);
        } finally {
          setDoctorsLoading(false);
        }
      } else {
        setDoctors([]);
      }
    };

    fetchDoctors();
  }, [selectedDepartment]);

  const onSubmit = async (data: InteractionFormValues) => {
    try {
      setIsLoading(true);
      const res = await myApi.post('/crm/appointments', {
        ...data,
        createdBy: profile.data.fullName,
      });
      setIsLoading(false);
      if (res.status === 200) {
        toast.success('Appointment scheduled successfully');
        if (mutate) {
          mutate();
        }
        setOpen(false);
        form.reset();
      }
    } catch (error: any) {
      setIsLoading(false);
      toast.error(error?.response?.data?.message || 'An error occurred');
    }
  };

  return (
    <Modal
      title="Schedule Appointment"
      open={open}
      setOpen={setOpen}
      isLoading={isLoading}
      description="Schedule a new appointment for a patient"
      onSubmit={form.handleSubmit(onSubmit)}
    >
      <Form {...form}>
        <form className="space-y-4">
          <div>
            <label className="text-sm font-medium">Patient</label>
            <Select
              onValueChange={(value) =>
                form.setValue('patientId', parseInt(value))
              }
              disabled={patientsLoading || !!patientIdFromUrl}
              defaultValue={patientIdFromUrl || undefined}
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Select a patient" />
              </SelectTrigger>
              <SelectContent>
                {patients.map((patient) => (
                  <SelectItem key={patient.id} value={patient.id.toString()}>
                    {patient.firstName} {patient.lastName}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {form.formState.errors.patientId && (
              <p className="text-xs text-red-500 mt-1">
                {form.formState.errors.patientId.message}
              </p>
            )}
          </div>

          <div>
            <label className="text-sm font-medium">Appointment Type</label>
            <Select
              onValueChange={(value) => form.setValue('type', value as any)}
              defaultValue={form.getValues('type')}
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Select appointment type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="appointment">Regular Appointment</SelectItem>
                <SelectItem value="lab_result">Lab Result Review</SelectItem>
                <SelectItem value="prescription">
                  Prescription Renewal
                </SelectItem>
                <SelectItem value="other">Other</SelectItem>
              </SelectContent>
            </Select>
            {form.formState.errors.type && (
              <p className="text-xs text-red-500 mt-1">
                {form.formState.errors.type.message}
              </p>
            )}
          </div>

          <div>
            <label className="text-sm font-medium">Department</label>
            <Select
              onValueChange={(value) => {
                form.setValue('department', value);
                setSelectedDepartment(value);
                form.setValue('doctorId', undefined); // Reset doctor when department changes
              }}
              disabled={departmentsLoading}
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Select department" />
              </SelectTrigger>
              <SelectContent>
                {departments.map((department) => (
                  <SelectItem key={department.id} value={department.name}>
                    {department.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {form.formState.errors.department && (
              <p className="text-xs text-red-500 mt-1">
                {form.formState.errors.department.message}
              </p>
            )}
          </div>

          <div>
            <label className="text-sm font-medium">Doctor</label>
            <Select
              onValueChange={(value) =>
                form.setValue('doctorId', parseInt(value))
              }
              disabled={doctorsLoading || !selectedDepartment}
            >
              <SelectTrigger className="w-full">
                <SelectValue
                  placeholder={
                    selectedDepartment
                      ? 'Select doctor'
                      : 'Select department first'
                  }
                />
              </SelectTrigger>
              <SelectContent>
                {doctors.map((doctor) => (
                  <SelectItem key={doctor.id} value={doctor.id.toString()}>
                    {doctor.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {form.formState.errors.doctorId && (
              <p className="text-xs text-red-500 mt-1">
                {form.formState.errors.doctorId.message}
              </p>
            )}
          </div>

          <InputField
            control={form.control}
            name="subject"
            label="Appointment Reason"
            placeholder="Brief reason for the appointment"
          />

          <div>
            <label className="text-sm font-medium">Date and Time</label>
            <input
              type="datetime-local"
              className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
              {...form.register('appointmentDate')}
            />
            {form.formState.errors.appointmentDate && (
              <p className="text-xs text-red-500 mt-1">
                {form.formState.errors.appointmentDate.message}
              </p>
            )}
          </div>

          <div>
            <label className="text-sm font-medium">Symptoms</label>
            <Textarea
              placeholder="Patient's symptoms"
              className="resize-none"
              {...form.register('symptoms')}
            />
          </div>

          <div>
            <label className="text-sm font-medium">Additional Notes</label>
            <Textarea
              placeholder="Additional notes about the appointment"
              className="resize-none"
              {...form.register('description')}
            />
            {form.formState.errors.description && (
              <p className="text-xs text-red-500 mt-1">
                {form.formState.errors.description.message}
              </p>
            )}
          </div>

          <div>
            <label className="text-sm font-medium">Priority</label>
            <Select
              onValueChange={(value) => form.setValue('priority', value as any)}
              defaultValue={form.getValues('priority')}
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Select priority" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="routine">Routine</SelectItem>
                <SelectItem value="urgent">Urgent</SelectItem>
                <SelectItem value="emergency">Emergency</SelectItem>
              </SelectContent>
            </Select>
            {form.formState.errors.priority && (
              <p className="text-xs text-red-500 mt-1">
                {form.formState.errors.priority.message}
              </p>
            )}
          </div>
        </form>
      </Form>
    </Modal>
  );
};

export default Create;
