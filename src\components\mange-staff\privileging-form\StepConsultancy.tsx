'use client';

import { useFormContext } from 'react-hook-form';
import { Input } from '@/components/ui/input';

export default function StepConsultancy() {
  const { register } = useFormContext();

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <label className="text-sm font-medium text-foreground">
            Date of entry on Specialist Register
          </label>
          <Input
            type="date"
            {...register('consultancy.specialistRegisterDate')}
          />
        </div>
        <div className="space-y-2">
          <label className="text-sm font-medium text-foreground">
            Do you hold a consultant post in a Government Hospital?
          </label>
          <select
            {...register('consultancy.currentGovtPost')}
            className="flex h-10 w-full rounded-sm border border-input bg-transparent px-3 py-1 text-sm shadow-xs"
          >
            <option value="">Select</option>
            <option>Yes</option>
            <option>No</option>
          </select>
        </div>
        <Input
          placeholder="Hospital Name (if Yes)"
          {...register('consultancy.currentGovtHospital')}
        />
        <Input
          type="number"
          placeholder="Number of clinical sessions"
          {...register('consultancy.clinicalSessions')}
        />
      </div>

      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-foreground border-b pb-2">
          Previous Employment
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Input
            placeholder="Previous Government Consultant Hospital"
            {...register('consultancy.prevGovtHospital')}
          />
          <Input
            type="date"
            placeholder="Date of leaving"
            {...register('consultancy.prevGovtLeaveDate')}
          />
        </div>
        <Input
          placeholder="Reason for leaving"
          {...register('consultancy.reasonForLeaving')}
        />
      </div>

      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-foreground border-b pb-2">
          Locum Consultant
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Input
            placeholder="Hospital"
            {...register('consultancy.locumHospital')}
          />
          <Input
            placeholder="Dates Held"
            {...register('consultancy.locumDates')}
          />
        </div>
      </div>

      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-foreground border-b pb-2">
          Additional Information
        </h3>
        <div className="space-y-4">
          <div className="space-y-2">
            <label className="text-sm font-medium text-foreground">
              Gaps in CV
            </label>
            <textarea
              {...register('consultancy.gaps')}
              className="flex min-h-[80px] w-full rounded-sm border border-input bg-transparent px-3 py-2 text-sm shadow-xs placeholder:text-muted-foreground/40"
            />
          </div>
          <div className="space-y-2">
            <label className="text-sm font-medium text-foreground">
              Previous work with children/vulnerable adults
            </label>
            <textarea
              {...register('consultancy.childrenWork')}
              className="flex min-h-[80px] w-full rounded-sm border border-input bg-transparent px-3 py-2 text-sm shadow-xs placeholder:text-muted-foreground/40"
            />
          </div>
        </div>
      </div>
    </div>
  );
}
