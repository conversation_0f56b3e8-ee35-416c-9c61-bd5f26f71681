import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  TrendingUp,
  Users,
  Trophy,
  Clock,
  Target,
  Award,
  Activity,
  BarChart3,
} from 'lucide-react';
import {
  ResponsiveContainer,
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
} from 'recharts';

interface GameAnalyticsProps {
  analytics: any;
  isLoading: boolean;
}

const COLORS = [
  '#BD9A3D',
  '#2196F3',
  '#1A1A1A',
  '#4CAF50',
  '#FF9800',
  '#9C27B0',
];

export const GameAnalytics: React.FC<GameAnalyticsProps> = ({
  analytics,
  isLoading,
}) => {
  if (isLoading) {
    return (
      <div className="space-y-6">
        {Array.from({ length: 4 }).map((_, index) => (
          <Card key={index}>
            <CardContent className="p-6">
              <div className="animate-pulse">
                <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
                <div className="h-32 bg-gray-200 rounded"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  const mockAnalytics = analytics || {
    totalGames: 45,
    totalParticipants: 1250,
    totalPrizeDistributed: 125000,
    averageScore: 78.5,
    completionRate: 85.2,
    popularCategories: [
      { name: 'General Knowledge', value: 35, count: 15 },
      { name: 'Medical', value: 25, count: 11 },
      { name: 'Technology', value: 20, count: 9 },
      { name: 'Science', value: 12, count: 5 },
      { name: 'History', value: 8, count: 3 },
    ],
    participationTrend: [
      { date: '2024-01-01', participants: 45, games: 3 },
      { date: '2024-01-02', participants: 62, games: 4 },
      { date: '2024-01-03', participants: 38, games: 2 },
      { date: '2024-01-04', participants: 75, games: 5 },
      { date: '2024-01-05', participants: 89, games: 6 },
      { date: '2024-01-06', participants: 56, games: 4 },
      { date: '2024-01-07', participants: 94, games: 7 },
    ],
    difficultyDistribution: [
      { difficulty: 'Easy', count: 18, percentage: 40 },
      { difficulty: 'Medium', count: 20, percentage: 44.4 },
      { difficulty: 'Hard', count: 7, percentage: 15.6 },
    ],
    topPerformers: [
      {
        name: 'John Doe',
        gamesWon: 12,
        totalEarnings: 25000,
        averageScore: 92.5,
      },
      {
        name: 'Jane Smith',
        gamesWon: 10,
        totalEarnings: 22000,
        averageScore: 89.2,
      },
      {
        name: 'Mike Johnson',
        gamesWon: 8,
        totalEarnings: 18000,
        averageScore: 87.8,
      },
    ],
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-NG', {
      style: 'currency',
      currency: 'NGN',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    }
    if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };

  return (
    <div className="space-y-6">
      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-2">
              <Trophy className="h-8 w-8 text-yellow-500" />
              <div>
                <p className="text-sm text-muted-foreground">Total Games</p>
                <p className="text-2xl font-bold">{mockAnalytics.totalGames}</p>
                <p className="text-xs text-green-600 flex items-center gap-1">
                  <TrendingUp className="h-3 w-3" />
                  +12% from last month
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-2">
              <Users className="h-8 w-8 text-blue-500" />
              <div>
                <p className="text-sm text-muted-foreground">
                  Total Participants
                </p>
                <p className="text-2xl font-bold">
                  {formatNumber(mockAnalytics.totalParticipants)}
                </p>
                <p className="text-xs text-green-600 flex items-center gap-1">
                  <TrendingUp className="h-3 w-3" />
                  +8% from last month
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-2">
              <Award className="h-8 w-8 text-green-500" />
              <div>
                <p className="text-sm text-muted-foreground">
                  Prize Distributed
                </p>
                <p className="text-2xl font-bold">
                  {formatCurrency(mockAnalytics.totalPrizeDistributed)}
                </p>
                <p className="text-xs text-green-600 flex items-center gap-1">
                  <TrendingUp className="h-3 w-3" />
                  +15% from last month
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-2">
              <Target className="h-8 w-8 text-purple-500" />
              <div>
                <p className="text-sm text-muted-foreground">Average Score</p>
                <p className="text-2xl font-bold">
                  {mockAnalytics.averageScore}%
                </p>
                <p className="text-xs text-green-600 flex items-center gap-1">
                  <TrendingUp className="h-3 w-3" />
                  +2.3% from last month
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Participation Trend */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            Participation Trend
          </CardTitle>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={300}>
            <LineChart data={mockAnalytics.participationTrend}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis
                dataKey="date"
                tickFormatter={(value) =>
                  new Date(value).toLocaleDateString('en-US', {
                    month: 'short',
                    day: 'numeric',
                  })
                }
              />
              <YAxis />
              <Tooltip
                labelFormatter={(value) => new Date(value).toLocaleDateString()}
                formatter={(value, name) => [
                  value,
                  name === 'participants' ? 'Participants' : 'Games',
                ]}
              />
              <Line
                type="monotone"
                dataKey="participants"
                stroke="#BD9A3D"
                strokeWidth={2}
                name="participants"
              />
              <Line
                type="monotone"
                dataKey="games"
                stroke="#2196F3"
                strokeWidth={2}
                name="games"
              />
            </LineChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Popular Categories */}
        <Card>
          <CardHeader>
            <CardTitle>Popular Categories</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={250}>
              <PieChart>
                <Pie
                  data={mockAnalytics.popularCategories}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, percentage }) => `${name} ${percentage}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {mockAnalytics.popularCategories.map(
                    (entry: any, index: number) => (
                      <Cell
                        key={`cell-${index}`}
                        fill={COLORS[index % COLORS.length]}
                      />
                    )
                  )}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Difficulty Distribution */}
        <Card>
          <CardHeader>
            <CardTitle>Difficulty Distribution</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={250}>
              <BarChart data={mockAnalytics.difficultyDistribution}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="difficulty" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="count" fill="#BD9A3D" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Top Performers */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Trophy className="h-5 w-5" />
            Top Performers
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {mockAnalytics.topPerformers.map(
              (performer: any, index: number) => (
                <div
                  key={index}
                  className="flex items-center justify-between p-4 bg-gray-50 rounded-lg"
                >
                  <div className="flex items-center gap-3">
                    <div className="flex items-center justify-center w-8 h-8 rounded-full bg-primary text-primary-foreground text-sm font-bold">
                      {index + 1}
                    </div>
                    <div>
                      <p className="font-medium">{performer.name}</p>
                      <p className="text-sm text-muted-foreground">
                        {performer.gamesWon} games won •{' '}
                        {performer.averageScore}% avg score
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-bold text-green-600">
                      {formatCurrency(performer.totalEarnings)}
                    </p>
                    <p className="text-sm text-muted-foreground">
                      Total earnings
                    </p>
                  </div>
                </div>
              )
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
