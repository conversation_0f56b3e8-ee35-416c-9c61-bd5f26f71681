import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { toast } from 'sonner';
import { Modal } from '@/components/common/modal';
import { myApi } from '@/api/fetcher';
import { GetPermissionList } from '@/api/staff';
import { Checkbox } from '@/components/ui/checkbox';
import { LoadingState } from '@/components/common/dataState';

interface CreateRoleProps {
  open: boolean;
  setOpen: (open: boolean) => void;
  mutate?: () => void;
}

const CreateRole: React.FC<CreateRoleProps> = ({ open, setOpen, mutate }) => {
  const { permissionList: permissions, permissionLoading } =
    GetPermissionList();
  const [selectedPermissions, setSelectedPermissions] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
  });

  const handlePermissionChange = (permissionId: string, checked: boolean) => {
    if (checked) {
      setSelectedPermissions([...selectedPermissions, permissionId]);
    } else {
      setSelectedPermissions(
        selectedPermissions.filter((id) => id !== permissionId)
      );
    }
  };

  const onSubmit = async () => {
    if (!formData.name.trim() || !formData.description.trim()) {
      toast.error('Name and description are required');
      return;
    }
    setIsLoading(true);
    const payload = {
      name: formData.name,
      description: formData.description,
      permissionIds: selectedPermissions,
    };
    const res = await myApi.post('/staff/create-role', payload);
    if (res.status === 200) {
      toast.success('Role created successfully');
      if (mutate) {
        mutate();
      }
      setOpen(false);
    } else {
      console.log('Failed to create role');
    }
    setIsLoading(false);
  };

  return (
    <Modal
      open={open}
      setOpen={setOpen}
      title="New Role"
      description="Create New Role"
      isLoading={isLoading}
      onSubmit={onSubmit}
    >
      <form className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="name">Role Name</Label>
          <Input
            id="name"
            value={formData.name}
            onChange={(e) => setFormData({ ...formData, name: e.target.value })}
            placeholder="Enter role name"
            required
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="description">Description</Label>
          <Textarea
            id="description"
            value={formData.description}
            onChange={(e) =>
              setFormData({ ...formData, description: e.target.value })
            }
            placeholder="Enter role description"
            rows={3}
          />
        </div>
        <div>
          <h3 className="text-sm font-medium mb-3">Permissions</h3>
          {permissionLoading ? (
            <LoadingState />
          ) : (
            <div className="space-y-2 max-h-60 overflow-y-auto">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {permissions.map((permission: any) => (
                  <div
                    key={permission.id}
                    className="flex items-center space-x-2"
                  >
                    <Checkbox
                      id={permission.id}
                      checked={selectedPermissions.includes(permission.id)}
                      onCheckedChange={(checked) =>
                        handlePermissionChange(
                          permission.id,
                          checked as boolean
                        )
                      }
                    />
                    <label
                      htmlFor={permission.id}
                      className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      {permission.action}
                    </label>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </form>
    </Modal>
  );
};

export default CreateRole;
