import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import {
  Settings,
  Circle,
  Clock,
  AlertCircle,
  Moon,
  Zap,
  Coffee,
  Briefcase,
  Home,
  Plane,
  Heart,
  Smile,
} from 'lucide-react';
import { useUserPresence, useUpdateUserStatus } from '@/hooks/useForum';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';

interface UserStatusManagerProps {
  userId: string;
  trigger?: React.ReactNode;
}

const statusOptions = [
  { value: 'online', label: 'Online', icon: Circle, color: 'text-green-500' },
  { value: 'away', label: 'Away', icon: Clock, color: 'text-yellow-500' },
  { value: 'busy', label: 'Busy', icon: AlertCircle, color: 'text-red-500' },
  { value: 'offline', label: 'Offline', icon: Moon, color: 'text-gray-500' },
];

const statusEmojis = [
  { emoji: '💼', label: 'Working' },
  { emoji: '☕', label: 'Coffee break' },
  { emoji: '🏠', label: 'Working from home' },
  { emoji: '✈️', label: 'Traveling' },
  { emoji: '🤒', label: 'Sick' },
  { emoji: '🎉', label: 'Celebrating' },
  { emoji: '📚', label: 'Learning' },
  { emoji: '💡', label: 'Brainstorming' },
  { emoji: '🔥', label: 'On fire' },
  { emoji: '😴', label: 'Sleepy' },
];

export const UserStatusManager: React.FC<UserStatusManagerProps> = ({
  userId,
  trigger,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [status, setStatus] = useState('online');
  const [customMessage, setCustomMessage] = useState('');
  const [selectedEmoji, setSelectedEmoji] = useState('');
  const [clearAfter, setClearAfter] = useState('');
  const [showAsAway, setShowAsAway] = useState(false);
  const [pauseNotifications, setPauseNotifications] = useState(false);

  const presence = useUserPresence(userId);
  const { updateStatus, isUpdating } = useUpdateUserStatus();

  const handleSaveStatus = async () => {
    try {
      const statusData = {
        status,
        customMessage: customMessage.trim(),
        emoji: selectedEmoji,
        clearAfter: clearAfter || null,
        showAsAway,
        pauseNotifications,
      };

      await updateStatus(statusData);
      setIsOpen(false);
      toast.success('Status updated successfully');
    } catch (error) {
      console.error('Error updating status:', error);
      toast.error('Failed to update status');
    }
  };

  const handleClearStatus = async () => {
    try {
      await updateStatus({
        status: 'online',
        customMessage: '',
        emoji: '',
        clearAfter: null,
        showAsAway: false,
        pauseNotifications: false,
      });

      // Reset form
      setStatus('online');
      setCustomMessage('');
      setSelectedEmoji('');
      setClearAfter('');
      setShowAsAway(false);
      setPauseNotifications(false);

      setIsOpen(false);
      toast.success('Status cleared');
    } catch (error) {
      console.error('Error clearing status:', error);
      toast.error('Failed to clear status');
    }
  };

  const getStatusIcon = (statusValue: string) => {
    const option = statusOptions.find((opt) => opt.value === statusValue);
    return option ? option.icon : Circle;
  };

  const getStatusColor = (statusValue: string) => {
    const option = statusOptions.find((opt) => opt.value === statusValue);
    return option ? option.color : 'text-gray-500';
  };

  const defaultTrigger = (
    <Button variant="ghost" size="sm" className="h-8 px-2">
      <Settings className="h-4 w-4" />
    </Button>
  );

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>{trigger || defaultTrigger}</DialogTrigger>

      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Update your status</DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Current Status Display */}
          {presence && (
            <div className="flex items-center gap-3 p-3 bg-muted/50 rounded-lg">
              <div className="relative">
                <div
                  className={cn(
                    'h-3 w-3 rounded-full',
                    presence.status === 'online' && 'bg-green-500',
                    presence.status === 'away' && 'bg-yellow-500',
                    presence.status === 'busy' && 'bg-red-500',
                    presence.status === 'offline' && 'bg-gray-400'
                  )}
                />
              </div>
              <div className="flex-1">
                <div className="font-medium">
                  {presence.emoji && (
                    <span className="mr-2">{presence.emoji}</span>
                  )}
                  {presence.customMessage ||
                    statusOptions.find((opt) => opt.value === presence.status)
                      ?.label}
                </div>
                {presence.clearAfter && (
                  <div className="text-xs text-muted-foreground">
                    Clears in {presence.clearAfter?.toLocaleString()}
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Status Selection */}
          <div className="space-y-3">
            <Label>Status</Label>
            <Select value={status} onValueChange={setStatus}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {statusOptions.map((option) => {
                  const Icon = option.icon;
                  return (
                    <SelectItem key={option.value} value={option.value}>
                      <div className="flex items-center gap-2">
                        <Icon className={cn('h-4 w-4', option.color)} />
                        {option.label}
                      </div>
                    </SelectItem>
                  );
                })}
              </SelectContent>
            </Select>
          </div>

          {/* Custom Message */}
          <div className="space-y-3">
            <Label>What's your status?</Label>
            <div className="flex gap-2">
              {selectedEmoji && (
                <div className="flex items-center justify-center w-10 h-10 border rounded-md">
                  <span className="text-lg">{selectedEmoji}</span>
                </div>
              )}
              <Input
                placeholder="What's happening?"
                value={customMessage}
                onChange={(e) => setCustomMessage(e.target.value)}
                maxLength={100}
                className="flex-1"
              />
            </div>
            <div className="text-xs text-muted-foreground text-right">
              {customMessage.length}/100
            </div>
          </div>

          {/* Emoji Selection */}
          <div className="space-y-3">
            <Label>Add an emoji (optional)</Label>
            <div className="grid grid-cols-5 gap-2">
              {statusEmojis.map((item) => (
                <Button
                  key={item.emoji}
                  variant={selectedEmoji === item.emoji ? 'default' : 'outline'}
                  size="sm"
                  className="h-10 p-0"
                  onClick={() =>
                    setSelectedEmoji(
                      selectedEmoji === item.emoji ? '' : item.emoji
                    )
                  }
                  title={item.label}
                >
                  <span className="text-lg">{item.emoji}</span>
                </Button>
              ))}
            </div>
          </div>

          {/* Clear After */}
          <div className="space-y-3">
            <Label>Clear after</Label>
            <Select value={clearAfter} onValueChange={setClearAfter}>
              <SelectTrigger>
                <SelectValue placeholder="Don't clear" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">Don't clear</SelectItem>
                <SelectItem value="30m">30 minutes</SelectItem>
                <SelectItem value="1h">1 hour</SelectItem>
                <SelectItem value="4h">4 hours</SelectItem>
                <SelectItem value="today">Today</SelectItem>
                <SelectItem value="1w">This week</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Additional Options */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label className="text-sm font-medium">Show as away</Label>
                <div className="text-xs text-muted-foreground">
                  When others mention you, they'll be told you may not respond
                  quickly
                </div>
              </div>
              <Switch checked={showAsAway} onCheckedChange={setShowAsAway} />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label className="text-sm font-medium">
                  Pause notifications
                </Label>
                <div className="text-xs text-muted-foreground">
                  You won't receive notifications while this status is active
                </div>
              </div>
              <Switch
                checked={pauseNotifications}
                onCheckedChange={setPauseNotifications}
              />
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3">
            <Button
              onClick={handleSaveStatus}
              disabled={isUpdating}
              className="flex-1"
            >
              {isUpdating ? 'Updating...' : 'Save'}
            </Button>

            <Button
              variant="outline"
              onClick={handleClearStatus}
              disabled={isUpdating}
            >
              Clear
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
