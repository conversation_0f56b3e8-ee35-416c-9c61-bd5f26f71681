'use client';

import React, { useState } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  <PERSON><PERSON><PERSON>,
  Legend,
  ResponsiveContainer,
  <PERSON><PERSON>hart,
  Pie,
  Cell,
  LineChart,
  Line,
  AreaChart,
  Area,
  Scatter<PERSON>hart,
  Scatter,
  ZAxis,
} from 'recharts';
import { AnalysisResult } from '@/lib/data-analysis/parser';
import ChartTypeSelector, { ChartType } from './chart-type-selector';
import DateVisualization from './date-visualization';

interface VisualizationProps {
  analysisResult: AnalysisResult;
}

const COLORS = [
  '#BD9A3D',
  '#2196F3',
  '#1A1A1A',
  '#8884d8',
  '#82ca9d',
  '#ffc658',
];

export const DataVisualizations: React.FC<VisualizationProps> = ({
  analysisResult,
}) => {
  if (!analysisResult) return null;

  const { summary, statistics } = analysisResult;

  // State to track selected chart type for each column
  const [chartTypes, setChartTypes] = useState<Record<string, ChartType>>({});

  // Get chart type for a column, with default values based on data type
  const getChartType = (
    columnName: string,
    dataType: 'categorical' | 'numeric' | 'date'
  ): ChartType => {
    if (chartTypes[columnName]) {
      return chartTypes[columnName];
    }

    // Default chart types
    if (dataType === 'categorical') return 'pie';
    if (dataType === 'date') return 'line';
    return 'bar-numeric';
  };

  // Handle chart type selection
  const handleChartTypeChange = (columnName: string, chartType: ChartType) => {
    setChartTypes((prev) => ({
      ...prev,
      [columnName]: chartType,
    }));
  };

  // Prepare data for categorical column visualization
  const prepareCategoricalData = (columnName: string) => {
    const distribution = statistics[columnName]?.distribution;
    if (!distribution) return [];

    return Object.entries(distribution).map(([name, value]) => ({
      name,
      value,
    }));
  };

  // Prepare data for numeric column visualization
  const prepareNumericDistribution = (columnName: string) => {
    const values = Object.values(statistics[columnName] || {});
    if (!values.length) return [];

    // For numeric columns, we'll create a simple representation of min, mean, max
    return [
      { name: 'Min', value: statistics[columnName]?.min || 0 },
      { name: 'Mean', value: statistics[columnName]?.mean || 0 },
      { name: 'Max', value: statistics[columnName]?.max || 0 },
    ];
  };

  // Generate scatter plot data from numeric columns
  const prepareScatterData = (columnName: string) => {
    const min = statistics[columnName]?.min || 0;
    const max = statistics[columnName]?.max || 100;
    const mean = statistics[columnName]?.mean || 50;

    // Create a simple scatter distribution around the mean
    return [
      { x: min, y: min, z: 10 },
      { x: mean * 0.8, y: mean * 0.9, z: 20 },
      { x: mean, y: mean, z: 30 },
      { x: mean * 1.1, y: mean * 1.2, z: 20 },
      { x: max, y: max, z: 10 },
    ];
  };

  return (
    <div className="space-y-8">
      <h2 className="text-xl font-bold">Data Visualizations</h2>

      {/* Summary Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow">
          <h3 className="text-sm font-medium text-gray-500 uppercase">Rows</h3>
          <p className="text-2xl font-bold">{summary.rowCount}</p>
        </div>
        <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow">
          <h3 className="text-sm font-medium text-gray-500 uppercase">
            Columns
          </h3>
          <p className="text-2xl font-bold">{summary.columnCount}</p>
        </div>
        <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow">
          <h3 className="text-sm font-medium text-gray-500 uppercase">
            Column Types
          </h3>
          <div className="flex gap-2 mt-2">
            <span className="px-2 py-1 bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400 rounded text-xs">
              Numeric: {summary.numericColumns.length}
            </span>
            <span className="px-2 py-1 bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400 rounded text-xs">
              Categorical: {summary.categoricalColumns.length}
            </span>
            <span className="px-2 py-1 bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-400 rounded text-xs">
              Date: {summary.dateColumns.length}
            </span>
          </div>
        </div>
      </div>

      {/* Date Data Visualizations */}
      {summary.dateColumns.length > 0 && (
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Time Series Data</h3>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {summary.dateColumns.slice(0, 4).map((columnName) => (
              <div
                key={columnName}
                className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow"
              >
                <div className="flex justify-between items-center mb-4">
                  <h4 className="text-md font-medium">{columnName}</h4>
                  <ChartTypeSelector
                    dataType="date"
                    selectedType={getChartType(columnName, 'date')}
                    onSelectType={(type) =>
                      handleChartTypeChange(columnName, type)
                    }
                  />
                </div>
                <DateVisualization
                  columnName={columnName}
                  analysisResult={analysisResult}
                  chartType={getChartType(columnName, 'date')}
                />
                <div className="mt-2 text-xs text-gray-500">
                  Time series data visualization
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Categorical Data Visualizations */}
      {summary.categoricalColumns.length > 0 && (
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Categorical Data</h3>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {summary.categoricalColumns.slice(0, 4).map((columnName) => (
              <div
                key={columnName}
                className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow"
              >
                <div className="flex justify-between items-center mb-4">
                  <h4 className="text-md font-medium">{columnName}</h4>
                  <ChartTypeSelector
                    dataType="categorical"
                    selectedType={getChartType(columnName, 'categorical')}
                    onSelectType={(type) =>
                      handleChartTypeChange(columnName, type)
                    }
                  />
                </div>
                <div className="h-64">
                  <ResponsiveContainer width="100%" height="100%">
                    <>
                      {getChartType(columnName, 'categorical') === 'pie' && (
                        <PieChart>
                          <Pie
                            data={prepareCategoricalData(columnName)}
                            cx="50%"
                            cy="50%"
                            labelLine={false}
                            label={({ name, percent }) =>
                              `${name}: ${(percent * 100).toFixed(0)}%`
                            }
                            outerRadius={80}
                            fill="#8884d8"
                            dataKey="value"
                          >
                            {prepareCategoricalData(columnName).map(
                              (entry, index) => (
                                <Cell
                                  key={`cell-${index}`}
                                  fill={COLORS[index % COLORS.length]}
                                />
                              )
                            )}
                          </Pie>
                          <Tooltip />
                          <Legend />
                        </PieChart>
                      )}
                      {getChartType(columnName, 'categorical') === 'donut' && (
                        <PieChart>
                          <Pie
                            data={prepareCategoricalData(columnName)}
                            cx="50%"
                            cy="50%"
                            labelLine={false}
                            label={({ name, percent }) =>
                              `${name}: ${(percent * 100).toFixed(0)}%`
                            }
                            innerRadius={40}
                            outerRadius={80}
                            fill="#8884d8"
                            dataKey="value"
                          >
                            {prepareCategoricalData(columnName).map(
                              (entry, index) => (
                                <Cell
                                  key={`cell-${index}`}
                                  fill={COLORS[index % COLORS.length]}
                                />
                              )
                            )}
                          </Pie>
                          <Tooltip />
                          <Legend />
                        </PieChart>
                      )}
                      {getChartType(columnName, 'categorical') === 'bar' && (
                        <BarChart
                          data={prepareCategoricalData(columnName)}
                          margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                        >
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="name" />
                          <YAxis />
                          <Tooltip />
                          <Legend />
                          <Bar dataKey="value" fill="#BD9A3D" />
                        </BarChart>
                      )}
                    </>
                  </ResponsiveContainer>
                </div>
                <div className="mt-2 text-xs text-gray-500">
                  Unique values: {statistics[columnName]?.uniqueValues || 0}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Numeric Data Visualizations */}
      {summary.numericColumns.length > 0 && (
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Numeric Data</h3>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {summary.numericColumns.slice(0, 4).map((columnName) => (
              <div
                key={columnName}
                className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow"
              >
                <div className="flex justify-between items-center mb-4">
                  <h4 className="text-md font-medium">{columnName}</h4>
                  <ChartTypeSelector
                    dataType="numeric"
                    selectedType={getChartType(columnName, 'numeric')}
                    onSelectType={(type) =>
                      handleChartTypeChange(columnName, type)
                    }
                  />
                </div>
                <div className="h-64">
                  <ResponsiveContainer width="100%" height="100%">
                    <>
                      {getChartType(columnName, 'numeric') ===
                        'bar-numeric' && (
                        <BarChart
                          data={prepareNumericDistribution(columnName)}
                          margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                        >
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="name" />
                          <YAxis />
                          <Tooltip />
                          <Legend />
                          <Bar dataKey="value" fill="#BD9A3D" />
                        </BarChart>
                      )}
                      {getChartType(columnName, 'numeric') === 'line' && (
                        <LineChart
                          data={prepareNumericDistribution(columnName)}
                          margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                        >
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="name" />
                          <YAxis />
                          <Tooltip />
                          <Legend />
                          <Line
                            type="monotone"
                            dataKey="value"
                            stroke="#2196F3"
                            strokeWidth={2}
                          />
                        </LineChart>
                      )}
                      {getChartType(columnName, 'numeric') === 'area' && (
                        <AreaChart
                          data={prepareNumericDistribution(columnName)}
                          margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                        >
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="name" />
                          <YAxis />
                          <Tooltip />
                          <Legend />
                          <Area
                            type="monotone"
                            dataKey="value"
                            fill="#BD9A3D"
                            stroke="#1A1A1A"
                          />
                        </AreaChart>
                      )}
                      {getChartType(columnName, 'numeric') === 'scatter' && (
                        <ScatterChart
                          margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                        >
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis type="number" dataKey="x" name="Value" />
                          <YAxis
                            type="number"
                            dataKey="y"
                            name="Distribution"
                          />
                          <ZAxis type="number" dataKey="z" range={[50, 400]} />
                          <Tooltip cursor={{ strokeDasharray: '3 3' }} />
                          <Legend />
                          <Scatter
                            name={columnName}
                            data={prepareScatterData(columnName)}
                            fill="#BD9A3D"
                          />
                        </ScatterChart>
                      )}
                    </>
                  </ResponsiveContainer>
                </div>
                <div className="mt-2 grid grid-cols-3 gap-2 text-xs">
                  <div>
                    <span className="text-gray-500">Min:</span>{' '}
                    {statistics[columnName]?.min?.toFixed(2)}
                  </div>
                  <div>
                    <span className="text-gray-500">Mean:</span>{' '}
                    {statistics[columnName]?.mean?.toFixed(2)}
                  </div>
                  <div>
                    <span className="text-gray-500">Max:</span>{' '}
                    {statistics[columnName]?.max?.toFixed(2)}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default DataVisualizations;
