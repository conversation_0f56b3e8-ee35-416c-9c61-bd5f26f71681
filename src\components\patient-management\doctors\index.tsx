'use client';

import { useState } from 'react';
import { UserCog } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import DoctorTable from './components/data-table';

export default function DoctorsPage() {
  const [open, setOpen] = useState(false);

  return (
    <>
      <h2 className="text-lg font-bold text-gray-900 dark:text-white mb-4 text-left flex items-center gap-2">
        <UserCog className="w-5 h-5 text-zinc-900 dark:text-zinc-50" />
        Doctor Management
      </h2>
      <div className="flex gap-2">
        <Badge className="h-7 cursor-pointer" onClick={() => setOpen(true)}>
          Add Doctor
        </Badge>
      </div>
      <div className="mt-4 bg-white dark:bg-[#0F0F12] rounded-xl sm:p-4 flex flex-col items-start justify-start sm:border border-gray-200 dark:border-[#1F1F23]">
        <DoctorTable openCreate={open} setOpenCreate={setOpen} />
      </div>
    </>
  );
}
