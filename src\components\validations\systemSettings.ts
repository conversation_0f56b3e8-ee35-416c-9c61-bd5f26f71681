import * as z from 'zod';

export const emailSchema = z.object({
  email: z.string().email({ message: 'Please enter a valid email address' }),
});

export const notificationEmailsSchema = z.object({
  emails: z
    .array(emailSchema)
    .min(1, { message: 'At least one email is required' }),
});

export type EmailFormValues = z.infer<typeof emailSchema>;
export type NotificationEmailsFormValues = z.infer<
  typeof notificationEmailsSchema
>;
