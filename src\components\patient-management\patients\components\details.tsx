import React, { useState, useEffect } from 'react';
import { Modal } from '@/components/common/modal';
import { ModalProps } from '@/components/crm/types';
import dayjs from 'dayjs';
import { myApi } from '@/api/fetcher';
import { toast } from 'sonner';
import { GetProfile } from '@/api/staff';
import {
  GetAppointmentsByPatientId,
  GetPatientMedicalRecords,
  GetInteractionsByPatientId,
} from '@/api/crm/data';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  User,
  CalendarDays,
  ClipboardList,
  MessageSquare,
  Mail,
  MessageCircle,
  Phone,
  Video,
  Instagram,
  Twitter,
  Facebook,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { hasPermission, PERMISSIONS } from '@/lib/types/permissions';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  patientFormSchema,
  PatientFormValues,
} from '@/components/validations/crm';
import { InputField } from '@/components/common/form';
import { Form } from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

import { useRouter } from 'next/navigation';
import { Paths } from '@/components/navigations/data';

interface TabProps {
  label: string;
  icon: React.ReactNode;
  content: React.ReactNode;
}

const Details: React.FC<ModalProps> = ({ open, setOpen, mutate, data }) => {
  const [isLoading, setIsLoading] = useState(false);
  // Using activeTab state to track the current tab
  const [, setActiveTab] = useState(0);
  const [editMode, setEditMode] = useState(false);
  const { profile } = GetProfile();
  const router = useRouter();
  const permitUpdate = hasPermission(PERMISSIONS.PATIENT_EDIT);

  const { appointments } = GetAppointmentsByPatientId(data?.id);
  const appointmentData = appointments?.data?.appointments;

  const { medicalRecords } = GetPatientMedicalRecords(data?.id);
  const medicalRecordData = medicalRecords?.data?.medicalRecords;

  const { interactions } = GetInteractionsByPatientId(data?.id);
  const interactionData = interactions?.data?.interactions;

  const form = useForm<PatientFormValues>({
    resolver: zodResolver(patientFormSchema),
    defaultValues: {
      title: data?.title || '',
      firstName: data?.firstName || '',
      lastName: data?.lastName || '',
      emailAddress: data?.emailAddress || '',
      phoneNumber: data?.phoneNumber || '',
      address: data?.address || '',
      city: data?.city || '',
      state: data?.state || '',
      zipCode: data?.zipCode || '',
      dateOfBirth: data?.dateOfBirth || '',
      gender: data?.gender || '',
      bloodType: data?.bloodType || '',
      allergies: data?.allergies || '',
      medicalHistory: data?.medicalHistory || '',
      emergencyContact: data?.emergencyContact || '',
      emergencyContactPhone: data?.emergencyContactPhone || '',
      insuranceProvider: data?.insuranceProvider || '',
      insuranceNumber: data?.insuranceNumber || '',
      notes: data?.notes || '',
    },
  });

  useEffect(() => {
    if (data && open) {
      form.reset({
        title: data.title || '',
        firstName: data.firstName || '',
        lastName: data.lastName || '',
        emailAddress: data.emailAddress || '',
        phoneNumber: data.phoneNumber || '',
        address: data.address || '',
        city: data.city || '',
        state: data.state || '',
        zipCode: data.zipCode || '',
        dateOfBirth: data.dateOfBirth || '',
        gender: data.gender || '',
        bloodType: data.bloodType || '',
        allergies: data.allergies || '',
        medicalHistory: data.medicalHistory || '',
        emergencyContact: data.emergencyContact || '',
        emergencyContactPhone: data.emergencyContactPhone || '',
        insuranceProvider: data.insuranceProvider || '',
        insuranceNumber: data.insuranceNumber || '',
        notes: data.notes || '',
      });
    }
  }, [data, open, form]);

  const handleUpdatePatient = async (formData: PatientFormValues) => {
    try {
      setIsLoading(true);
      const res = await myApi.patch(`/crm/patients/${data.id}`, {
        ...formData,
        updatedBy: profile.data.fullName,
      });
      setIsLoading(false);
      if (res.status === 200) {
        toast.success('Patient updated successfully');
        setEditMode(false);
        if (mutate) {
          mutate();
        }
      }
    } catch (error: any) {
      setIsLoading(false);
      toast.error(error?.response?.data?.message || 'An error occurred');
    }
  };

  const togglePatientStatus = async () => {
    try {
      setIsLoading(true);
      const newStatus = data.status === 'active' ? 'inactive' : 'active';
      const res = await myApi.patch(`/crm/patients/${data.id}/status`, {
        status: newStatus,
        updatedBy: profile.data.fullName,
      });
      setIsLoading(false);
      if (res.status === 200) {
        toast.success(`Patient marked as ${newStatus}`);
        if (mutate) {
          mutate();
        }
      }
    } catch (error: any) {
      setIsLoading(false);
      toast.error(error?.response?.data?.message || 'An error occurred');
    }
  };

  const handleScheduleAppointment = () => {
    setOpen(false);
    router.push(`${Paths.Appointments}/new?patientId=${data.id}`);
  };

  if (!data) return null;

  // Calculate age from date of birth
  const age = data.dateOfBirth
    ? dayjs().diff(dayjs(data.dateOfBirth), 'year')
    : 'N/A';

  // Tab content components
  const PatientInfo = () => (
    <div className="space-y-6">
      {editMode ? (
        <Form {...form}>
          <form
            className="space-y-4"
            onSubmit={form.handleSubmit(handleUpdatePatient)}
          >
            <div className="grid grid-cols-3 gap-4">
              <div className="col-span-1">
                <Select
                  onValueChange={(value) => form.setValue('title', value)}
                  defaultValue={form.getValues('title')}
                >
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Title" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Mr">Mr</SelectItem>
                    <SelectItem value="Mrs">Mrs</SelectItem>
                    <SelectItem value="Ms">Ms</SelectItem>
                    <SelectItem value="Dr">Dr</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="col-span-2">
                <InputField
                  control={form.control}
                  name="firstName"
                  label="First Name"
                  placeholder="John"
                />
              </div>
            </div>

            <InputField
              control={form.control}
              name="lastName"
              label="Last Name"
              placeholder="Doe"
            />

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium">Date of Birth</label>
                <input
                  type="date"
                  className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                  {...form.register('dateOfBirth')}
                />
              </div>

              <div>
                <label className="text-sm font-medium">Gender</label>
                <Select
                  onValueChange={(value) => form.setValue('gender', value)}
                  defaultValue={form.getValues('gender')}
                >
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Select Gender" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="male">Male</SelectItem>
                    <SelectItem value="female">Female</SelectItem>
                    <SelectItem value="other">Other</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* More form fields would go here */}

            <div className="flex justify-end gap-2 mt-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => setEditMode(false)}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading ? 'Saving...' : 'Save Changes'}
              </Button>
            </div>
          </form>
        </Form>
      ) : (
        <>
          <div>
            <h3 className="text-xs font-medium mb-2 text-gray-500 uppercase">
              Personal Information
            </h3>
            <div className="grid sm:grid-cols-2 gap-4 text-sm">
              <div>
                <p className="text-gray-500 dark:text-gray-400">Full Name</p>
                <p className="font-medium">
                  {(data.title ? data.title + ' ' : '') +
                    data.firstName +
                    ' ' +
                    data.lastName}
                </p>
              </div>
              <div>
                <p className="text-gray-500 dark:text-gray-400">Patient ID</p>
                <p className="font-medium">
                  P-{String(data.id).padStart(6, '0')}
                </p>
              </div>
              <div>
                <p className="text-gray-500 dark:text-gray-400">
                  Date of Birth
                </p>
                <p className="font-medium">
                  {data.dateOfBirth
                    ? dayjs(data.dateOfBirth).format('MMMM D, YYYY')
                    : 'Not provided'}
                </p>
              </div>
              <div>
                <p className="text-gray-500 dark:text-gray-400">Age</p>
                <p className="font-medium">{age}</p>
              </div>
              <div>
                <p className="text-gray-500 dark:text-gray-400">Gender</p>
                <p className="font-medium">
                  {data.gender
                    ? data.gender.charAt(0).toUpperCase() + data.gender.slice(1)
                    : 'Not provided'}
                </p>
              </div>
              <div>
                <p className="text-gray-500 dark:text-gray-400">Blood Type</p>
                <p className="font-medium">
                  {data.bloodType || 'Not recorded'}
                </p>
              </div>
              <div>
                <p className="text-gray-500 dark:text-gray-400">Email</p>
                <p className="font-medium">{data.emailAddress}</p>
              </div>
              <div>
                <p className="text-gray-500 dark:text-gray-400">Phone</p>
                <p className="font-medium">{data.phoneNumber}</p>
              </div>
              <div>
                <p className="text-gray-500 dark:text-gray-400">Status</p>
                <p
                  className={`font-medium ${
                    data.status === 'active' ? 'text-green-600' : 'text-red-600'
                  }`}
                >
                  {data.status.charAt(0).toUpperCase() + data.status.slice(1)}
                </p>
              </div>
            </div>
          </div>

          <div>
            <h3 className="text-xs font-medium mb-2 text-gray-500 uppercase">
              Medical Information
            </h3>
            <div className="grid sm:grid-cols-2 gap-4 text-sm">
              <div className="sm:col-span-2">
                <p className="text-gray-500 dark:text-gray-400">Allergies</p>
                <p className="font-medium">
                  {data.allergies || 'None recorded'}
                </p>
              </div>
              <div className="sm:col-span-2">
                <p className="text-gray-500 dark:text-gray-400">
                  Medical History
                </p>
                <p className="font-medium">
                  {data.medicalHistory || 'None recorded'}
                </p>
              </div>
            </div>
          </div>

          <div>
            <h3 className="text-xs font-medium mb-2 text-gray-500 uppercase">
              Insurance Information
            </h3>
            <div className="grid sm:grid-cols-2 gap-4 text-sm">
              <div>
                <p className="text-gray-500 dark:text-gray-400">
                  Insurance Provider
                </p>
                <p className="font-medium">
                  {data.insuranceProvider || 'Not provided'}
                </p>
              </div>
              <div>
                <p className="text-gray-500 dark:text-gray-400">
                  Insurance Number
                </p>
                <p className="font-medium">
                  {data.insuranceNumber || 'Not provided'}
                </p>
              </div>
            </div>
          </div>

          <div>
            <h3 className="text-xs font-medium mb-2 text-gray-500 uppercase">
              Emergency Contact
            </h3>
            <div className="grid sm:grid-cols-2 gap-4 text-sm">
              <div>
                <p className="text-gray-500 dark:text-gray-400">Name</p>
                <p className="font-medium">
                  {data.emergencyContact || 'Not provided'}
                </p>
              </div>
              <div>
                <p className="text-gray-500 dark:text-gray-400">Phone</p>
                <p className="font-medium">
                  {data.emergencyContactPhone || 'Not provided'}
                </p>
              </div>
            </div>
          </div>

          <div>
            <h3 className="text-xs font-medium mb-2 text-gray-500 uppercase">
              Address
            </h3>
            <div className="grid sm:grid-cols-2 gap-4 text-sm">
              <div className="sm:col-span-2">
                <p className="text-gray-500 dark:text-gray-400">
                  Street Address
                </p>
                <p className="font-medium">{data.address || 'Not provided'}</p>
              </div>
              <div>
                <p className="text-gray-500 dark:text-gray-400">City</p>
                <p className="font-medium">{data.city || 'Not provided'}</p>
              </div>
              <div>
                <p className="text-gray-500 dark:text-gray-400">State</p>
                <p className="font-medium">{data.state || 'Not provided'}</p>
              </div>
              <div>
                <p className="text-gray-500 dark:text-gray-400">Zip Code</p>
                <p className="font-medium">{data.zipCode || 'Not provided'}</p>
              </div>
            </div>
          </div>

          {data.notes && (
            <div>
              <h3 className="text-xs font-medium mb-2 text-gray-500 uppercase">
                Notes
              </h3>
              <p className="text-sm whitespace-pre-wrap">{data.notes}</p>
            </div>
          )}

          {permitUpdate && (
            <div className="flex justify-end gap-2 mt-4">
              <Button
                variant="outline"
                onClick={togglePatientStatus}
                className={
                  data.status === 'active' ? 'text-red-600' : 'text-green-600'
                }
              >
                Mark as {data.status === 'active' ? 'Inactive' : 'Active'}
              </Button>
              <Button onClick={() => setEditMode(true)}>Edit Patient</Button>
            </div>
          )}
        </>
      )}
    </div>
  );

  const AppointmentsInfo = () => (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-xs font-medium mb-2 text-gray-500 uppercase">
          Appointment History
        </h3>
        <Button size="sm" onClick={handleScheduleAppointment}>
          Schedule Appointment
        </Button>
      </div>

      {appointmentData && appointmentData.length > 0 ? (
        <div className="space-y-4">
          {appointmentData.map((appointment: any) => (
            <div
              key={appointment.id}
              className="p-3 border rounded-md border-gray-200 dark:border-gray-700"
            >
              <div className="flex justify-between items-start">
                <h4 className="font-medium">
                  {appointment.type} - {appointment.department}
                </h4>
                <span
                  className={`px-2 py-1 text-xs rounded-full ${
                    appointment.status === 'completed'
                      ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'
                      : appointment.status === 'scheduled'
                        ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400'
                        : appointment.status === 'cancelled'
                          ? 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400'
                          : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400'
                  }`}
                >
                  {appointment.status.charAt(0).toUpperCase() +
                    appointment.status.slice(1)}
                </span>
              </div>
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                {appointment.description}
              </p>
              <div className="flex justify-between items-center mt-2 text-xs text-gray-500">
                <span>Doctor: {appointment.doctorName}</span>
                <span>
                  {dayjs(appointment.appointmentDate).format(
                    'MMM D, YYYY h:mm A'
                  )}
                </span>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <p className="text-sm text-gray-500">No appointments recorded yet.</p>
      )}
    </div>
  );

  const MedicalRecordsInfo = () => (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-xs font-medium mb-2 text-gray-500 uppercase">
          Medical Records
        </h3>
        <Button size="sm">Add Medical Record</Button>
      </div>

      {medicalRecordData && medicalRecordData.length > 0 ? (
        <div className="space-y-4">
          {medicalRecordData.map((record: any) => (
            <div
              key={record.id}
              className="p-3 border rounded-md border-gray-200 dark:border-gray-700"
            >
              <div className="flex justify-between items-start">
                <h4 className="font-medium">{record.recordType}</h4>
                <span className="text-xs text-gray-500">
                  {dayjs(record.recordDate).format('MMM D, YYYY')}
                </span>
              </div>

              {record.diagnosis && (
                <div className="mt-2">
                  <p className="text-xs text-gray-500">Diagnosis</p>
                  <p className="text-sm">{record.diagnosis}</p>
                </div>
              )}

              {record.treatment && (
                <div className="mt-2">
                  <p className="text-xs text-gray-500">Treatment</p>
                  <p className="text-sm">{record.treatment}</p>
                </div>
              )}

              {record.notes && (
                <div className="mt-2">
                  <p className="text-xs text-gray-500">Notes</p>
                  <p className="text-sm">{record.notes}</p>
                </div>
              )}

              <div className="flex justify-between items-center mt-2 text-xs text-gray-500">
                <span>Doctor: {record.doctorName}</span>
                <Button size="sm" variant="outline">
                  View Details
                </Button>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <p className="text-sm text-gray-500">No medical records available.</p>
      )}
    </div>
  );

  const InteractionsInfo = () => (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-xs font-medium mb-2 text-gray-500 uppercase">
          Communication History
        </h3>
        <Button
          size="sm"
          onClick={() => {
            setOpen(false);
            router.push(`${Paths.Interactions}?patientId=${data.id}`);
          }}
        >
          Add Interaction
        </Button>
      </div>

      {interactionData && interactionData.length > 0 ? (
        <div className="space-y-4">
          {interactionData.map((interaction: any) => (
            <div
              key={interaction.id}
              className="p-3 border rounded-md border-gray-200 dark:border-gray-700"
            >
              <div className="flex justify-between items-start">
                <div className="flex items-center gap-2">
                  {interaction.channel === 'whatsapp' && (
                    <MessageSquare className="w-4 h-4 text-green-500" />
                  )}
                  {interaction.channel === 'email' && (
                    <Mail className="w-4 h-4 text-blue-500" />
                  )}
                  {interaction.channel === 'sms' && (
                    <MessageCircle className="w-4 h-4 text-purple-500" />
                  )}
                  {interaction.channel === 'phone' && (
                    <Phone className="w-4 h-4 text-red-500" />
                  )}
                  {interaction.channel === 'video' && (
                    <Video className="w-4 h-4 text-orange-500" />
                  )}
                  {interaction.channel === 'instagram' && (
                    <Instagram className="w-4 h-4 text-pink-500" />
                  )}
                  {interaction.channel === 'twitter' && (
                    <Twitter className="w-4 h-4 text-blue-400" />
                  )}
                  {interaction.channel === 'facebook' && (
                    <Facebook className="w-4 h-4 text-blue-600" />
                  )}
                  <h4 className="font-medium capitalize">
                    {interaction.channel} - {interaction.subject}
                  </h4>
                </div>
                <span
                  className={`px-2 py-1 text-xs rounded-full ${
                    interaction.status === 'completed'
                      ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'
                      : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400'
                  }`}
                >
                  {interaction.status.charAt(0).toUpperCase() +
                    interaction.status.slice(1)}
                </span>
              </div>

              <div className="mt-2">
                <p className="text-sm line-clamp-2">{interaction.content}</p>
              </div>

              <div className="flex justify-between items-center mt-2 text-xs text-gray-500">
                <span
                  className={`px-2 py-1 rounded-full ${
                    interaction.direction === 'inbound'
                      ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400'
                      : 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'
                  }`}
                >
                  {interaction.direction === 'inbound' ? 'Inbound' : 'Outbound'}
                </span>
                <span>
                  {dayjs(interaction.createdAt).format('MMM D, YYYY h:mm A')}
                </span>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <p className="text-sm text-gray-500">
          No interaction history available.
        </p>
      )}
    </div>
  );

  // Define tabs
  const tabs: TabProps[] = [
    {
      label: 'Patient Info',
      icon: <User className="w-4 h-4" />,
      content: <PatientInfo />,
    },
    {
      label: 'Appointments',
      icon: <CalendarDays className="w-4 h-4" />,
      content: <AppointmentsInfo />,
    },
    {
      label: 'Medical Records',
      icon: <ClipboardList className="w-4 h-4" />,
      content: <MedicalRecordsInfo />,
    },
    {
      label: 'Interactions',
      icon: <MessageSquare className="w-4 h-4" />,
      content: <InteractionsInfo />,
    },
  ];

  return (
    <Modal
      open={open}
      setOpen={setOpen}
      title="Patient Details"
      description={`${data.firstName} ${data.lastName}`}
      size="lg"
    >
      <Tabs
        defaultValue="0"
        className="w-full"
        onValueChange={(value) => setActiveTab(parseInt(value))}
      >
        <TabsList className="grid grid-cols-3 mb-4">
          {tabs.map((tab, index) => (
            <TabsTrigger
              key={index}
              value={index.toString()}
              className="flex items-center gap-2"
            >
              {tab.icon}
              <span className="hidden sm:inline">{tab.label}</span>
            </TabsTrigger>
          ))}
        </TabsList>

        {tabs.map((tab, index) => (
          <TabsContent key={index} value={index.toString()} className="mt-0">
            {tab.content}
          </TabsContent>
        ))}
      </Tabs>
    </Modal>
  );
};

export default Details;
