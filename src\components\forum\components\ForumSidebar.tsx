import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON>List, TabsTrigger, TabsContent } from '@/components/ui/tabs';
import {
  PlusCircle,
  Search,
  Users,
  Hash,
  Lock,
  Globe,
  MessageCircle,
  Settings,
  UserPlus,
  MoreHorizontal,
  Gamepad2,
} from 'lucide-react';
import { useRouter } from 'next/navigation';
import { GetForumGroups } from '@/api/forum/data';
import { GetProfile } from '@/api/staff';
import { hasPermission, PERMISSIONS } from '@/lib/types/permissions';
import { CreateGroupModal } from '../modals/CreateGroupModal';
import { GamesAndSurveys } from './GamesAndSurveys';
import { useForum, useUnreadCounts, useOnlineStatus } from '@/hooks/useForum';
import { cn } from '@/lib/utils';

export const ForumSidebar = () => {
  const router = useRouter();
  const { groups, isLoading } = GetForumGroups();
  const { profile } = GetProfile();
  const [searchTerm, setSearchTerm] = useState('');
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [activeTab, setActiveTab] = useState('groups');

  const {
    activeGroupId,
    activeRecipientId,
    setActiveGroup,
    setActiveRecipient,
  } = useForum();
  const { unreadCounts, getUnreadCount } = useUnreadCounts();
  const { onlineUsers, onlineCount } = useOnlineStatus();

  const canCreateGroup = hasPermission(PERMISSIONS.FORUM_CREATE_GROUP);

  // Categorize groups
  const publicGroups =
    groups?.filter((group: any) => group.type === 'public') || [];
  const privateGroups =
    groups?.filter((group: any) => group.type === 'private') || [];
  const channels =
    groups?.filter((group: any) => group.type === 'channel') || [];

  const filteredGroups = (groupList: any[]) =>
    groupList.filter((group: any) =>
      group.name.toLowerCase().includes(searchTerm.toLowerCase())
    );

  const handleGroupClick = (groupId: string) => {
    setActiveGroup(groupId);
    router.push(`/forum/groups/${groupId}`);
  };

  const handleDirectMessageClick = () => {
    setActiveRecipient(null);
    router.push('/forum/direct-messages');
  };

  const handleGamesClick = () => {
    router.push('/forum/games');
  };

  const getGroupIcon = (type: string) => {
    switch (type) {
      case 'public':
        return <Globe className="h-4 w-4" />;
      case 'private':
        return <Lock className="h-4 w-4" />;
      case 'channel':
        return <Hash className="h-4 w-4" />;
      default:
        return <Users className="h-4 w-4" />;
    }
  };

  const GroupList = ({ groups, title }: { groups: any[]; title: string }) => (
    <div className="mb-4">
      <h3 className="text-sm font-medium text-muted-foreground mb-2 px-2">
        {title} ({groups.length})
      </h3>
      <div className="space-y-1">
        {filteredGroups(groups).map((group: any) => {
          const unreadCount = getUnreadCount(group.id);
          const isActive = activeGroupId === group.id;

          return (
            <Button
              key={group.id}
              variant={isActive ? 'secondary' : 'ghost'}
              className={cn(
                'w-full justify-start relative',
                isActive && 'bg-secondary'
              )}
              onClick={() => handleGroupClick(group.id)}
            >
              {getGroupIcon(group.type)}
              <span className="truncate ml-2 flex-1 text-left">
                {group.name}
              </span>
              <div className="flex items-center gap-1 ml-auto">
                {group.memberCount && (
                  <span className="text-xs text-muted-foreground">
                    {group.memberCount}
                  </span>
                )}
                {unreadCount > 0 && (
                  <Badge variant="destructive" className="h-5 w-5 p-0 text-xs">
                    {unreadCount > 99 ? '99+' : unreadCount}
                  </Badge>
                )}
              </div>
            </Button>
          );
        })}
      </div>
    </div>
  );

  return (
    <div className="w-80 bg-background border-r flex flex-col h-full">
      <div className="p-4 border-b">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold">Forum</h2>
          {canCreateGroup && (
            <Button
              size="sm"
              variant="outline"
              onClick={() => setIsCreateModalOpen(true)}
            >
              <PlusCircle className="h-4 w-4 mr-2" />
              New
            </Button>
          )}
        </div>

        <div className="relative mb-4">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search..."
            className="pl-8"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="groups" className="text-xs">
              Groups
            </TabsTrigger>
            <TabsTrigger value="dms" className="text-xs">
              DMs
            </TabsTrigger>
            <TabsTrigger value="games" className="text-xs">
              Games
            </TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      <div className="flex-1 overflow-y-auto">
        <Tabs value={activeTab} className="h-full">
          <TabsContent value="groups" className="p-2 h-full">
            {isLoading ? (
              <div className="text-center py-4 text-muted-foreground">
                Loading groups...
              </div>
            ) : (
              <div className="space-y-4">
                {channels.length > 0 && (
                  <GroupList groups={channels} title="Channels" />
                )}
                {publicGroups.length > 0 && (
                  <GroupList groups={publicGroups} title="Public Groups" />
                )}
                {privateGroups.length > 0 && (
                  <GroupList groups={privateGroups} title="Private Groups" />
                )}
                {groups?.length === 0 && (
                  <div className="text-center py-8 text-muted-foreground">
                    <Users className="h-12 w-12 mx-auto mb-2 opacity-50" />
                    <p>No groups yet</p>
                    <p className="text-sm">
                      Create your first group to get started
                    </p>
                  </div>
                )}
              </div>
            )}
          </TabsContent>

          <TabsContent value="dms" className="p-2 h-full">
            <div className="space-y-2">
              <Button
                variant="ghost"
                className="w-full justify-start"
                onClick={handleDirectMessageClick}
              >
                <MessageCircle className="h-4 w-4 mr-2" />
                All Direct Messages
              </Button>

              <div className="border-t pt-2">
                <h3 className="text-sm font-medium text-muted-foreground mb-2 px-2">
                  Online ({onlineCount})
                </h3>
                <div className="space-y-1">
                  {onlineUsers.slice(0, 10).map((user: any) => (
                    <Button
                      key={user.userId}
                      variant="ghost"
                      className="w-full justify-start"
                      onClick={() => {
                        setActiveRecipient(user.userId);
                        router.push(`/forum/direct-messages/${user.userId}`);
                      }}
                    >
                      <div className="relative">
                        <div className="h-6 w-6 rounded-full bg-primary/10 flex items-center justify-center mr-2">
                          <span className="text-xs font-medium">
                            {user.userName.charAt(0).toUpperCase()}
                          </span>
                        </div>
                        <div
                          className={cn(
                            'absolute -bottom-0.5 -right-0.5 h-2 w-2 rounded-full border border-background',
                            user.status === 'online' && 'bg-green-500',
                            user.status === 'away' && 'bg-yellow-500',
                            user.status === 'busy' && 'bg-red-500'
                          )}
                        />
                      </div>
                      <span className="truncate">{user.userName}</span>
                    </Button>
                  ))}
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="games" className="h-full overflow-y-auto">
            <GamesAndSurveys />
          </TabsContent>
        </Tabs>
      </div>

      <CreateGroupModal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
      />
    </div>
  );
};
