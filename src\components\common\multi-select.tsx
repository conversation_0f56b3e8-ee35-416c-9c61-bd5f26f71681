'use client';

import * as React from 'react';
import { X, ChevronDown, ChevronUp, Check } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { cn } from '@/lib/utils';

export type Option = {
  value: string;
  label: string;
  disabled?: boolean;
  [key: string]: any; // Allow for additional properties
};

export interface MultiSelectProps<T extends Record<string, any> = Option> {
  options: T[];
  selected: string[];
  onChange: (values: string[]) => void;
  placeholder?: string;
  emptyMessage?: string;
  className?: string;
  badgeClassName?: string;
  disabled?: boolean;
  maxDisplayItems?: number;
  valueField?: keyof T;
  labelField?: keyof T;
  renderOption?: (option: T, isSelected: boolean) => React.ReactNode;
}

export function MultiSelect<T extends Record<string, any> = Option>({
  options,
  selected,
  onChange,
  placeholder = 'Select options...',
  emptyMessage = 'No options found.',
  className,
  badgeClassName,
  disabled = false,
  maxDisplayItems,
  valueField = 'value' as keyof T,
  labelField = 'label' as keyof T,
  renderOption,
}: MultiSelectProps<T>) {
  const [open, setOpen] = React.useState(false);
  const [inputValue, setInputValue] = React.useState('');

  // Map options to internal format for consistent handling
  const normalizedOptions = React.useMemo(() => {
    return options.map((option) => ({
      originalOption: option,
      value: String(option[valueField]),
      label: String(option[labelField]),
      disabled: option.disabled,
    }));
  }, [options, valueField, labelField]);

  const handleUnselect = React.useCallback(
    (value: string) => {
      onChange(selected.filter((item) => item !== value));
    },
    [onChange, selected]
  );

  const handleKeyDown = React.useCallback(
    (e: React.KeyboardEvent<HTMLDivElement>) => {
      if (e.key === 'Backspace' && !inputValue && selected.length > 0) {
        handleUnselect(selected[selected.length - 1]);
      }
      // Close on escape
      if (e.key === 'Escape') {
        setOpen(false);
      }
    },
    [inputValue, selected, handleUnselect]
  );

  // Get display labels for selected values
  const selectedLabels = React.useMemo(() => {
    return selected.map((value) => {
      // Find the option with matching value, handling both string and number comparisons
      const option = normalizedOptions.find(
        (opt) =>
          opt.value === value ||
          opt.value === String(value) ||
          Number(opt.value) === Number(value)
      );
      return option ? option.label : value;
    });
  }, [selected, normalizedOptions]);

  // Display badges with limit if maxDisplayItems is set
  const displayBadges = React.useMemo(() => {
    if (!maxDisplayItems || selected.length <= maxDisplayItems) {
      return selectedLabels.map((label, i) => (
        <Badge
          key={selected[i]}
          variant="secondary"
          className={cn('mr-1 mb-1', badgeClassName)}
        >
          {label}
          <button
            className="ml-1 rounded-full outline-none ring-offset-background focus:ring-2 focus:ring-ring focus:ring-offset-2"
            onMouseDown={(e) => {
              e.preventDefault();
              e.stopPropagation();
            }}
            onClick={() => handleUnselect(selected[i])}
          >
            <X className="h-3 w-3" />
            <span className="sr-only">Remove {label}</span>
          </button>
        </Badge>
      ));
    }

    // Show limited items + count
    return (
      <>
        {selectedLabels.slice(0, maxDisplayItems).map((label, i) => (
          <Badge
            key={selected[i]}
            variant="secondary"
            className={cn('mr-1 mb-1', badgeClassName)}
          >
            {label}
            <button
              className="ml-1 rounded-full outline-none ring-offset-background focus:ring-2 focus:ring-ring focus:ring-offset-2"
              onMouseDown={(e) => {
                e.preventDefault();
                e.stopPropagation();
              }}
              onClick={() => handleUnselect(selected[i])}
            >
              <X className="h-3 w-3" />
              <span className="sr-only">Remove {label}</span>
            </button>
          </Badge>
        ))}
        <Badge variant="secondary" className={cn('mr-1 mb-1', badgeClassName)}>
          +{selected.length - maxDisplayItems} more
        </Badge>
      </>
    );
  }, [
    selected,
    selectedLabels,
    maxDisplayItems,
    handleUnselect,
    badgeClassName,
  ]);

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <div
          role="combobox"
          aria-expanded={open}
          className={cn(
            'flex min-h-10 w-full flex-wrap items-center rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background',
            'focus-within:outline-none focus-within:ring-2 focus-within:ring-ring focus-within:ring-offset-2',
            disabled && 'cursor-not-allowed opacity-50',
            className
          )}
          onClick={() => !disabled && setOpen(true)}
        >
          {selected.length > 0 ? (
            <div className="flex flex-wrap gap-1">{displayBadges}</div>
          ) : null}
          <div className="flex flex-1 items-center">
            {selected.length === 0 && (
              <span className="text-muted-foreground">{placeholder}</span>
            )}
            <div className="ml-auto flex">
              {open ? (
                <ChevronUp className="h-4 w-4 opacity-50" />
              ) : (
                <ChevronDown className="h-4 w-4 opacity-50" />
              )}
            </div>
          </div>
        </div>
      </PopoverTrigger>
      <PopoverContent className="p-0" align="start" sideOffset={5}>
        <Command shouldFilter={false}>
          <CommandInput
            placeholder="Search..."
            value={inputValue}
            onValueChange={setInputValue}
            onKeyDown={handleKeyDown}
            className="h-9"
          />
          <CommandList>
            <CommandEmpty>{emptyMessage}</CommandEmpty>
            <CommandGroup>
              {normalizedOptions
                .filter((option) =>
                  option.label.toLowerCase().includes(inputValue.toLowerCase())
                )
                .map((option) => {
                  // Check if the option value is in the selected array
                  // Handle both string and number comparisons
                  const isSelected = selected.some(
                    (selectedValue) =>
                      selectedValue === option.value ||
                      Number(selectedValue) === Number(option.value) ||
                      String(selectedValue) === option.value
                  );
                  return (
                    <CommandItem
                      key={option.value}
                      value={option.value}
                      disabled={option.disabled}
                      onSelect={() => {
                        const newSelected = isSelected
                          ? selected.filter(
                              (item) =>
                                !(
                                  item === option.value ||
                                  Number(item) === Number(option.value) ||
                                  String(item) === option.value
                                )
                            )
                          : [...selected, option.value];

                        onChange(newSelected);
                        setInputValue('');
                      }}
                    >
                      {renderOption ? (
                        renderOption(option.originalOption, isSelected)
                      ) : (
                        <>
                          <div className="mr-2 flex h-4 w-4 items-center justify-center">
                            {isSelected ? <Check className="h-4 w-4" /> : null}
                          </div>
                          {option.label}
                        </>
                      )}
                    </CommandItem>
                  );
                })}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}
